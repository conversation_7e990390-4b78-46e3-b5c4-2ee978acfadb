#!/usr/bin/env python3
"""
调试完整的包装器链
"""

import numpy as np
import embodied
from embodied.envs.carla_env import CarlaEnv

def test_full_wrapper_chain():
    """测试完整的包装器链"""
    print("测试完整的包装器链...")
    
    try:
        # 创建环境
        env = CarlaEnv(size=(64, 64), max_steps=10)
        print(f"原始动作空间: {env.act_space}")
        
        # 应用DreamerV3的包装器链
        print("\n应用包装器链...")
        
        # 1. NormalizeAction
        for name, space in env.act_space.items():
            if not space.discrete:
                print(f"应用 NormalizeAction 到 {name}")
                env = embodied.wrappers.NormalizeAction(env, name)
        print(f"NormalizeAction后动作空间: {env.act_space}")
        
        # 2. ClipAction
        for name, space in env.act_space.items():
            if not space.discrete:
                print(f"应用 ClipAction 到 {name}")
                env = embodied.wrappers.ClipAction(env, name)
        print(f"ClipAction后动作空间: {env.act_space}")
        
        # 3. UnifyDtypes
        print("应用 UnifyDtypes")
        env = embodied.wrappers.UnifyDtypes(env)
        print(f"UnifyDtypes后动作空间: {env.act_space}")
        
        # 4. CheckSpaces
        print("应用 CheckSpaces")
        env = embodied.wrappers.CheckSpaces(env)
        print(f"最终动作空间: {env.act_space}")
        
        # 重置环境
        reset_action = {'acceleration': np.float32(0.0), 'steer': np.float32(0.0), 'reset': True}
        obs = env.step(reset_action)
        print("✓ 环境重置成功")
        
        # 测试超出范围的动作
        problem_action = {
            'acceleration': np.float64(1.267778754234314), 
            'steer': np.float64(1.267778754234314), 
            'reset': False
        }
        
        print(f"\n测试问题动作: {problem_action}")
        print("动作类型:")
        for key, value in problem_action.items():
            if key != 'reset':
                print(f"  {key}: {type(value)}, dtype: {getattr(value, 'dtype', 'N/A')}, value: {value}")
        
        try:
            obs = env.step(problem_action)
            print("✓ 动作执行成功！")
        except Exception as e:
            print(f"❌ 动作执行失败: {e}")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("完整包装器链调试")
    print("=" * 50)
    test_full_wrapper_chain()
