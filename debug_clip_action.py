#!/usr/bin/env python3
"""
调试ClipAction包装器的行为
"""

import numpy as np
import embodied
from embodied.envs.carla_env import CarlaEnv

def test_clip_action():
    """测试ClipAction包装器"""
    print("测试ClipAction包装器...")
    
    try:
        # 创建环境
        env = CarlaEnv(size=(64, 64), max_steps=10)
        print(f"原始动作空间: {env.act_space}")
        
        # 应用ClipAction包装器
        clip_wrapper = embodied.wrappers.ClipAction(env, 'acceleration')
        print(f"ClipAction._key: {clip_wrapper._key}")
        print(f"ClipAction._low: {clip_wrapper._low}")
        print(f"ClipAction._high: {clip_wrapper._high}")
        
        # 测试一些动作值
        test_actions = [
            {'acceleration': 0.5, 'steer': 0.1, 'reset': False},
            {'acceleration': 1.5, 'steer': 0.1, 'reset': False},  # 超出范围
            {'acceleration': -1.5, 'steer': 0.1, 'reset': False}, # 超出范围
        ]
        
        for i, action in enumerate(test_actions):
            print(f"\n测试动作 {i+1}: {action}")
            
            # 模拟ClipAction的裁剪
            original_value = action['acceleration']
            clipped = np.clip(original_value, clip_wrapper._low, clip_wrapper._high)
            print(f"  原始值: {original_value}")
            print(f"  裁剪后: {clipped}")
            print(f"  类型: {type(clipped)}, dtype: {clipped.dtype}")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("ClipAction调试")
    print("=" * 50)
    test_clip_action()
