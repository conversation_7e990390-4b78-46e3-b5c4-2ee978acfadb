{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Alembic Importer", "Description": "Support importing Alembic files", "Category": "Importers", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "https://docs.unrealengine.com/en-US/WorkingWithContent/Importing/AlembicImporter/", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "AlembicImporter", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "AlembicLibrary", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}], "Plugins": [{"Name": "GeometryCache", "Enabled": true}]}