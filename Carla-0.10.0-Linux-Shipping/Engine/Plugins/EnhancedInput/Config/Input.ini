[/Script/EngineSettings.ConsoleSettings]
+ManualAutoCompleteList=(Command="ShowDebug EnhancedInput",Desc="Displays debug information about the current state of any Enhanced Input Mapping Contexts")
+ManualAutoCompleteList=(Command="ShowDebug WorldSubsystemInput",Desc="Displays debug information about the current state of any Enhanced Input Mapping Contexts applied to the Enhanced Input world subsystem.")
+ManualAutoCompleteList=(Command="ShowDebug InputSettings",Desc="Displays debug information any user input settings, such as player mappable keys.")
