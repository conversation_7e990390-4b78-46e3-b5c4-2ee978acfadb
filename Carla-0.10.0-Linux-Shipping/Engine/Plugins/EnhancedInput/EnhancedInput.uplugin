{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Enhanced Input", "Description": "Input handling that allows for contextual and dynamic mappings.", "Category": "Input", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "https://docs.unrealengine.com/en-US/enhanced-input-in-unreal-engine/", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "EnhancedInput", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "InputBlueprintNodes", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "InputEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "DataValidation", "Enabled": true}]}