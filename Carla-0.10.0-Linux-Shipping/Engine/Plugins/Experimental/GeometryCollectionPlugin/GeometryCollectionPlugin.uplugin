{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "Geometry", "Description": "Adds Geometry Collection Container.", "Category": "Geometry", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": true, "Installed": false, "Modules": [{"Name": "GeometryCollectionEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "GeometryCollectionTracks", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "GeometryCollectionSequencer", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "GeometryCollectionNodes", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "GeometryCollectionDepNodes", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "ProceduralMeshComponent", "Enabled": true}, {"Name": "ChaosSolverPlugin", "Enabled": true}, {"Name": "Dataflow", "Enabled": true}, {"Name": "PlanarCut", "Enabled": true}, {"Name": "GeometryProcessing", "Enabled": true}, {"Name": "Fracture", "Enabled": true}]}