{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "Editor DataflowGraph", "Description": "Editor Dataflow Graph", "Category": "Editor", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": true, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "DataflowEditor", "Type": "Editor", "LoadingPhase": "PostDefault"}, {"Name": "DataflowAssetTools", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DataflowEnginePlugin", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DataflowNodes", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "BaseCharacterFXEditor", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "ProceduralMeshComponent", "Enabled": true}, {"Name": "GeometryProcessing", "Enabled": true}, {"Name": "MeshModelingToolsetExp", "Enabled": true}, {"Name": "ChaosCaching", "Enabled": true}, {"Name": "GeometryCache", "Enabled": true}]}