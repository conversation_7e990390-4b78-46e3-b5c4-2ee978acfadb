{"FileVersion": 1, "Version": 1, "VersionName": "1.0", "FriendlyName": "HoldoutComposite", "Description": "Plugin designed to facilitate alpha holdout compositing primitives after post-processing.", "Category": "Compositing", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "HoldoutComposite", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}]}