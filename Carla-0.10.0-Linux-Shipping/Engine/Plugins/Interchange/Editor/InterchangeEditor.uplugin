{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Interchange Editor", "Description": "The Interchange Editor plugin exposes the Interchange import framework and pipelines to Unreal Editor.", "Category": "Importers", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"], "Modules": [{"Name": "InterchangeEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetAllowList": ["Editor", "Program"]}, {"Name": "InterchangeEditorPipelines", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetAllowList": ["Editor", "Program"]}, {"Name": "InterchangeEditorUtilities", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "TargetAllowList": ["Editor", "Program"]}], "Plugins": [{"Name": "Interchange", "Enabled": true, "TargetAllowList": ["Editor", "Program"]}, {"Name": "USDCore", "Enabled": true}], "IsExperimentalVersion": false}