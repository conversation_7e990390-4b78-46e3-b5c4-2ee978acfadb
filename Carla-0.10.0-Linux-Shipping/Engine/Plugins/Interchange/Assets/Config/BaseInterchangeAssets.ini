[CoreRedirects]

+PackageRedirects=(OldName="/Interchange/Functions/MF_Iridescence.MF_Iridescence", NewName="/InterchangeAssets/Functions/MF_Iridescence.MF_Iridescence")
+PackageRedirects=(OldName="/Interchange/Functions/MF_OrenNayerView.MF_OrenNayerView", NewName="/InterchangeAssets/Functions/MF_OrenNayerView.MF_OrenNayerView")
+PackageRedirects=(OldName="/Interchange/Functions/MF_PhongToMetalRoughness.MF_PhongToMetalRoughness", NewName="/InterchangeAssets/Functions/MF_PhongToMetalRoughness.MF_PhongToMetalRoughness")
+PackageRedirects=(OldName="/Interchange/Functions/MF_SchlickApprox.MF_SchlickApprox", NewName="/InterchangeAssets/Functions/MF_SchlickApprox.MF_SchlickApprox")
+PackageRedirects=(OldName="/Interchange/Functions/MX_AbsorptionVDF.MX_AbsorptionVDF", NewName="/InterchangeAssets/Functions/MX_AbsorptionVDF.MX_AbsorptionVDF")

+PackageRedirects=(OldName="/Interchange/Functions/MX_AnisotropicVDF.MX_AnisotropicVDF", NewName="/InterchangeAssets/Functions/MX_AnisotropicVDF.MX_AnisotropicVDF")
+PackageRedirects=(OldName="/Interchange/Functions/MX_Artistic_IOR.MX_Artistic_IOR", NewName="/InterchangeAssets/Functions/MX_Artistic_IOR.MX_Artistic_IOR")
+PackageRedirects=(OldName="/Interchange/Functions/MX_BurleyDiffuseBSDF.MX_BurleyDiffuseBSDF", NewName="/InterchangeAssets/Functions/MX_BurleyDiffuseBSDF.MX_BurleyDiffuseBSDF")
+PackageRedirects=(OldName="/Interchange/Functions/MX_ConductorBSDF.MX_ConductorBSDF", NewName="/InterchangeAssets/Functions/MX_ConductorBSDF.MX_ConductorBSDF")
+PackageRedirects=(OldName="/Interchange/Functions/MX_ConicalEDF.MX_ConicalEDF", NewName="/InterchangeAssets/Functions/MX_ConicalEDF.MX_ConicalEDF")

+PackageRedirects=(OldName="/Interchange/Functions/MX_DielectricBSDF.MX_DielectricBSDF", NewName="/InterchangeAssets/Functions/MX_DielectricBSDF.MX_DielectricBSDF")
+PackageRedirects=(OldName="/Interchange/Functions/MX_GeneralizedSchlickBSDF.MX_GeneralizedSchlickBSDF", NewName="/InterchangeAssets/Functions/MX_GeneralizedSchlickBSDF.MX_GeneralizedSchlickBSDF")
+PackageRedirects=(OldName="/Interchange/Functions/MX_MeasuredEDF.MX_MeasuredEDF", NewName="/InterchangeAssets/Functions/MX_MeasuredEDF.MX_MeasuredEDF")
+PackageRedirects=(OldName="/Interchange/Functions/MX_OpenPBR_Opaque.MX_OpenPBR_Opaque", NewName="/InterchangeAssets/Functions/MX_OpenPBR_Opaque.MX_OpenPBR_Opaque")
+PackageRedirects=(OldName="/Interchange/Functions/MX_OpenPBR_Translucent.MX_OpenPBR_Translucent", NewName="/InterchangeAssets/Functions/MX_OpenPBR_Translucent.MX_OpenPBR_Translucent")

+PackageRedirects=(OldName="/Interchange/Functions/MX_OrenNayarBSDF.MX_OrenNayarBSDF", NewName="/InterchangeAssets/Functions/MX_OrenNayarBSDF.MX_OrenNayarBSDF")
+PackageRedirects=(OldName="/Interchange/Functions/MX_Place2D.MX_Place2D", NewName="/InterchangeAssets/Functions/MX_Place2D.MX_Place2D")
+PackageRedirects=(OldName="/Interchange/Functions/MX_Roughness_Anisotropy.MX_Roughness_Anisotropy", NewName="/InterchangeAssets/Functions/MX_Roughness_Anisotropy.MX_Roughness_Anisotropy")
+PackageRedirects=(OldName="/Interchange/Functions/MX_Roughness_Dual.MX_Roughness_Dual", NewName="/InterchangeAssets/Functions/MX_Roughness_Dual.MX_Roughness_Dual")
+PackageRedirects=(OldName="/Interchange/Functions/MX_SheenBSDF.MX_SheenBSDF", NewName="/InterchangeAssets/Functions/MX_SheenBSDF.MX_SheenBSDF")

+PackageRedirects=(OldName="/Interchange/Functions/MX_StandardSurface.MX_StandardSurface", NewName="/InterchangeAssets/Functions/MX_StandardSurface.MX_StandardSurface")
+PackageRedirects=(OldName="/Interchange/Functions/MX_SubsurfaceBSDF.MX_SubsurfaceBSDF", NewName="/InterchangeAssets/Functions/MX_SubsurfaceBSDF.MX_SubsurfaceBSDF")
+PackageRedirects=(OldName="/Interchange/Functions/MX_Surface.MX_Surface", NewName="/InterchangeAssets/Functions/MX_Surface.MX_Surface")
+PackageRedirects=(OldName="/Interchange/Functions/MX_SurfaceUnlit.MX_SurfaceUnlit", NewName="/InterchangeAssets/Functions/MX_SurfaceUnlit.MX_SurfaceUnlit")
+PackageRedirects=(OldName="/Interchange/Functions/MX_ThinFilmBSDF.MX_ThinFilmBSDF", NewName="/InterchangeAssets/Functions/MX_ThinFilmBSDF.MX_ThinFilmBSDF")

+PackageRedirects=(OldName="/Interchange/Functions/MX_TranslucentBSDF.MX_TranslucentBSDF", NewName="/InterchangeAssets/Functions/MX_TranslucentBSDF.MX_TranslucentBSDF")
+PackageRedirects=(OldName="/Interchange/Functions/MX_Transmission.MX_Transmission", NewName="/InterchangeAssets/Functions/MX_Transmission.MX_Transmission")
+PackageRedirects=(OldName="/Interchange/Functions/MX_TransmissionSurface.MX_TransmissionSurface", NewName="/InterchangeAssets/Functions/MX_TransmissionSurface.MX_TransmissionSurface")
+PackageRedirects=(OldName="/Interchange/Functions/MX_UniformEDF.MX_UniformEDF", NewName="/InterchangeAssets/Functions/MX_UniformEDF.MX_UniformEDF")
+PackageRedirects=(OldName="/Interchange/Functions/MX_UsdPreviewSurface.MX_UsdPreviewSurface", NewName="/InterchangeAssets/Functions/MX_UsdPreviewSurface.MX_UsdPreviewSurface")


+PackageRedirects=(OldName="/Interchange/gltf/M_ClearCoat.M_ClearCoat", NewName="/InterchangeAssets/gltf/M_ClearCoat.M_ClearCoat")
+PackageRedirects=(OldName="/Interchange/gltf/M_Default.M_Default", NewName="/InterchangeAssets/gltf/M_Default.M_Default")
+PackageRedirects=(OldName="/Interchange/gltf/M_Sheen.M_Sheen", NewName="/InterchangeAssets/gltf/M_Sheen.M_Sheen")
+PackageRedirects=(OldName="/Interchange/gltf/M_SpecularGlossiness.M_SpecularGlossiness", NewName="/InterchangeAssets/gltf/M_SpecularGlossiness.M_SpecularGlossiness")
+PackageRedirects=(OldName="/Interchange/gltf/M_Transmission.M_Transmission", NewName="/InterchangeAssets/gltf/M_Transmission.M_Transmission")

+PackageRedirects=(OldName="/Interchange/gltf/M_Unlit.M_Unlit", NewName="/InterchangeAssets/gltf/M_Unlit.M_Unlit")
+PackageRedirects=(OldName="/Interchange/gltf/PerceivedBrightness.PerceivedBrightness", NewName="/InterchangeAssets/gltf/PerceivedBrightness.PerceivedBrightness")
+PackageRedirects=(OldName="/Interchange/gltf/SpecGlossToMetalRoughness.SpecGlossToMetalRoughness", NewName="/InterchangeAssets/gltf/SpecGlossToMetalRoughness.SpecGlossToMetalRoughness")


+PackageRedirects=(OldName="/Interchange/gltf/MaterialBodies/MF_ClearCoat_Body.MF_ClearCoat_Body", NewName="/InterchangeAssets/gltf/MaterialBodies/MF_ClearCoat_Body.MF_ClearCoat_Body")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialBodies/MF_Default_Body.MF_Default_Body", NewName="/InterchangeAssets/gltf/MaterialBodies/MF_Default_Body.MF_Default_Body")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialBodies/MF_Sheen_Body.MF_Sheen_Body", NewName="/InterchangeAssets/gltf/MaterialBodies/MF_Sheen_Body.MF_Sheen_Body")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialBodies/MF_SpecularGlossiness_Body.MF_SpecularGlossiness_Body", NewName="/InterchangeAssets/gltf/MaterialBodies/MF_SpecularGlossiness_Body.MF_SpecularGlossiness_Body")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialBodies/MF_Transmission_Body.MF_Transmission_Body", NewName="/InterchangeAssets/gltf/MaterialBodies/MF_Transmission_Body.MF_Transmission_Body")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialBodies/MF_Unlit_Body.MF_Unlit_Body", NewName="/InterchangeAssets/gltf/MaterialBodies/MF_Unlit_Body.MF_Unlit_Body")


+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_BaseColor.MF_BaseColor", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_BaseColor.MF_BaseColor")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_Clearcoat.MF_Clearcoat", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_Clearcoat.MF_Clearcoat")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_DiffuseSpecGloss.MF_DiffuseSpecGloss", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_DiffuseSpecGloss.MF_DiffuseSpecGloss")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_Emissive.MF_Emissive", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_Emissive.MF_Emissive")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_Fresnel_DS.MF_Fresnel_DS", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_Fresnel_DS.MF_Fresnel_DS")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_IOR.MF_IOR", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_IOR.MF_IOR")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_Iridescence.MF_Iridescence", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_Iridescence.MF_Iridescence")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_MaxComponentValue.MF_MaxComponentValue", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_MaxComponentValue.MF_MaxComponentValue")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_MetallicRoughness.MF_MetallicRoughness", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_MetallicRoughness.MF_MetallicRoughness")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_Normals.MF_Normals", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_Normals.MF_Normals")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_Occlusion.MF_Occlusion", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_Occlusion.MF_Occlusion")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_PerceivedBrightness.MF_PerceivedBrightness", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_PerceivedBrightness.MF_PerceivedBrightness")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_RotateNormals_TS.MF_RotateNormals_TS", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_RotateNormals_TS.MF_RotateNormals_TS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_RotateV2.MF_RotateV2", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_RotateV2.MF_RotateV2")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_ScaleNormal.MF_ScaleNormal", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_ScaleNormal.MF_ScaleNormal")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_Sheen.MF_Sheen", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_Sheen.MF_Sheen")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_SpecGlossToMetalRoughness.MF_SpecGlossToMetalRoughness", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_SpecGlossToMetalRoughness.MF_SpecGlossToMetalRoughness")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_Specular.MF_Specular", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_Specular.MF_Specular")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_SpecularAnisotropy.MF_SpecularAnisotropy", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_SpecularAnisotropy.MF_SpecularAnisotropy")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_Temporal_Blur.MF_Temporal_Blur", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_Temporal_Blur.MF_Temporal_Blur")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_TransformUVs.MF_TransformUVs", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_TransformUVs.MF_TransformUVs")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_Transmission.MF_Transmission", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_Transmission.MF_Transmission")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialFunctions/MF_TransmissionOpacity.MF_TransmissionOpacity", NewName="/InterchangeAssets/gltf/MaterialFunctions/MF_TransmissionOpacity.MF_TransmissionOpacity")


+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_ClearCoat_Blend.MI_ClearCoat_Blend", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_ClearCoat_Blend.MI_ClearCoat_Blend")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_ClearCoat_Blend_DS.MI_ClearCoat_Blend_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_ClearCoat_Blend_DS.MI_ClearCoat_Blend_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_ClearCoat_Mask.MI_ClearCoat_Mask", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_ClearCoat_Mask.MI_ClearCoat_Mask")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_ClearCoat_Mask_DS.MI_ClearCoat_Mask_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_ClearCoat_Mask_DS.MI_ClearCoat_Mask_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_ClearCoat_Opaque.MI_ClearCoat_Opaque", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_ClearCoat_Opaque.MI_ClearCoat_Opaque")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_ClearCoat_Opaque_DS.MI_ClearCoat_Opaque_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_ClearCoat_Opaque_DS.MI_ClearCoat_Opaque_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Default_Blend.MI_Default_Blend", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Default_Blend.MI_Default_Blend")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Default_Blend_DS.MI_Default_Blend_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Default_Blend_DS.MI_Default_Blend_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Default_Mask.MI_Default_Mask", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Default_Mask.MI_Default_Mask")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Default_Mask_DS.MI_Default_Mask_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Default_Mask_DS.MI_Default_Mask_DS")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Default_Opaque.MI_Default_Opaque", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Default_Opaque.MI_Default_Opaque")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS.MI_Default_Opaque_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Default_Opaque_DS.MI_Default_Opaque_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Sheen_Blend.MI_Sheen_Blend", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Sheen_Blend.MI_Sheen_Blend")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Sheen_Blend_DS.MI_Sheen_Blend_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Sheen_Blend_DS.MI_Sheen_Blend_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Sheen_Mask.MI_Sheen_Mask", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Sheen_Mask.MI_Sheen_Mask")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Sheen_Mask_DS.MI_Sheen_Mask_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Sheen_Mask_DS.MI_Sheen_Mask_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Sheen_Opaque.MI_Sheen_Opaque", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Sheen_Opaque.MI_Sheen_Opaque")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Sheen_Opaque_DS.MI_Sheen_Opaque_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Sheen_Opaque_DS.MI_Sheen_Opaque_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_SpecularGlossiness_Blend.MI_SpecularGlossiness_Blend", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_SpecularGlossiness_Blend.MI_SpecularGlossiness_Blend")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_SpecularGlossiness_Blend_DS.MI_SpecularGlossiness_Blend_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_SpecularGlossiness_Blend_DS.MI_SpecularGlossiness_Blend_DS")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_SpecularGlossiness_Mask.MI_SpecularGlossiness_Mask", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_SpecularGlossiness_Mask.MI_SpecularGlossiness_Mask")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_SpecularGlossiness_Mask_DS.MI_SpecularGlossiness_Mask_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_SpecularGlossiness_Mask_DS.MI_SpecularGlossiness_Mask_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_SpecularGlossiness_Opaque.MI_SpecularGlossiness_Opaque", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_SpecularGlossiness_Opaque.MI_SpecularGlossiness_Opaque")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_SpecularGlossiness_Opaque_DS.MI_SpecularGlossiness_Opaque_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_SpecularGlossiness_Opaque_DS.MI_SpecularGlossiness_Opaque_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Transmission.MI_Transmission", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Transmission.MI_Transmission")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Transmission_DS.MI_Transmission_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Transmission_DS.MI_Transmission_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Unlit_Blend.MI_Unlit_Blend", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Unlit_Blend.MI_Unlit_Blend")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Unlit_Blend_DS.MI_Unlit_Blend_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Unlit_Blend_DS.MI_Unlit_Blend_DS")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Unlit_Mask.MI_Unlit_Mask", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Unlit_Mask.MI_Unlit_Mask")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Unlit_Mask_DS.MI_Unlit_Mask_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Unlit_Mask_DS.MI_Unlit_Mask_DS")

+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Unlit_Opaque.MI_Unlit_Opaque", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Unlit_Opaque.MI_Unlit_Opaque")
+PackageRedirects=(OldName="/Interchange/gltf/MaterialInstances/MI_Unlit_Opaque_DS.MI_Unlit_Opaque_DS", NewName="/InterchangeAssets/gltf/MaterialInstances/MI_Unlit_Opaque_DS.MI_Unlit_Opaque_DS")


+PackageRedirects=(OldName="/Interchange/gltf/Textures/T_Anisotropy_Direction_Linear.T_Anisotropy_Direction_Linear", NewName="/InterchangeAssets/gltf/Textures/T_Anisotropy_Direction_Linear.T_Anisotropy_Direction_Linear")
+PackageRedirects=(OldName="/Interchange/gltf/Textures/T_Black_srgb.T_Black_srgb", NewName="/InterchangeAssets/gltf/Textures/T_Black_srgb.T_Black_srgb")
+PackageRedirects=(OldName="/Interchange/gltf/Textures/T_Generic_N.T_Generic_N", NewName="/InterchangeAssets/gltf/Textures/T_Generic_N.T_Generic_N")
+PackageRedirects=(OldName="/Interchange/gltf/Textures/T_Gray_Linear.T_Gray_Linear", NewName="/InterchangeAssets/gltf/Textures/T_Gray_Linear.T_Gray_Linear")
+PackageRedirects=(OldName="/Interchange/gltf/Textures/T_White_Linear.T_White_Linear", NewName="/InterchangeAssets/gltf/Textures/T_White_Linear.T_White_Linear")

+PackageRedirects=(OldName="/Interchange/gltf/Textures/T_White_srgb.T_White_srgb", NewName="/InterchangeAssets/gltf/Textures/T_White_srgb.T_White_srgb")


+PackageRedirects=(OldName="/Interchange/Materials/ClearCoatMaterial_MR.ClearCoatMaterial_MR", NewName="/InterchangeAssets/Materials/ClearCoatMaterial_MR.ClearCoatMaterial_MR")
+PackageRedirects=(OldName="/Interchange/Materials/ClearCoatMaterial_SG.ClearCoatMaterial_SG", NewName="/InterchangeAssets/Materials/ClearCoatMaterial_SG.ClearCoatMaterial_SG")
+PackageRedirects=(OldName="/Interchange/Materials/CommonMaterial.CommonMaterial", NewName="/InterchangeAssets/Materials/CommonMaterial.CommonMaterial")
+PackageRedirects=(OldName="/Interchange/Materials/DecalMaterial.DecalMaterial", NewName="/InterchangeAssets/Materials/DecalMaterial.DecalMaterial")
+PackageRedirects=(OldName="/Interchange/Materials/LambertSurfaceMaterial.LambertSurfaceMaterial", NewName="/InterchangeAssets/Materials/LambertSurfaceMaterial.LambertSurfaceMaterial")

+PackageRedirects=(OldName="/Interchange/Materials/MapOrColorFunction.MapOrColorFunction", NewName="/InterchangeAssets/Materials/MapOrColorFunction.MapOrColorFunction")
+PackageRedirects=(OldName="/Interchange/Materials/MapOrLinearColorFunction.MapOrLinearColorFunction", NewName="/InterchangeAssets/Materials/MapOrLinearColorFunction.MapOrLinearColorFunction")
+PackageRedirects=(OldName="/Interchange/Materials/MapOrScalarFunction.MapOrScalarFunction", NewName="/InterchangeAssets/Materials/MapOrScalarFunction.MapOrScalarFunction")
+PackageRedirects=(OldName="/Interchange/Materials/OrenNayerMaterial.OrenNayerMaterial", NewName="/InterchangeAssets/Materials/OrenNayerMaterial.OrenNayerMaterial")
+PackageRedirects=(OldName="/Interchange/Materials/PBRSurfaceFunction_MR.PBRSurfaceFunction_MR", NewName="/InterchangeAssets/Materials/PBRSurfaceFunction_MR.PBRSurfaceFunction_MR")

+PackageRedirects=(OldName="/Interchange/Materials/PBRSurfaceFunction_SG.PBRSurfaceFunction_SG", NewName="/InterchangeAssets/Materials/PBRSurfaceFunction_SG.PBRSurfaceFunction_SG")
+PackageRedirects=(OldName="/Interchange/Materials/PBRSurfaceMaterial_MRPBRSurfaceMaterial_MR", NewName="/InterchangeAssets/Materials/PBRSurfaceMaterial_MR.PBRSurfaceMaterial_MR")
+PackageRedirects=(OldName="/Interchange/Materials/PBRSurfaceMaterial_SG.PBRSurfaceMaterial_SG", NewName="/InterchangeAssets/Materials/PBRSurfaceMaterial_SG.PBRSurfaceMaterial_SG")
+PackageRedirects=(OldName="/Interchange/Materials/PhongSurfaceMaterial.PhongSurfaceMaterial", NewName="/InterchangeAssets/Materials/PhongSurfaceMaterial.PhongSurfaceMaterial")
+PackageRedirects=(OldName="/Interchange/Materials/SheenMaterial_MR.SheenMaterial_MR", NewName="/InterchangeAssets/Materials/SheenMaterial_MR.SheenMaterial_MR")

+PackageRedirects=(OldName="/Interchange/Materials/SheenMaterial_SG.SheenMaterial_SG", NewName="/InterchangeAssets/Materials/SheenMaterial_SG.SheenMaterial_SG")
+PackageRedirects=(OldName="/Interchange/Materials/SubsurfaceMaterial_MR.SubsurfaceMaterial_MR", NewName="/InterchangeAssets/Materials/SubsurfaceMaterial_MR.SubsurfaceMaterial_MR")
+PackageRedirects=(OldName="/Interchange/Materials/SubsurfaceMaterial_SG.SubsurfaceMaterial_SG", NewName="/InterchangeAssets/Materials/SubsurfaceMaterial_SG.SubsurfaceMaterial_SG")
+PackageRedirects=(OldName="/Interchange/Materials/ThinTranslucentMaterial_MR.ThinTranslucentMaterial_MR", NewName="/InterchangeAssets/Materials/ThinTranslucentMaterial_MR.ThinTranslucentMaterial_MR")
+PackageRedirects=(OldName="/Interchange/Materials/ThinTranslucentMaterial_SG.ThinTranslucentMaterial_SG", NewName="/InterchangeAssets/Materials/ThinTranslucentMaterial_SG.ThinTranslucentMaterial_SG")

+PackageRedirects=(OldName="/Interchange/Materials/UnlitMaterial.UnlitMaterial", NewName="/InterchangeAssets/Materials/UnlitMaterial.UnlitMaterial")


+PackageRedirects=(OldName="/Interchange/Substrate/MX_AbsorptionVDF.MX_AbsorptionVDF", NewName="/InterchangeAssets/Substrate/MX_AbsorptionVDF.MX_AbsorptionVDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_AnisotropicVDF.MX_AnisotropicVDF", NewName="/InterchangeAssets/Substrate/MX_AnisotropicVDF.MX_AnisotropicVDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_BurleyDiffuseBSDF.MX_BurleyDiffuseBSDF", NewName="/InterchangeAssets/Substrate/MX_BurleyDiffuseBSDF.MX_BurleyDiffuseBSDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_ConductorBSDF.MX_ConductorBSDF", NewName="/InterchangeAssets/Substrate/MX_ConductorBSDF.MX_ConductorBSDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_ConicalEDF.MX_ConicalEDF", NewName="/InterchangeAssets/Substrate/MX_ConicalEDF.MX_ConicalEDF")

+PackageRedirects=(OldName="/Interchange/Substrate/MX_DielectricBSDF.MX_DielectricBSDF", NewName="/InterchangeAssets/Substrate/MX_DielectricBSDF.MX_DielectricBSDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_GeneralizedSchlickBSDF.MX_GeneralizedSchlickBSDF", NewName="/InterchangeAssets/Substrate/MX_GeneralizedSchlickBSDF.MX_GeneralizedSchlickBSDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_MeasuredEDF.MX_MeasuredEDF", NewName="/InterchangeAssets/Substrate/MX_MeasuredEDF.MX_MeasuredEDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_OrenNayarBSDF.MX_OrenNayarBSDF", NewName="/InterchangeAssets/Substrate/MX_OrenNayarBSDF.MX_OrenNayarBSDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_SheenBSDF.MX_SheenBSDF", NewName="/InterchangeAssets/Substrate/MX_SheenBSDF.MX_SheenBSDF")

+PackageRedirects=(OldName="/Interchange/Substrate/MX_SubsurfaceBSDF.MX_SubsurfaceBSDF", NewName="/InterchangeAssets/Substrate/MX_SubsurfaceBSDF.MX_SubsurfaceBSDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_Surface.MX_Surface", NewName="/InterchangeAssets/Substrate/MX_Surface.MX_Surface")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_SurfaceUnlit.MX_SurfaceUnlit", NewName="/InterchangeAssets/Substrate/MX_SurfaceUnlit.MX_SurfaceUnlit")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_ThinFilmBSDF.MX_ThinFilmBSDF", NewName="/InterchangeAssets/Substrate/MX_ThinFilmBSDF.MX_ThinFilmBSDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_TranslucentBSDF.MX_TranslucentBSDF", NewName="/InterchangeAssets/Substrate/MX_TranslucentBSDF.MX_TranslucentBSDF")

+PackageRedirects=(OldName="/Interchange/Substrate/MX_UniformEDF.MX_UniformEDF", NewName="/InterchangeAssets/Substrate/MX_UniformEDF.MX_UniformEDF")
+PackageRedirects=(OldName="/Interchange/Substrate/MX_UsdPreviewSurface.MX_UsdPreviewSurface", NewName="/InterchangeAssets/Substrate/MX_UsdPreviewSurface.MX_UsdPreviewSurface")


+PackageRedirects=(OldName="/Interchange/Utilities/New_LUT.New_LUT", NewName="/InterchangeAssets/Utilities/New_LUT.New_LUT")
+PackageRedirects=(OldName="/Interchange/Utilities/T_Bayer64_Grayscale_64px.T_Bayer64_Grayscale_64px", NewName="/InterchangeAssets/Utilities/T_Bayer64_Grayscale_64px.T_Bayer64_Grayscale_64px")
