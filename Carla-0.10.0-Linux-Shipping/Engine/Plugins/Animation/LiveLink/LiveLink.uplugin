{"FileVersion": 3, "Version": 1, "VersionName": "2.0", "FriendlyName": "Live Link", "Description": "LiveLink allows streaming of animated data into Unreal Engine", "Category": "Animation", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "SupportedPrograms": ["LiveLinkHub"], "Plugins": [{"Name": "Takes", "Enabled": true}, {"Name": "ConcertSyncClient", "Enabled": true, "Optional": true}, {"Name": "ContentBrowserAssetDataSource", "Enabled": true}], "Modules": [{"Name": "LiveLink", "Type": "Runtime", "ProgramAllowList": ["LiveLinkHub"], "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "LiveLinkComponents", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "LiveLinkEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "ProgramAllowList": ["LiveLinkHub"]}, {"Name": "LiveLinkGraphNode", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "LiveLinkMovieScene", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "LiveLinkSequencer", "Type": "UncookedOnly", "ProgramAllowList": ["LiveLinkHub"], "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "LiveLinkMultiUser", "Type": "UncookedOnly", "LoadingPhase": "PostEngineInit"}]}