[CommonSettings]
SourcePath=Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils
DestinationPath=Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils
ManifestName=OnlineSubsystemUtils.manifest
ArchiveName=OnlineSubsystemUtils.archive
PortableObjectName=OnlineSubsystemUtils.po
ResourceName=OnlineSubsystemUtils.locres
NativeCulture=en
CulturesToGenerate=en
CulturesToGenerate=fr
CulturesToGenerate=de
CulturesToGenerate=es
CulturesToGenerate=es-419
CulturesToGenerate=it
CulturesToGenerate=ja
CulturesToGenerate=ko
CulturesToGenerate=ru
CulturesToGenerate=zh-Hans
CulturesToGenerate=ar
CulturesToGenerate=pl
CulturesToGenerate=pt-BR

[GatherTextStep0]
CommandletClass=GatherTextFromSource
SearchDirectoryPaths=Plugins/Online/OnlineSubsystemUtils/Source/
FileNameFilters=*.cpp
FileNameFilters=*.h
FileNameFilters=*.c
FileNameFilters=*.inl
FileNameFilters=*.mm
FileNameFilters=*.ini
ShouldGatherFromEditorOnlyData=false

[GatherTextStep1]
CommandletClass=GenerateGatherManifest

[GatherTextStep2]
CommandletClass=GenerateGatherArchive

[GatherTextStep3]
CommandletClass=InternationalizationExport
bImportLoc=true

[GatherTextStep4]
CommandletClass=GenerateTextLocalizationResource

[GatherTextStep5]
CommandletClass=InternationalizationExport
bExportLoc=true
