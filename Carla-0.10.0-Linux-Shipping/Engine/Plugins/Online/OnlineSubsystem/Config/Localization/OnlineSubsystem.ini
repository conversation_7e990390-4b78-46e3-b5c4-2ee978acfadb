[CommonSettings]
SourcePath=Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem
DestinationPath=Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem
ManifestName=OnlineSubsystem.manifest
ArchiveName=OnlineSubsystem.archive
PortableObjectName=OnlineSubsystem.po
ResourceName=OnlineSubsystem.locres
NativeCulture=en
CulturesToGenerate=en
CulturesToGenerate=fr
CulturesToGenerate=de
CulturesToGenerate=es
CulturesToGenerate=es-419
CulturesToGenerate=it
CulturesToGenerate=ja
CulturesToGenerate=ko
CulturesToGenerate=ru
CulturesToGenerate=zh-Hans
CulturesToGenerate=ar
CulturesToGenerate=pl
CulturesToGenerate=pt-BR

[GatherTextStep0]
CommandletClass=GatherTextFromSource
SearchDirectoryPaths=Plugins/Online/OnlineSubsystem/Source/
FileNameFilters=*.cpp
FileNameFilters=*.h
FileNameFilters=*.c
FileNameFilters=*.inl
FileNameFilters=*.mm
FileNameFilters=*.ini
ShouldGatherFromEditorOnlyData=false

[GatherTextStep1]
CommandletClass=GenerateGatherManifest

[GatherTextStep2]
CommandletClass=GenerateGatherArchive

[GatherTextStep3]
CommandletClass=InternationalizationExport
bImportLoc=true

[GatherTextStep4]
CommandletClass=GenerateTextLocalizationResource

[GatherTextStep5]
CommandletClass=InternationalizationExport
bExportLoc=true
