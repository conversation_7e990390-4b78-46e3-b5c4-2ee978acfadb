[CoreRedirects]

+ClassRedirects=(OldName="/Script/EditorScriptingUtilities.BlutilityActor",NewName="/Script/Blutility.PlacedEditorUtilityBase")
+StructRedirects=(OldName="/Script/EditorScriptingUtilities.EditorScriptingMeshReductionSettings",NewName="/Script/StaticMeshEditor.StaticMeshReductionSettings")
+StructRedirects=(OldName="/Script/EditorScriptingUtilities.EditorScriptingMeshReductionOptions",NewName="/Script/StaticMeshEditor.StaticMeshReductionOptions")
+StructRedirects=(OldName="/Script/EditorScriptingUtilities.EditorScriptingJoinStaticMeshActorsOptions",NewName="/Script/StaticMeshEditor.JoinStaticMeshActorsOptions")
+StructRedirects=(OldName="/Script/EditorScriptingUtilities.EditorScriptingMergeStaticMeshActorsOptions",NewName="/Script/StaticMeshEditor.MergeStaticMeshActorsOptions")
+StructRedirects=(OldName="/Script/EditorScriptingUtilities.EditorScriptingCreateProxyMeshActorOptions",NewName="/Script/StaticMeshEditor.CreateProxyMeshActorOptions")
+EnumRedirects=(OldName="/Script/EditorScriptingUtilities.EScriptingCollisionShapeType",NewName="/Script/StaticMeshEditor.EScriptCollisionShapeType")
