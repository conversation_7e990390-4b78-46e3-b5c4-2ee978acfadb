{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Wave Tables", "Description": "Default implementation of WaveTable support within the Unreal Audio Engine.", "Category": "Audio", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": true, "IsExperimentalVersion": false, "Installed": false, "Modules": [{"Name": "WaveTable", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "WaveTableEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit"}], "Plugins": []}