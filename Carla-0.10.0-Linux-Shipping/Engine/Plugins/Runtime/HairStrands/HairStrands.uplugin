{"FileVersion": 3, "Version": 2, "VersionName": "1.0", "FriendlyName": "Groom", "Description": "Rendering and simulation of grooms", "Category": "Geometry", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "HairStrandsCore", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}, {"Name": "HairStrandsDeformer", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformDenyList": ["Android", "IOS"]}, {"Name": "HairStrandsRuntime", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HairStrandsEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HairCardGeneratorFramework", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "Niagara", "Enabled": true}, {"Name": "GeometryCache", "Enabled": true}, {"Name": "DeformerGraph", "Enabled": true}]}