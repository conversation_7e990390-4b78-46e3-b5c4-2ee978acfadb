{"FileVersion": 1, "Version": 1, "VersionName": "1.0", "FriendlyName": "Niagara", "Description": "Niagara effect systems.", "Category": "FX", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": true, "IsBetaVersion": false, "Installed": false, "Plugins": [{"Name": "PythonScriptPlugin", "Enabled": true}], "Modules": [{"Name": "NiagaraCore", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "Niagara", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "NiagaraAnimNotifies", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "NiagaraShader", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}, {"Name": "NiagaraVertexFactories", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}, {"Name": "NiagaraBlueprintNodes", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "NiagaraEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "NiagaraEditorWidgets", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}]}