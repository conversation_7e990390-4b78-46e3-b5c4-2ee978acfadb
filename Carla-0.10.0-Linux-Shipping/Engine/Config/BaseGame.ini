
[SectionsToSave]
+Section=DebugWindows
+Section=/Script/DeveloperToolSettings.PlatformsMenuSettings

[Internationalization]
+LocalizationPaths=%GAMEDIR%Content/Localization/Game

[DefaultPlayer]
Name=Player

[/Script/Engine.GameNetworkManager]
TotalNetBandwidth=32000
MaxDynamicBandwidth=7000
MinDynamicBandwidth=4000
MoveRepSize=42.0f
MAXPOSITIONERRORSQUARED=3.0f
MAXNEARZEROVELOCITYSQUARED=9.0f
CLIENTADJUSTUPDATECOST=180.0f
MAXCLIENTUPDATEINTERVAL=0.25f
MaxClientForcedUpdateDuration=1.0f
ServerForcedUpdateHitchThreshold=0.150f
ServerForcedUpdateHitchCooldown=0.100f
MaxMoveDeltaTime=0.125f
MaxClientSmoothingDeltaTime=0.50f
ClientNetSendMoveDeltaTime=0.0166
ClientNetSendMoveDeltaTimeThrottled=0.0222
ClientNetSendMoveDeltaTimeStationary=0.0166
ClientNetSendMoveThrottleAtNetSpeed=10000
ClientNetSendMoveThrottleOverPlayerCount=10
ClientAuthorativePosition=false
ClientErrorUpdateRateLimit=0.0f
bMovementTimeDiscrepancyDetection=false
bMovementTimeDiscrepancyResolution=false
MovementTimeDiscrepancyMaxTimeMargin=0.25f
MovementTimeDiscrepancyMinTimeMargin=-0.25f
MovementTimeDiscrepancyResolutionRate=1.0f
MovementTimeDiscrepancyDriftAllowance=0.0f
bMovementTimeDiscrepancyForceCorrectionsDuringResolution=false
bUseDistanceBasedRelevancy=true

[/Script/Party.Party]
DefaultMaxPartySize=5

[/Script/Party.SocialSettings]
!SocialPlatformDescriptions=ClearArray
+SocialPlatformDescriptions=(Name="AND", PlatformType="MOBILE", CrossplayPool="MOBILE")
+SocialPlatformDescriptions=(Name="IOS", PlatformType="MOBILE", CrossplayPool="MOBILE")
+SocialPlatformDescriptions=(Name="LNX", PlatformType="DESKTOP", CrossplayPool="DESKTOP")
+SocialPlatformDescriptions=(Name="MAC", PlatformType="DESKTOP", CrossplayPool="DESKTOP")
+SocialPlatformDescriptions=(Name="WIN", PlatformType="DESKTOP", CrossplayPool="DESKTOP")
+SocialPlatformDescriptions=(Name="PSN", PlatformType="CONSOLE", OnlineSubsystem="PS4", ExternalAccountType="psn", SessionType="PS4", CrossplayPool="psn")
+SocialPlatformDescriptions=(Name="SWT", PlatformType="CONSOLE", OnlineSubsystem="SWITCH", ExternalAccountType="nintendo", CrossplayPool="nintendo")
+SocialPlatformDescriptions=(Name="XBL", PlatformType="CONSOLE", OnlineSubsystem="LIVE", ExternalAccountType="xbl", SessionType="MPSD", CrossplayPool="xbl")

[/Script/Lobby.LobbyBeaconState]
WaitForPlayersTimeRemaining=20.0

[/Script/Engine.GameSession]
MaxPlayers=16
MaxSpectators=2
MaxSplitscreensPerConnection=4
bRequiresPushToTalk=true

[/Script/EngineSettings.GeneralProjectSettings]
CompanyName=
CompanyDistinguishedName=
CopyrightNotice=Fill out your copyright notice in the Description page of Project Settings.
Description=
LicensingTerms=
PrivacyPolicy=
ProjectName=
ProjectVersion=*******
Homepage=
SupportContact=
MinWindowWidth=16
MinWindowHeight=16

[/Script/UnrealEd.ProjectPackagingSettings]
@EngineCustomBuilds=Name
@ProjectCustomBuilds=Name
BuildConfiguration=PPBC_Development
FullRebuild=False
UsePakFile=True
bUseIoStore=True
bUseZenStore=False
bCompressed=True
PackageCompressionFormat=Oodle
PackageCompressionMethod=Kraken
PackageCompressionLevel_Distribution=7
PackageCompressionLevel_TestShipping=4
PackageCompressionLevel_DebugDevelopment=4
PackageCompressionMinBytesSaved=1024
PackageCompressionMinPercentSaved=5
bForceUseProjectCompressionFormatIgnoreHardwareOverride=False
bGenerateChunks=False
bChunkHardReferencesOnly=False
IncludePrerequisites=True
IncludeCrashReporter=False
InternationalizationPreset=English
+CulturesToStage=en
DefaultCulture=en
bSkipEditorContent=false
bSharedMaterialNativeLibraries=True
bShareMaterialShaderCode=True
bSkipMovies=False
bPakUsesSecondaryOrder=True
WriteBackMetadataToAssetRegistry=Disabled

+EarlyDownloaderPakFileFiles=...\Content\Internationalization\...\*.icu
+EarlyDownloaderPakFileFiles=...\Content\Internationalization\...\*.brk
+EarlyDownloaderPakFileFiles=...\Content\Internationalization\...\*.res
+EarlyDownloaderPakFileFiles=...\Content\Internationalization\...\*.nrm
+EarlyDownloaderPakFileFiles=...\Content\Internationalization\...\*.cfu
+EarlyDownloaderPakFileFiles=...\Content\Localization\...\*.*
+EarlyDownloaderPakFileFiles=...\Content\Certificates\...\*.*
+EarlyDownloaderPakFileFiles=-...\Content\Localization\Game\...\*.*
+EarlyDownloaderPakFileFiles=...\Config\...\*.ini
+EarlyDownloaderPakFileFiles=...\Engine\GlobalShaderCache*.bin
+EarlyDownloaderPakFileFiles=...\Content\ShaderArchive-Global*.ushaderbytecode
+EarlyDownloaderPakFileFiles=...\Content\Slate\...\*.*
+EarlyDownloaderPakFileFiles=-...\Content\Slate\...\*.uasset
+EarlyDownloaderPakFileFiles=-...\Content\Slate\...\*.umap
+EarlyDownloaderPakFileFiles=...\*.upluginmanifest
+EarlyDownloaderPakFileFiles=...\*.uproject
+EarlyDownloaderPakFileFiles=...\global_sf*.metalmap



[/Script/DeveloperToolSettings.PlatformsMenuSettings]
StagingDirectory=(Path="")
LaunchOnTarget=
PerPlatformBuildConfig=()
PerPlatformTargetFlavorName=(("Android", "Android_ASTC"))
PerPlatformBuildTarget=()

[/Script/Engine.HUD]
DebugDisplay=AI

[/Script/Engine.PlayerController]
InputYawScale=2.5
InputPitchScale=-2.5
InputRollScale=1.0
ForceFeedbackScale=1.0

[/Script/Engine.DebugCameraController]
bShowSelectedInfo=true

[/Script/Engine.WorldSettings]
ChanceOfPhysicsChunkOverride=1.0
bEnableChanceOfPhysicsChunkOverride=false
DefaultAmbientZoneSettings=(bIsWorldSettings=true)
MinUndilatedFrameTime=0.0005		; 2000 fps
MaxUndilatedFrameTime=0.4			; 2.5 fps
MinGlobalTimeDilation=0.0001
MaxGlobalTimeDilation=20.0

[/Script/AIModule.AIPerceptionComponent]
HearingRange=768
SightRadius=3000
LoseSightRadius=3500
PeripheralVisionAngle=90

[/Script/AIModule.AISense_Hearing]
SpeedOfSoundSq=0

[/Script/AIModule.AISenseConfig_Hearing]
Implementation=Class'/Script/AIModule.AISense_Hearing'
HearingRange=768
DetectionByAffiliation=(bDetectEnemies=true)

[/Script/AIModule.AISenseConfig_Sight]
Implementation=Class'/Script/AIModule.AISense_Sight'
SightRadius=3000
LoseSightRadius=3500
PeripheralVisionAngleDegrees=90
DetectionByAffiliation=(bDetectEnemies=true)
AutoSuccessRangeFromLastSeenLocation=-1.f

[/Script/AIModule.AISenseConfig_Damage]
Implementation=Class'/Script/AIModule.AISense_Damage'

[/Script/AIModule.EnvQueryManager]
MaxAllowedTestingTime=0.01
bTestQueriesUsingBreadth=true
QueryCountWarningThreshold=0
QueryCountWarningInterval=30.0

[/Script/LiveLink.LiveLinkSettings]
FrameInterpolationProcessor=Class'/Script/LiveLink.LiveLinkBasicFrameInterpolationProcessor'
+DefaultRoleSettings=(Role=Class'/Script/LiveLink.LiveLinkAnimationRole', FrameInterpolationProcessor=Class'/Script/LiveLink.LiveLinkAnimationFrameInterpolationProcessor')

[/Script/Engine.AssetManagerSettings]
@PrimaryAssetTypesToScan=PrimaryAssetType


+PrimaryAssetTypesToScan=(PrimaryAssetType="Map",AssetBaseClass=/Script/Engine.World,bHasBlueprintClasses=False,bIsEditorOnly=True,Directories=((Path="/Game/Maps")),SpecificAssets=,Rules=(Priority=-1,ChunkId=-1,bApplyRecursively=True,CookRule=Unknown))
+PrimaryAssetTypesToScan=(PrimaryAssetType="PrimaryAssetLabel",AssetBaseClass=/Script/Engine.PrimaryAssetLabel,bHasBlueprintClasses=False,bIsEditorOnly=True,Directories=((Path="/Game")),SpecificAssets=,Rules=(Priority=-1,ChunkId=-1,bApplyRecursively=True,CookRule=Unknown))

[ShaderPipelineCache.CacheFile]

[Staging]

[CookedEditorSettings]
+EngineAssetPaths="/Engine"
+ProjectAssetPaths="/Game"
+DisallowedObjectClassesToLoad="/Script/Engine.Blueprint"
+DisallowedObjectClassesToLoad="/Script/Engine.AssetImportData"
+DisallowedAssetClassesToGather="/Script/Engine.World"
+DisallowedPathsToGather="/Game/Developers"

+EngineExtraStageFiles=(Path="Config", Files="*Editor*")
+EngineExtraStageFiles=(Path="Config", Files="*Lightmass*")
+EngineExtraStageFiles=(Path="Config", Files="*SourceControlSettings*")
+EngineExtraStageFiles=(Path="Content/Slate")
+EngineExtraStageFiles=(Path="Content/Splash")
+EngineExtraStageFiles=(Path="Content/EngineMaterials")
+EngineExtraStageFiles=(Path="Content/EditorResources")
+EngineExtraStageFiles=(Path="Content/EngineFonts")
+EngineExtraStageFiles=(Path="Content/Maps/Templates")
+EngineExtraStageFiles=(Path="Build", Files="Build.version", Recursive=false, NonUFS=true)
+EngineExtraStageFiles=(Path="Source/ThirdParty/Licenses")

bStageShaderDirs=true
bStagePlatformBuildDirs=false
bStageExtrasDirs=false
bStagePlatformDirs=true
bStageUAT=true
bIsForExternalDistribution=false

+DisabledPlugins=USDStageEditor
+DisabledPlugins=USDImporter


bBuildAgainstRelease=false
DLCPluginName=MakeCookedEditorAsDLC
ReleaseName=
ReleaseTargetType=Game


[CookedEditorSettings_CookedEditor]
MapMode=Uncooked

+DisabledPlugins=GeometryMode
+DisabledPlugins=Volumetrics
+DisabledPlugins=AssetSearch
+DisabledPlugins=BPEditorExtension
+DisabledPlugins=ObjectBrowser
+DisabledPlugins=FunctionalTestingEditor
+DisabledPlugins=RuntimeTests
+DisabledPlugins=EditorTests
+DisabledPluginsInShipping=GameplayInsights
+DisabledPluginsInShipping=TraceSourceFilters

+EngineExtraStageFiles=(Path="Content/Editor/Slate")
+ProjectExtraStageFiles=(Path="Content/Editor/Slate")

[CookedEditorSettings_CookedCooker]
MapMode=Cooked

+EngineExtraStageFiles=(Path="Config")
+ProjectExtraStageFiles=(Path="Config")

+EngineExtraStageFiles=(Path="Build/BatchFiles", Recursive=true, NonUFS=true)
+EngineExtraStageFiles=(Path="Build/BatchFiles/Linux", Recursive=true, NonUFS=true)
