

[DataDrivenPlatformInfo]
bIsConfidential=false
bIsFakePlatform=true
GlobalIdentifier=97F8C85EF1B0424792D201C893358C8F

[ShaderPlatform VULKAN_SM5]
Language=Vulkan
MaxFeatureLevel=SM5
ShaderFormat=SF_VULKAN_SM5

bIsMobile = false
bIsMetalMRT = false
bIsPC = true
bIsConsole = false
bIsAndroidOpenGLES = false
bSupportsDxc = true
bIsSPIRV=true

bSupportsDebugViewShaders=true

bSupportsMobileMultiView = false

bSupportsArrayTextureCompression = false

bSupportsDistanceFields = true
bSupportsDiaphragmDOF = true
bSupportsRGBColorBuffer = true
bSupportsPercentageCloserShadows=true
bSupportsIndexBufferUAVs = true

bSupportsInstancedStereo = true
SupportsMultiViewport = RuntimeDependent

bSupportsMSAA = true

bSupports4ComponentUAVReadWrite = false

bSupportsRenderTargetWriteMask = false

bSupportsRayTracing = true
bSupportsRayTracingShaders = false
bSupportsInlineRayTracing = true
bSupportsRayTracingIndirectInstanceData = true
bSupportsByteBufferComputeShaders = true

bSupportsGPUScene = true

bSupportsPrimitiveShaders = false

bSupportsUInt64ImageAtomics = false

bRequiresVendorExtensionsForAtomics = false

bSupportsNanite = false
bSupportsSceneDataCompressedTransforms = true
bSupportsLumenGI = true

bSupportsSSDIndirect = false

bSupportsTemporalHistoryUpscale = false

bSupportsRTIndexFromVS = false

bSupportsIntrinsicWaveOnce = false

bSupportsConservativeRasterization = false

bSupportsWaveOperations=RuntimeDependent
MinimumWaveSize=4
MaximumWaveSize=128

bRequiresExplicit128bitRT = false

bTargetsTiledGPU = false
bNeedsOfflineCompiler = false

bSupportsComputeFramework = true

bSupportsDualSourceBlending = true

bRequiresGeneratePrevTransformBuffer = false

bRequiresRenderTargetDuringRaster = true

bRequiresDisableForwardLocalLights = true

bCompileSignalProcessingPipeline = true
bSupportsGen5TemporalAA=true

bSupportsPerPixelDBufferMask = false


bSupportsVariableRateShading = true

NumberOfComputeThreads = 64

bSupportsHairStrandGeometry=true
bSupportsFFTBloom=true
bSupportsDiaphragmDOF=true

bSupportsVertexShaderLayer=false

bSupportsAnisotropicMaterials=true

bSupportsROV=false
bSupportsOIT=false

EnablesHLSL2021ByDefault=1
FriendlyName=LOCTEXT("FriendlyShaderPlatformName_VulkanPC_SM5", "PC Vulkan SM5")

BindlessSupport=Unsupported
bSupportsNNEShaders=false
bSupportsUniformBufferObjects = true
bSupportsRealTypes=Unsupported
bSupportsIndependentSamplers=true



[ShaderPlatform VULKAN_SM6]
Language=Vulkan
MaxFeatureLevel=SM6
ShaderFormat=SF_VULKAN_SM6

bIsMobile = false
bIsMetalMRT = false
bIsPC = true
bIsConsole = false
bIsAndroidOpenGLES = false
bSupportsDxc = true
bIsSPIRV=true

bSupportsDebugViewShaders=true
bSupportsMobileMultiView = false
bSupportsArrayTextureCompression = false

bSupportsDistanceFields = true
bSupportsDiaphragmDOF = true
bSupportsRGBColorBuffer = true
bSupportsPercentageCloserShadows=true
bSupportsVolumetricFog = true
bSupportsIndexBufferUAVs = true

bSupportsInstancedStereo = true
SupportsMultiViewport = RuntimeDependent

bSupportsMSAA = true
bSupports4ComponentUAVReadWrite = false
bSupportsRenderTargetWriteMask = false

bSupportsRayTracing = true
bSupportsInlineRayTracing = true
bSupportsRayTracingIndirectInstanceData = true
bSupportsByteBufferComputeShaders = true

bSupportsRayTracingShaders = true
bSupportsPathTracing = true
bSupportsRayTracingCallableShaders = true
bSupportsRayTracingProceduralPrimitive = true
bSupportsHighEndRayTracingEffects=true

bSupportsGPUScene = true
bSupportsPrimitiveShaders = false
bSupportsUInt64ImageAtomics = true
bRequiresVendorExtensionsForAtomics = false

bSupportsNanite = true
bSupportsSceneDataCompressedTransforms = true
bSupportsLumenGI = true

bSupportsMeshShadersTier0=true
bSupportsMeshShadersTier1=true
MaxMeshShaderThreadGroupSize=128

bSupportsSSDIndirect = false

bSupportsTemporalHistoryUpscale = false

bSupportsRTIndexFromVS = false

bSupportsIntrinsicWaveOnce = false

bSupportsConservativeRasterization = false

bSupportsWaveOperations=RuntimeGuaranteed
MinimumWaveSize=4
MaximumWaveSize=128

bRequiresExplicit128bitRT = false

bTargetsTiledGPU = false
bNeedsOfflineCompiler = false

bSupportsComputeFramework = true

bSupportsDualSourceBlending = true

bRequiresGeneratePrevTransformBuffer = false

bRequiresRenderTargetDuringRaster = true

bRequiresDisableForwardLocalLights = true

bCompileSignalProcessingPipeline = true
bSupportsGen5TemporalAA=true

bSupportsPerPixelDBufferMask = false

bSupportsVariableRateShading = true

NumberOfComputeThreads = 64

bSupportsHairStrandGeometry=true
bSupportsFFTBloom=true
bSupportsDiaphragmDOF=true

bSupportsVertexShaderLayer=false

bSupportsAnisotropicMaterials=true

bSupportsROV=false
bSupportsOIT=false

EnablesHLSL2021ByDefault=1
FriendlyName=LOCTEXT("FriendlyShaderPlatformName_VulkanPC_SM6", "PC Vulkan SM6")

BindlessSupport=AllShaderTypes
bSupportsNNEShaders=false
bSupportsUniformBufferObjects = true
bSupportsRealTypes=Unsupported
bSupportsIndependentSamplers=true

SupportsBarycentricsSemantic=RuntimeGuaranteed


[ShaderPlatform VULKAN_PCES3_1]
Language=Vulkan
MaxFeatureLevel=ES3_1
ShaderFormat=SF_VULKAN_ES31
bIsPC=true
bIsMobile=true
bSupportsMobileMultiView=true
bSupportsIndexBufferUAVs=true
bSupportsManualVertexFetch=false
bSupportsVertexShaderLayer=true
bSupportsSceneDataCompressedTransforms=true
bSupportsShaderPipelines = false
bSupportsVertexShaderSRVs = false
bSupportsUniformBufferObjects = true
bSupportsDualSourceBlending = true
bSupportsIndependentSamplers=true

FriendlyName=LOCTEXT("FriendlyShaderPlatformName_VulkanPC_Mobile", "PC Vulkan Mobile")

[PreviewPlatform VULKAN_SM5]
PlatformName=PC
ShaderFormat=SF_VULKAN_SM5
ShaderPlatform=VULKAN_SM5
MenuTooltip=LOCTEXT("PreviewMenuTooltip_VulkanPC_SM5", "Linux using SM5 profile")
