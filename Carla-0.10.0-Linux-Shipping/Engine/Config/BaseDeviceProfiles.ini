[DeviceProfile]
*TextureLODGroups=Group

[DeviceProfiles]
+DeviceProfileNameAndTypes=WindowsEditor,Windows
+DeviceProfileNameAndTypes=Windows,Windows
+DeviceProfileNameAndTypes=WindowsServer,Windows
+DeviceProfileNameAndTypes=WindowsClient,Windows
+DeviceProfileNameAndTypes=Windows_Preview_ES31,Windows
+DeviceProfileNameAndTypes=Windows_Preview_ES31_SDF,Windows
+DeviceProfileNameAndTypes=IOS,IOS
+DeviceProfileNameAndTypes=iPadAir2,IOS
+DeviceProfileNameAndTypes=iPadAir3,IOS
+DeviceProfileNameAndTypes=iPadAir4,IOS
+DeviceProfileNameAndTypes=iPadAir5,IOS
+DeviceProfileNameAndTypes=iPadAir6,IOS
+DeviceProfileNameAndTypes=iPadMini4,IOS
+DeviceProfileNameAndTypes=iPadMini5,IOS
+DeviceProfileNameAndTypes=iPadMini6,IOS
+DeviceProfileNameAndTypes=iPodTouch7,IOS
+DeviceProfileNameAndTypes=iPhone6S,IOS
+DeviceProfileNameAndTypes=iPhone6SPlus,IOS
+DeviceProfileNameAndTypes=iPhone7,IOS
+DeviceProfileNameAndTypes=iPhone7Plus,IOS
+DeviceProfileNameAndTypes=iPhoneSE,IOS
+DeviceProfileNameAndTypes=iPhone8,IOS
+DeviceProfileNameAndTypes=iPhone8Plus,IOS
+DeviceProfileNameAndTypes=iPhoneX,IOS
+DeviceProfileNameAndTypes=iPhoneXS,IOS
+DeviceProfileNameAndTypes=iPhoneXSMax,IOS
+DeviceProfileNameAndTypes=iPhoneXR,IOS
+DeviceProfileNameAndTypes=iPhone11,IOS
+DeviceProfileNameAndTypes=iPhone11Pro,IOS
+DeviceProfileNameAndTypes=iPhone11ProMax,IOS
+DeviceProfileNameAndTypes=iPhoneSE2,IOS
+DeviceProfileNameAndTypes=iPhone12Mini,IOS
+DeviceProfileNameAndTypes=iPhone12,IOS
+DeviceProfileNameAndTypes=iPhone12Pro,IOS
+DeviceProfileNameAndTypes=iPhone12ProMax,IOS
+DeviceProfileNameAndTypes=iPhone13Mini,IOS
+DeviceProfileNameAndTypes=iPhone13,IOS
+DeviceProfileNameAndTypes=iPhone13Pro,IOS
+DeviceProfileNameAndTypes=iPhone13ProMax,IOS
+DeviceProfileNameAndTypes=iPhoneSE3,IOS
+DeviceProfileNameAndTypes=iPhone14,IOS
+DeviceProfileNameAndTypes=iPhone14Plus,IOS
+DeviceProfileNameAndTypes=iPhone14Pro,IOS
+DeviceProfileNameAndTypes=iPhone14ProMax,IOS
+DeviceProfileNameAndTypes=iPhone15,IOS
+DeviceProfileNameAndTypes=iPhone15Plus,IOS
+DeviceProfileNameAndTypes=iPhone15Pro,IOS
+DeviceProfileNameAndTypes=iPhone15ProMax,IOS
+DeviceProfileNameAndTypes=iPhone16,IOS
+DeviceProfileNameAndTypes=iPhone16Plus,IOS
+DeviceProfileNameAndTypes=iPhone16Pro,IOS
+DeviceProfileNameAndTypes=iPhone16ProMax,IOS
+DeviceProfileNameAndTypes=iPadPro,IOS
+DeviceProfileNameAndTypes=iPadPro105,IOS
+DeviceProfileNameAndTypes=iPadPro129,IOS
+DeviceProfileNameAndTypes=iPadPro97,IOS
+DeviceProfileNameAndTypes=iPadPro2_129,IOS
+DeviceProfileNameAndTypes=iPad5,IOS
+DeviceProfileNameAndTypes=iPad6,IOS
+DeviceProfileNameAndTypes=iPad7,IOS
+DeviceProfileNameAndTypes=iPad8,IOS
+DeviceProfileNameAndTypes=iPad9,IOS
+DeviceProfileNameAndTypes=iPad10,IOS
+DeviceProfileNameAndTypes=iPadPro11,IOS
+DeviceProfileNameAndTypes=iPadPro2_11,IOS
+DeviceProfileNameAndTypes=iPadPro3_11,IOS
+DeviceProfileNameAndTypes=iPadPro4_11,IOS
+DeviceProfileNameAndTypes=iPadPro5_11,IOS
+DeviceProfileNameAndTypes=iPadPro3_129,IOS
+DeviceProfileNameAndTypes=iPadPro4_129,IOS
+DeviceProfileNameAndTypes=iPadPro5_129,IOS
+DeviceProfileNameAndTypes=iPadPro6_129,IOS
+DeviceProfileNameAndTypes=iPadPro7_129,IOS
+DeviceProfileNameAndTypes=AppleTV,IOS
+DeviceProfileNameAndTypes=AppleTV4K,IOS
+DeviceProfileNameAndTypes=AppleTV2_4K,IOS
+DeviceProfileNameAndTypes=TVOS,TVOS
+DeviceProfileNameAndTypes=VisionPro,VisionOS
+DeviceProfileNameAndTypes=VisionProSimulator,VisionOS
+DeviceProfileNameAndTypes=VisionOS,VisionOS
+DeviceProfileNameAndTypes=MacEditor,Mac
+DeviceProfileNameAndTypes=MacClient,Mac
+DeviceProfileNameAndTypes=Mac,Mac
+DeviceProfileNameAndTypes=MacServer,Mac
+DeviceProfileNameAndTypes=LinuxEditor,Linux
+DeviceProfileNameAndTypes=LinuxArm64Editor,LinuxArm64
+DeviceProfileNameAndTypes=Linux,Linux
+DeviceProfileNameAndTypes=LinuxArm64,Linux
+DeviceProfileNameAndTypes=LinuxClient,Linux
+DeviceProfileNameAndTypes=LinuxArm64Client,LinuxArm64
+DeviceProfileNameAndTypes=LinuxServer,Linux
+DeviceProfileNameAndTypes=LinuxArm64Server,LinuxArm64
+DeviceProfileNameAndTypes=Android,Android
+DeviceProfileNameAndTypes=Android_Preview_OpenGL,Android
+DeviceProfileNameAndTypes=Android_Preview_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Low,Android
+DeviceProfileNameAndTypes=Android_Mid,Android
+DeviceProfileNameAndTypes=Android_High,Android
+DeviceProfileNameAndTypes=Android_Emulator,Android
+DeviceProfileNameAndTypes=Android_Default,Android
+DeviceProfileNameAndTypes=Android_Adreno4xx,Android
+DeviceProfileNameAndTypes=Android_Adreno5xx_Low,Android
+DeviceProfileNameAndTypes=Android_Adreno5xx,Android
+DeviceProfileNameAndTypes=Android_Adreno6xx,Android
+DeviceProfileNameAndTypes=Android_Adreno6xx_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Adreno7xx,Android
+DeviceProfileNameAndTypes=Android_Adreno7xx_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Mali_T6xx,Android
+DeviceProfileNameAndTypes=Android_Mali_T7xx,Android
+DeviceProfileNameAndTypes=Android_Mali_T8xx,Android
+DeviceProfileNameAndTypes=Android_Mali_G71,Android
+DeviceProfileNameAndTypes=Android_Mali_G72,Android
+DeviceProfileNameAndTypes=Android_Mali_G72_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Mali_G76,Android
+DeviceProfileNameAndTypes=Android_Mali_G76_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Mali_G77,Android
+DeviceProfileNameAndTypes=Android_Mali_G77_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Mali_G78,Android
+DeviceProfileNameAndTypes=Android_Mali_G78_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Mali_G710,Android
+DeviceProfileNameAndTypes=Android_Mali_G710_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Mali_G7xx,Android
+DeviceProfileNameAndTypes=Android_Mali_G7xx_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Mali_G9xx,Android
+DeviceProfileNameAndTypes=Android_Mali_G9xx_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Xclipse_5xx,Android
+DeviceProfileNameAndTypes=Android_Xclipse_5xx_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Xclipse_9xx,Android
+DeviceProfileNameAndTypes=Android_Xclipse_xxx,Android
+DeviceProfileNameAndTypes=Android_Xclipse_xxx_Vulkan,Android
+DeviceProfileNameAndTypes=Android_Vulkan_SM5,Android
+DeviceProfileNameAndTypes=Android_Adreno_Vulkan_SM5,Android
+DeviceProfileNameAndTypes=Android_Mali_Vulkan_SM5,Android
+DeviceProfileNameAndTypes=Android_Xclipse_Vulkan_SM5,Android
+DeviceProfileNameAndTypes=Android_PowerVR_G6xxx,Android
+DeviceProfileNameAndTypes=Android_PowerVR_GT7xxx,Android
+DeviceProfileNameAndTypes=Android_PowerVR_GE8xxx,Android
+DeviceProfileNameAndTypes=Android_PowerVR_GM9xxx,Android
+DeviceProfileNameAndTypes=Android_PowerVR_GM9xxx_Vulkan,Android
+DeviceProfileNameAndTypes=Android_TegraK1,Android
+DeviceProfileNameAndTypes=Android_Unknown_Vulkan,Android
+DeviceProfileNameAndTypes=Meta_Quest_3,Android
+DeviceProfileNameAndTypes=Meta_Quest_Pro,Android
+DeviceProfileNameAndTypes=Oculus_Quest2,Android
+DeviceProfileNameAndTypes=Oculus_Quest,Android
+DeviceProfileNameAndTypes=HoloLens,HoloLens
+DeviceProfileNameAndTypes=MagicLeap_Vulkan,Android

[GlobalDefaults DeviceProfile]
TextureLODGroups=(Group=TEXTUREGROUP_World,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_WorldNormalMap,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_WorldSpecular,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Character,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_CharacterNormalMap,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_CharacterSpecular,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Weapon,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_WeaponNormalMap,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_WeaponSpecular,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Vehicle,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_VehicleNormalMap,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_VehicleSpecular,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Cinematic,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Effects,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=linear,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_EffectsNotFiltered,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Skybox,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_UI,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_NoMipmaps)
+TextureLODGroups=(Group=TEXTUREGROUP_Lightmap,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Shadowmap,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,NumStreamedMips=3,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_RenderTarget,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_MobileFlattened,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Terrain_Heightmap,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Terrain_Weightmap,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=aniso,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Bokeh,MinLODSize=1,MaxLODSize=256,LODBias=0,MinMagFilter=linear,MipFilter=linear,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_Pixels2D,MinLODSize=1,MaxLODSize=16384,LODBias=0,MinMagFilter=point,MipFilter=point,MipGenSettings=TMGS_SimpleAverage)
+TextureLODGroups=(Group=TEXTUREGROUP_8BitData,LODBias=0,MinMagFilter=point,MipFilter=point,MipGenSettings=TMGS_NoMipmaps)
+TextureLODGroups=(Group=TEXTUREGROUP_16BitData,LODBias=0,MinMagFilter=point,MipFilter=point,MipGenSettings=TMGS_NoMipmaps)
+CVars=UI.SlateSDFText.RasterizationMode=Msdf
+CVars=UI.SlateSDFText.ResolutionLevel=2

[Windows DeviceProfile]
DeviceType=Windows
BaseProfileName=
bIsVisibleForAssets=True
+CVars=UI.SlateSDFText.RasterizationMode=Msdf
+CVars=UI.SlateSDFText.ResolutionLevel=2

[WindowsEditor DeviceProfile]
DeviceType=Windows
BaseProfileName=Windows

[WindowsClient DeviceProfile]
DeviceType=Windows
BaseProfileName=Windows

[WindowsServer DeviceProfile]
DeviceType=Windows
BaseProfileName=Windows

[WindowsCookedEditor DeviceProfile]
DeviceType=Windows
BaseProfileName=Windows
+CVars=s.AllowUnversionedContentInEditor=1
+CVars=cook.AllowCookedDataInEditorBuilds=1
+CVars=VA.AllowPkgVirtualization=0


[IOSDeviceMappings]
iPhone8,1=iPhone6S
iPhone8,2=iPhone6SPlus
iPhone8,4=iPhoneSE
iPhone9,[13]=iPhone7
iPhone9,[24]=iPhone7Plus
iPhone10,[1,4]=iPhone8
iPhone10,[2,5]=iPhone8Plus
iPhone10,[3,6]=iPhoneX
iPhone11,2=iPhoneXS
iPhone11,[4,6]=iPhoneXSMax
iPhone11,8=iPhoneXR
iPhone12,1=iPhone11
iPhone12,3=iPhone11Pro
iPhone12,5=iPhone11ProMax
iPhone12,8=iPhoneSE2
iPhone13,1=iPhone12Mini
iPhone13,2=iPhone12
iPhone13,3=iPhone12Pro
iPhone13,4=iPhone12ProMax
iPhone14,2=iPhone13Pro
iPhone14,3=iPhone13ProMax
iPhone14,4=iPhone13Mini
iPhone14,5=iPhone13
iPhone14,6=iPhoneSE3
iPhone14,7=iPhone14
iPhone14,8=iPhone14Plus
iPhone15,2=iPhone14Pro
iPhone15,3=iPhone14ProMax
iPhone15,4=iPhone15
iPhone15,5=iPhone15Plus
iPhone16,1=iPhone15Pro
iPhone16,2=iPhone15ProMax
iPhone17,1=iPhone16Pro
iPhone17,2=iPhone16ProMax
iPhone17,3=iPhone16
iPhone17,4=iPhone16Plus
iPod9,1=iPodTouch7
iPad5,[1,2]=iPadMini4
iPad5,[3,4]=iPadAir2
iPad6,[3,4]=iPadPro97
iPad6,[7,8]=iPadPro129
iPad6,11=iPad5
iPad6,12=iPad5
iPad7,1$=iPadPro2_129
iPad7,2=iPadPro2_129
iPad7,[3,4]=iPadPro105
iPad7,[5,6]=iPad6
iPad7,11=iPad7
iPad7,12=iPad7
iPad8,1$=iPadPro11
iPad8,[2-4]=iPadPro11
iPad8,[5-8]=iPadPro3_129
iPad8,9=iPadPro2_11
iPad8,10=iPadPro2_11
iPad8,11=iPadPro4_129
iPad8,12=iPadPro4_129
iPad11,[1,2]=iPadMini5
iPad11,[3,4]=iPadAir3
iPad11,[6,7]=iPad8
iPad12,[1,2]=iPad9
iPad13,1$=iPadAir4
iPad13,2=iPadAir4
iPad13,[4-7]=iPadPro3_11
iPad13,[8-9]=iPadPro5_129
iPad13,1[0-1]=iPadPro5_129
iPad13,1[6-7]=iPadAir5
iPad13,1[8-9]=iPad10
iPad14,[1,2]=iPadMini6
iPad14,[3,4]=iPadPro4_11
iPad14,[5,6]=iPadPro6_129
iPad14,[8,9]=iPadAir6
iPad14,1$=iPadAir_13
iPad16,[3,4]=iPadPro5_11
iPad16,[5,6]=iPadPro7_129
AppleTV5,=AppleTV
AppleTV6,=AppleTV4K
AppleTV11,=AppleTV2_4K
RealityDevice14,1=VisionPro
iPhone=iPhone15
iPod=iPodTouch7
iPad=iPad10
AppleTV=AppleTV2_4K
VisionPro0.*=VisionProSimulator
VisionPro1.*=VisionPro

[IOS DeviceProfile]
DeviceType=IOS
BaseProfileName=
bIsVisibleForAssets=True
+CVars=r.HZBOcclusion=0
+CVars=r.EarlyZPass=0
+CVars=r.TranslucentLightingVolume=0
+CVars=r.AllowPointLightCubemapShadows=0
+CVars=r.Decal.StencilSizeThreshold=-1
+CVars=r.MorphTarget.Mode=0
+CVars=r.DefaultBackBufferPixelFormat=0
+CVars=sg.AntiAliasingQuality=0
+CVars=sg.ShadowQuality=1
+CVars=sg.GlobalIlluminationQuality=1
+CVars=sg.ReflectionQuality=1
+CVars=sg.ViewDistanceQuality=3
+CVars=sg.PostProcessQuality=3
+CVars=sg.TextureQuality=3
+CVars=sg.EffectsQuality=3
+CVars=sg.FoliageQuality=3
+CVars=sg.LandscapeQuality=2
+CVars=r.Forward.LightLinkedListCulling=0
+CVars=UI.SlateSDFText.RasterizationMode=SdfApproximation
+CVars=UI.SlateSDFText.ResolutionLevel=1

PreviewAllowlistCVars=sg.

[iPhone6S DeviceProfile]
DeviceType=IOS
BaseProfileName=IOS
+CVars=ios.PhysicalScreenDensity=326
+CVars=r.MobileContentScaleFactor=2
+CVars=sg.PostProcessQuality=3
+CVars=sg.ShadowQuality=2
+CVars=sg.GlobalIlluminationQuality=2
+CVars=r.Mobile.AmbientOcclusionQuality=0
+CVars=r.Mobile.PixelProjectedReflectionQuality=0

[iPhone6SPlus DeviceProfile]
DeviceType=IOS
BaseProfileName=IOS
+CVars=ios.PhysicalScreenDensity=401
+CVars=r.MobileContentScaleFactor=2
+CVars=sg.PostProcessQuality=3
+CVars=sg.ShadowQuality=2
+CVars=sg.GlobalIlluminationQuality=2
+CVars=r.Mobile.AmbientOcclusionQuality=0
+CVars=r.Mobile.PixelProjectedReflectionQuality=0

[iPhoneSE DeviceProfile]
DeviceType=IOS
BaseProfileName=IOS
+CVars=ios.PhysicalScreenDensity=326
+CVars=r.MobileContentScaleFactor=2
+CVars=sg.PostProcessQuality=3
+CVars=r.Mobile.AmbientOcclusionQuality=0
+CVars=r.Mobile.PixelProjectedReflectionQuality=0

[iPhone7 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone6S
+CVars=ios.PhysicalScreenDensity=326
+CVars=r.MobileContentScaleFactor=2
+CVars=r.Mobile.AmbientOcclusionQuality=0
+CVars=r.Mobile.PixelProjectedReflectionQuality=0

[iPhone7Plus DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone6SPlus
+CVars=ios.PhysicalScreenDensity=401
+CVars=r.MobileContentScaleFactor=2
+CVars=r.Mobile.AmbientOcclusionQuality=0
+CVars=r.Mobile.PixelProjectedReflectionQuality=0

[iPhone8 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone7
+CVars=ios.PhysicalScreenDensity=326
+CVars=sg.ShadowQuality=3
+CVars=sg.GlobalIlluminationQuality=3
+CVars=r.Mobile.AmbientOcclusionQuality=1
+CVars=r.Mobile.PixelProjectedReflectionQuality=1

[iPhone8Plus DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone7Plus
+CVars=ios.PhysicalScreenDensity=401
+CVars=sg.ShadowQuality=3
+CVars=sg.GlobalIlluminationQuality=3
+CVars=r.Mobile.AmbientOcclusionQuality=1
+CVars=r.Mobile.PixelProjectedReflectionQuality=1

[iPhoneX DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone8
+CVars=ios.PhysicalScreenDensity=401
+CVars=r.CustomUnsafeZones="(L:free[0,-15][812,15]);(P:fixed[83,0][206,30])"

[iPhoneXS DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone8
+CVars=ios.PhysicalScreenDensity=401
+CVars=r.CustomUnsafeZones="(L:free[0,-15][812,15]);(P:fixed[83,0][206,30])"

[iPhoneXSMax DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone8
+CVars=ios.PhysicalScreenDensity=401
+CVars=r.CustomUnsafeZones="(L:free[0,-15][896,15]);(P:fixed[104,0][206,30])"

[iPhoneXR DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone8
+CVars=ios.PhysicalScreenDensity=326
+CVars=r.CustomUnsafeZones="(L:free[0,-15][896,15]);(P:fixed[104,0][206,30])"

[iPhone11 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone8
+CVars=ios.PhysicalScreenDensity=326
+CVars=r.CustomUnsafeZones="(L:free[0,-15][812,15]);(P:fixed[83,0][206,30])"

[iPhone11Pro DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone8
+CVars=ios.PhysicalScreenDensity=401
+CVars=r.CustomUnsafeZones="(L:free[0,-15][812,15]);(P:fixed[83,0][206,30])"

[iPhone11ProMax DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone8
+CVars=ios.PhysicalScreenDensity=401
+CVars=r.CustomUnsafeZones="(L:free[0,-15][812,15]);(P:fixed[83,0][206,30])"

[iPhoneSE2 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone11
+CVars=ios.PhysicalScreenDensity=326

[iPhone12Mini DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone11
+CVars=ios.PhysicalScreenDensity=476

[iPhone12 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone11
+CVars=ios.PhysicalScreenDensity=460

[iPhone12Pro DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone11Pro
+CVars=ios.PhysicalScreenDensity=460

[iPhone12ProMax DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone11ProMax
+CVars=ios.PhysicalScreenDensity=458

[iPhone13Mini DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone11
+CVars=ios.PhysicalScreenDensity=476

[iPhone13 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone11
+CVars=ios.PhysicalScreenDensity=460

[iPhone13Pro DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone11Pro
+CVars=ios.PhysicalScreenDensity=460

[iPhone13ProMax DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone11ProMax
+CVars=ios.PhysicalScreenDensity=458

[iPhoneSE3 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone13
+CVars=ios.PhysicalScreenDensity=326

[iPhone14 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone13

[iPhone14Plus DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone13
+CVars=ios.PhysicalScreenDensity=458

[iPhone14Pro DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone13Pro

[iPhone14ProMax DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone13ProMax
+CVars=ios.PhysicalScreenDensity=460

[iPhone15 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone13

[iPhone15Plus DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone13
+CVars=ios.PhysicalScreenDensity=458

[iPhone15Pro DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone13Pro

[iPhone15ProMax DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone13ProMax
+CVars=ios.PhysicalScreenDensity=460

[iPhone16 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone15

[iPhone16Plus DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone15Plus
+CVars=ios.PhysicalScreenDensity=460

[iPhone16Pro DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone15Pro

[iPhone16ProMax DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone15ProMax

[iPodTouch7 DeviceProfile]
DeviceType=IOS
BaseProfileName=iPhone7
+CVars=ios.PhysicalScreenDensity=326
+CVars=r.MobileContentScaleFactor=2
+CVars=r.Mobile.AmbientOcclusionQuality=0
+CVars=r.Mobile.PixelProjectedReflectionQuality=0

[iPadAir2 DeviceProfile]
DeviceType=IOS
BaseProfileName=IOS
+CVars=ios.PhysicalScreenDensity=264
+CVars=sg.PostProcessQuality=3
+CVars=sg.ShadowQuality=2
+CVars=sg.GlobalIlluminationQuality=2
+CVars=r.Mobile.AmbientOcclusionQuality=0
+CVars=r.Mobile.PixelProjectedReflectionQuality=0

[iPadMini4 DeviceProfile]
DeviceType=IOS
BaseProfileName=IOS
+CVars=ios.PhysicalScreenDensity=401
+CVars=sg.PostProcessQuality=2
+CVars=r.Mobile.AmbientOcclusionQuality=0
+CVars=r.Mobile.PixelProjectedReflectionQuality=0

[iPadPro DeviceProfile]
DeviceType=IOS
BaseProfileName=IOS
+CVars=ios.PhysicalScreenDensity=264
+CVars=r.MobileContentScaleFactor=1.5
+CVars=g.TimeoutForBlockOnRenderFence=3000000
+CVars=r.Decal.StencilSizeThreshold=0.1
+CVars=r.MetalComputeParameterSize=1024
+CVars=r.EarlyZPass=3
+CVars=sg.ShadowQuality=3
+CVars=sg.GlobalIlluminationQuality=3
+CVars=sg.PostProcessQuality=3
+CVars=sg.AntiAliasingQuality=3

[iPadPro97 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadPro129 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadPro105 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadPro11 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadPro2_11 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadPro3_11 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadPro4_11 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro3_11

[iPadPro5_11 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro3_11

[iPad5 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro97
+CVars=ios.PhysicalScreenDensity=264

[iPad6 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro97
+CVars=ios.PhysicalScreenDensity=264

[iPad7 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro97
+CVars=ios.PhysicalScreenDensity=264

[iPad8 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro97
+CVars=ios.PhysicalScreenDensity=264

[iPad9 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro97
+CVars=ios.PhysicalScreenDensity=264

[iPad10 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPad9

[iPadPro2_129 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadPro3_129 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadPro4_129 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadPro5_129 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadPro6_129 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro5_129

[iPadPro7_129 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro5_129

[iPadAir3 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadAir4 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=264

[iPadAir5 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadAir4

[iPadAir6 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadAir4

[iPadMini5 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=401

[iPadMini6 DeviceProfile]
DeviceType=IOS
BaseProfileName=IPadPro
+CVars=ios.PhysicalScreenDensity=326

[AppleTV DeviceProfile]
DeviceType=IOS
BaseProfileName=IOS
+CVars=ios.PhysicalScreenDensity=0
+CVars=sg.PostProcessQuality=3
+CVars=sg.ShadowQuality=3
+CVars=sg.GlobalIlluminationQuality=3
+CVars=r.Mobile.AmbientOcclusionQuality=0
+CVars=r.Mobile.PixelProjectedReflectionQuality=0
+CVars=UI.SlateSDFText.RasterizationMode=SdfApproximation
+CVars=UI.SlateSDFText.ResolutionLevel=1

[TVOS DeviceProfile]
DeviceType=TVOS
BaseProfileName=AppleTV

[AppleTV4K DeviceProfile]
DeviceType=IOS
BaseProfileName=AppleTV
+CVars=ios.PhysicalScreenDensity=0

[AppleTV2_4K DeviceProfile]
DeviceType=IOS
BaseProfileName=AppleTV4K

[VisionPro DeviceProfile]
DeviceType=VisionOS
BaseProfileName=IOS
+CVars=r.Mobile.XRMSAAMode=2
+CVars=xr.OpenXRLateUpdateDeviceLocationsAfterReflections=1

[VisionProSimulator DeviceProfile]
DeviceType=VisionOS
BaseProfileName=VisionPro
+CVars=r.Mobile.VirtualTextures=0
+CVars=xr.OpenXRLateUpdateDeviceLocationsAfterReflections=1

[VisionOS DeviceProfile]
DeviceType=VisionOS
BaseProfileName=VisionPro


[/Script/AndroidDeviceProfileSelector.AndroidDeviceProfileMatchingRules]
+MatchProfile=(Profile="Meta_Quest_3",Match=((SourceType=SRC_DeviceMake,CompareType=CMP_Equal,MatchString="Oculus"), (SourceType=SRC_HMDSystemName,CompareType=CMP_Equal,MatchString="Meta Quest 3")))
+MatchProfile=(Profile="Meta_Quest_Pro",Match=((SourceType=SRC_DeviceMake,CompareType=CMP_Equal,MatchString="Oculus"), (SourceType=SRC_HMDSystemName,CompareType=CMP_Equal,MatchString="Meta Quest Pro")))
+MatchProfile=(Profile="Oculus_Quest2",Match=((SourceType=SRC_DeviceMake,CompareType=CMP_Equal,MatchString="Oculus"), (SourceType=SRC_HMDSystemName,CompareType=CMP_Equal,MatchString="Oculus Quest2")))
+MatchProfile=(Profile="Oculus_Quest",Match=((SourceType=SRC_DeviceMake,CompareType=CMP_Equal,MatchString="Oculus")))
+MatchProfile=(Profile="MagicLeap_Vulkan",Match=((SourceType=SRC_DeviceMake,CompareType=CMP_Equal,MatchString="Magic Leap"), (SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))

+MatchProfile=(Profile="Android_Emulator",Match=((SourceType=SRC_DeviceMake,CompareType=CMP_Equal,MatchString="Google"),(SourceType=SRC_GPUFamily,CompareType=CMP_Regex,MatchString="Android Emulator")))

+MatchProfile=(Profile="Android_Adreno4xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Adreno \\(TM\\) 4[0-9][0-9]")))
+MatchProfile=(Profile="Android_Adreno5xx_Low",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Adreno \\(TM\\) 5[0-1][0-9]")))
+MatchProfile=(Profile="Android_Adreno5xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Adreno \\(TM\\) 5[0-9][0-9]")))
+MatchProfile=(Profile="Android_Adreno_Vulkan_SM5",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Adreno \\(TM\\) 7[0-9][0-9]"),(SourceType=SRC_AndroidVersion, CompareType=CMP_Regex,MatchString="([0-9]+).*"),(SourceType=SRC_PreviousRegexMatch,CompareType=CMP_GreaterEqual,MatchString="10"),(SourceType=SRC_SM5Available,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Adreno6xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Adreno \\(TM\\) 660"),(SourceType=SRC_GLVersion, CompareType=CMP_Regex,MatchString="V@([0-9]+)"),(SourceType=SRC_PreviousRegexMatch,CompareType=CMP_LessEqual,MatchString="525")))
+MatchProfile=(Profile="Android_Adreno6xx_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Adreno \\(TM\\) 6[0-9][0-9]"),(SourceType=SRC_AndroidVersion, CompareType=CMP_Regex,MatchString="([0-9]+).*"),(SourceType=SRC_PreviousRegexMatch,CompareType=CMP_GreaterEqual,MatchString="10"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true"),(SourceType=SRC_GLVersion, CompareType=CMP_Regex,MatchString="V@([0-9]+)"),(SourceType=SRC_PreviousRegexMatch,CompareType=CMP_GreaterEqual,MatchString="400")))
+MatchProfile=(Profile="Android_Adreno6xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Adreno \\(TM\\) 6[0-9][0-9]")))
+MatchProfile=(Profile="Android_Adreno7xx_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Adreno \\(TM\\) 7[0-9][0-9]"),(SourceType=SRC_AndroidVersion, CompareType=CMP_Regex,MatchString="([0-9]+).*"),(SourceType=SRC_PreviousRegexMatch,CompareType=CMP_GreaterEqual,MatchString="10"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Adreno7xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Adreno \\(TM\\) 7[0-9][0-9]")))
+MatchProfile=(Profile="Android_Unknown_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Adreno"),(SourceType=SRC_AndroidVersion, CompareType=CMP_Regex,MatchString="([0-9]+).*"),(SourceType=SRC_PreviousRegexMatch,CompareType=CMP_GreaterEqual,MatchString="10"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))

+MatchProfile=(Profile="Android_Mali_T6xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-T6")))
+MatchProfile=(Profile="Android_Mali_T7xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-T7")))
+MatchProfile=(Profile="Android_Mali_T8xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-T8")))
+MatchProfile=(Profile="Android_Mali_G71",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G71($| )")))
+MatchProfile=(Profile="Android_Mali_G72_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G72($| )"),(SourceType=SRC_AndroidVersion, CompareType=CMP_Regex,MatchString="([0-9]+).*"),(SourceType=SRC_PreviousRegexMatch,CompareType=CMP_GreaterEqual,MatchString="9"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Mali_G72",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G72($| )")))
+MatchProfile=(Profile="Android_Mali_G76_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G76($| )"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Mali_G76",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G76($| )")))
+MatchProfile=(Profile="Android_Mali_G77_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G77($| )"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Mali_G77",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G77($| )")))
+MatchProfile=(Profile="Android_Mali_G78_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G78($| )"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Mali_G78",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G78($| )")))
+MatchProfile=(Profile="Android_Mali_G710_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G710"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Mali_G710",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G710")))
+MatchProfile=(Profile="Android_Mali_Vulkan_SM5",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G[7-9][1-9][0-9]"),(SourceType=SRC_SM5Available,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Mali_G7xx_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G7[0-9][0-9]"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Mali_G7xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G7[0-9][0-9]")))
+MatchProfile=(Profile="Android_Mali_G9xx_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G9[0-9][0-9]"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Mali_G9xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali\\-G9[0-9][0-9]")))

+MatchProfile=(Profile="Android_Unknown_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="^Mali"),(SourceType=SRC_AndroidVersion, CompareType=CMP_Regex,MatchString="([0-9]+).*"),(SourceType=SRC_PreviousRegexMatch,CompareType=CMP_GreaterEqual,MatchString="10"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))

+MatchProfile=(Profile="Android_Xclipse_5xx_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Samsung Xclipse 5[0-9][0-9]"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Xclipse_5xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Samsung Xclipse 5[0-9][0-9]")))
+MatchProfile=(Profile="Android_Xclipse_Vulkan_SM5",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Samsung Xclipse 9[0-9][0-9]"),(SourceType=SRC_SM5Available,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Xclipse_9xx_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Samsung Xclipse 9[0-9][0-9]"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Xclipse_9xx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Samsung Xclipse 9[0-9][0-9]")))
+MatchProfile=(Profile="Android_Xclipse_xxx_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Samsung Xclipse [0-9][0-9][0-9]"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_Xclipse_xxx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="Samsung Xclipse [0-9][0-9][0-9]")))

+MatchProfile=(Profile="Android_PowerVR_G6xxx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="PowerVR Rogue G6[0-9]+")))
+MatchProfile=(Profile="Android_PowerVR_GT7xxx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="PowerVR Rogue GT7[0-9]+")))
+MatchProfile=(Profile="Android_PowerVR_GE8xxx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="PowerVR Rogue GE8[0-9]+")))
+MatchProfile=(Profile="Android_PowerVR_GM9xxx_Vulkan",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="PowerVR Rogue GM9[0-9]+"),(SourceType=SRC_VulkanAvailable,CompareType=CMP_Equal,MatchString="true")))
+MatchProfile=(Profile="Android_PowerVR_GM9xxx",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Regex,MatchString="PowerVR Rogue GM9[0-9]+")))

+MatchProfile=(Profile="Android_TegraK1",Match=((SourceType=SRC_GpuFamily,CompareType=CMP_Equal,MatchString="NVIDIA Tegra"),(SourceType=SRC_GlVersion,CompareType=CMP_Regex,MatchString="^OpenGL ES 3\\.")))


[Android DeviceProfile]
DeviceType=Android
BaseProfileName=
bIsVisibleForAssets=True

+CVars=r.MobileContentScaleFactor=1.0
+CVars=r.Vulkan.RobustBufferAccess=1
+CVars=r.Android.DisableVulkanSupport=1
+CVars=r.Android.DisableVulkanSM5Support=1
+CVars=r.DefaultBackBufferPixelFormat=0
+CVars=r.DistanceFields=0
+CVars=r.Forward.LightLinkedListCulling=0
+CVars=r.SkylightCapture.LODDistanceScale=2
+CVars=UI.SlateSDFText.RasterizationMode=SdfApproximation
+CVars=UI.SlateSDFText.ResolutionLevel=1
PreviewAllowlistCVars=sg.

[Android_Preview_OpenGL DeviceProfile]
DeviceType=Android
BaseProfileName=Android
+CVars=FX.AllowGPUSorting=0

[Android_Preview_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android

[Android_Low DeviceProfile]
DeviceType=Android
BaseProfileName=Android
+CVars=r.MobileContentScaleFactor=0.8
+CVars=sg.ViewDistanceQuality=0
+CVars=sg.AntiAliasingQuality=0
+CVars=sg.ShadowQuality=0
+CVars=sg.GlobalIlluminationQuality=0
+CVars=sg.ReflectionQuality=0
+CVars=sg.PostProcessQuality=0
+CVars=sg.TextureQuality=0
+CVars=sg.EffectsQuality=0
+CVars=sg.FoliageQuality=0
+CVars=sg.LandscapeQuality=0

[Android_Mid DeviceProfile]
DeviceType=Android
BaseProfileName=Android
+CVars=r.MobileContentScaleFactor=1.0
+CVars=sg.ViewDistanceQuality=1
+CVars=sg.AntiAliasingQuality=1
+CVars=sg.ShadowQuality=1
+CVars=sg.GlobalIlluminationQuality=1
+CVars=sg.ReflectionQuality=1
+CVars=sg.PostProcessQuality=1
+CVars=sg.TextureQuality=1
+CVars=sg.EffectsQuality=1
+CVars=sg.FoliageQuality=1
+CVars=sg.LandscapeQuality=1
+CVars=UI.SlateSDFText.ResolutionLevel=2

[Android_High DeviceProfile]
DeviceType=Android
BaseProfileName=Android
+CVars=sg.ViewDistanceQuality=2
+CVars=sg.AntiAliasingQuality=2
+CVars=sg.ShadowQuality=2
+CVars=sg.GlobalIlluminationQuality=2
+CVars=sg.ReflectionQuality=2
+CVars=sg.PostProcessQuality=2
+CVars=sg.TextureQuality=2
+CVars=sg.EffectsQuality=2
+CVars=sg.FoliageQuality=2
+CVars=sg.LandscapeQuality=2
+CVars=r.MobileContentScaleFactor=1.0
+CVars=UI.SlateSDFText.RasterizationMode=Msdf
+CVars=UI.SlateSDFText.ResolutionLevel=2

[Android_Emulator DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High
+CVars=r.Android.DisableVulkanSupport=0

[Android_Default DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mid

[Android_Vulkan_SM5 DeviceProfile]
DeviceType=Android
BaseProfileName=Android
+CVars=sg.ViewDistanceQuality=2
+CVars=sg.AntiAliasingQuality=1
+CVars=sg.ShadowQuality=2
+CVars=sg.GlobalIlluminationQuality=2
+CVars=sg.ReflectionQuality=2
+CVars=sg.PostProcessQuality=2
+CVars=sg.TextureQuality=2
+CVars=sg.EffectsQuality=2
+CVars=sg.FoliageQuality=2
+CVars=sg.ShadingQuality=2
+CVars=sg.LandscapeQuality=2
+CVars=r.BloomQuality=2
+CVars=r.LightShaftQuality=1
+CVars=r.Shadow.MaxResolution=2048
+CVars=r.Shadow.MaxCSMResolution=2048
+CVars=r.Shadow.WholeSceneShadowCacheMb=40
+CVars=r.Shadow.CachedShadowsCastFromMovablePrimitives=0
+CVars=r.Shadow.MaxNumPointShadowCacheUpdatesPerFrame=1
+CVars=r.Shadow.MaxNumSpotShadowCacheUpdatesPerFrame=1
+CVars=r.Shadow.DistanceScale=1.0
+CVars=r.Shadow.CSM.MaxCascades=2
+CVars=r.ShadowQuality=2
+CVars=r.Shadow.CSMShadowDistanceFadeoutMultiplier=2.5
+CVars=r.SSS.Quality=0
+CVars=r.SSS.Scale=0
+CVars=r.SSR.Quality=0
+CVars=r.Android.DisableVulkanSM5Support=0
+CVars=r.Android.DisableVulkanSupport=0
+CVars=r.DistanceFields=1
+CVars=r.Vulkan.RayTracing.AllowCompaction=0
+CVars=r.Vulkan.RayTracing.TLASPreferFastTraceTLAS=0

[Android_Adreno_Vulkan_SM5 DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Vulkan_SM5

[Android_Mali_Vulkan_SM5 DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Vulkan_SM5
+CVars=r.Vulkan.DepthStencilForceStorageBit=1

[Android_Xclipse_Vulkan_SM5 DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Vulkan_SM5


[Android_Adreno4xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Low
+CVars=r.Android.GLESFlipYMethod=2

[Android_Adreno5xx_Low DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Low

[Android_Adreno5xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mid

[Android_Adreno6xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High
+CVars=r.Android.GLESFlipYMethod=2

[Android_Adreno6xx_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Adreno6xx
+CVars=r.Android.DisableVulkanSupport=0

[Android_Adreno7xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High

[Android_Adreno7xx_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Adreno7xx
+CVars=r.Android.DisableVulkanSupport=0


[Android_Xclipse_5xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Low

[Android_Xclipse_5xx_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Xclipse_5xx
+CVars=r.Android.DisableVulkanSupport=0

[Android_Xclipse_9xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High

[Android_Xclipse_9xx_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Xclipse_9xx
+CVars=r.Android.DisableVulkanSupport=0
+CVars=r.Vulkan.Depth24Bit=0

[Android_Xclipse_xxx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mid

[Android_Xclipse_xxx_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Xclipse_xxx
+CVars=r.Android.DisableVulkanSupport=0
+CVars=r.Vulkan.Depth24Bit=0


[Android_PowerVR_G6xxx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Low
+CVars=r.SkyAtmosphere.LUT32=1

[Android_PowerVR_GT7xxx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mid
+CVars=r.SkyAtmosphere.LUT32=1

[Android_PowerVR_GE8xxx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mid
+CVars=r.SkyAtmosphere.LUT32=1

[Android_PowerVR_GM9xxx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High
+CVars=r.SkyAtmosphere.LUT32=1

[Android_PowerVR_GM9xxx_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_PowerVR_GM9xxx
+CVars=r.Android.DisableVulkanSupport=0


[Android_Mali_T6xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Low
+CVars=r.Android.MaliMidgardIndexingBug=1

[Android_Mali_T7xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Low
+CVars=r.Android.MaliMidgardIndexingBug=1

[Android_Mali_T8xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mid
+CVars=r.Android.MaliMidgardIndexingBug=1
+CVars=r.Android.DisableEarlyFragmentTests=1

[Android_Mali_G71 DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mid

[Android_Mali_G72 DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High

[Android_Mali_G72_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mali_G72
+CVars=r.Android.DisableVulkanSupport=0

[Android_Mali_G76 DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High

[Android_Mali_G76_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mali_G76
+CVars=r.Android.DisableVulkanSupport=0

[Android_Mali_G77 DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High

[Android_Mali_G77_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mali_G77
+CVars=r.Android.DisableVulkanSupport=0

[Android_Mali_G78 DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High

[Android_Mali_G78_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mali_G78
+CVars=r.Android.DisableVulkanSupport=0

[Android_Mali_G710 DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High

[Android_Mali_G710_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mali_G710
+CVars=r.Android.DisableVulkanSupport=0

[Android_Mali_G7xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mali_G710

[Android_Mali_G7xx_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mali_G7xx
+CVars=r.Android.DisableVulkanSupport=0

[Android_Mali_G9xx DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mali_G710

[Android_Mali_G9xx_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mali_G9xx
+CVars=r.Android.DisableVulkanSupport=0

[Android_TegraK1 DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High

[Android_Unknown_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_High
+CVars=r.Android.DisableVulkanSupport=0

[Meta_Quest_3 DeviceProfile]
DeviceType=Android
BaseProfileName=Meta_Quest_Pro

[Meta_Quest_Pro DeviceProfile]
DeviceType=Android
BaseProfileName=Oculus_Quest2

[Oculus_Quest2 DeviceProfile]
DeviceType=Android
BaseProfileName=Oculus_Quest
+CVars=r.Mobile.Oculus.ForceSymmetric=1
+CVars=fx.NiagaraAllowGPUParticles=1
+CVars=FX.AllowGPUSorting=1

[Oculus_Quest DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mid
+CVars=xr.VRS.DynamicFoveation=1
+CVars=r.Android.DisableVulkanSupport=0
+CVars=fx.NiagaraAllowGPUParticles=0
+CVars=FX.AllowGPUSorting=0
+CVars=r.Vulkan.VRSFormat=3

[Mac DeviceProfile]
DeviceType=Mac
BaseProfileName=
bIsVisibleForAssets=True
+CVars=r.Shaders.ZeroInitialise=1
+CVars=r.Shaders.BoundsChecking=1
+CVars=UI.SlateSDFText.RasterizationMode=Msdf
+CVars=UI.SlateSDFText.ResolutionLevel=2

[MacClient DeviceProfile]
DeviceType=Mac
BaseProfileName=Mac

[MacEditor DeviceProfile]
DeviceType=Mac
BaseProfileName=Mac

[MacServer DeviceProfile]
DeviceType=Mac
BaseProfileName=Mac

[Linux DeviceProfile]
DeviceType=Linux
BaseProfileName=
bIsVisibleForAssets=True
MeshLODSettings=
TextureLODSettings=
+CVars=UI.SlateSDFText.RasterizationMode=Msdf
+CVars=UI.SlateSDFText.ResolutionLevel=2

[LinuxEditor DeviceProfile]
DeviceType=Linux
BaseProfileName=Linux
MeshLODSettings=
TextureLODSettings=

[LinuxAArch6 DeviceProfile]
DeviceType=Linux
BaseProfileName=Linux
MeshLODSettings=
TextureLODSettings=

[LinuxClient DeviceProfile]
DeviceType=Linux
BaseProfileName=Linux

[LinuxArm64Client DeviceProfile]
DeviceType=Linux
BaseProfileName=Linux

[LinuxServer DeviceProfile]
DeviceType=Linux
BaseProfileName=Linux
MeshLODSettings=
TextureLODSettings=

[LinuxArm64Server DeviceProfile]
DeviceType=Linux
BaseProfileName=Linux
MeshLODSettings=
TextureLODSettings=

[LinuxCookedEditor DeviceProfile]
DeviceType=Linux
BaseProfileName=Linux
+CVars=s.AllowUnversionedContentInEditor=1
+CVars=cook.AllowCookedDataInEditorBuilds=1
+CVars=VA.AllowPkgVirtualization=0

[HoloLens DeviceProfile]
DeviceType=HoloLens
BaseProfileName=Windows
+CVars=r.D3D11.Depth24Bit=1
+CVars=r.D3D12.Depth24Bit=1

[MagicLeap_Vulkan DeviceProfile]
DeviceType=Android
BaseProfileName=Android_Mid
+CVars=xr.DisableOpenXROnAndroidWithoutOculus=0
+CVars=r.Android.DisableVulkanSupport=0
+CVars=r.Vulkan.SupportsBCTextureFormats=1
+CVars=r.Vulkan.MaxBarriersPerBatch=128

[Windows_Preview_ES31 DeviceProfile]
DeviceType=Windows
BaseProfileName=Windows
+CVars=r.Substrate.RoughDiffuse=0
+CVars=r.Substrate.ShadingQuality=2
+CVars=r.Substrate.TileCoord8bits=1
+CVars=r.Substrate.SheenQuality=2
+CVars=r.Substrate.Glints=0
+CVars=r.Substrate.SpecularProfile=0
+CVars=r.Substrate.ClosuresPerPixel=1

[Windows_Preview_ES31_SDF DeviceProfile]
DeviceType=Windows
BaseProfileName=Windows
+CVars=r.Substrate.RoughDiffuse=0
+CVars=r.Substrate.ShadingQuality=2
+CVars=r.Substrate.TileCoord8bits=1
+CVars=r.Substrate.SheenQuality=2
+CVars=r.Substrate.Glints=0
+CVars=r.Substrate.SpecularProfile=0
+CVars=r.Substrate.ClosuresPerPixel=1
