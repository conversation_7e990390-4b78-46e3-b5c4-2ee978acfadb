
[GPU_NVIDIA Windows]
SuggestedDriverVersion="536.40"

+DriverDenyList=(DriverVersion="<474.64", Reason="These driver versions have known stability issues and missing features", RHI="D3D11")

+DriverDenyList=(DriverVersion="<536.00", Reason="These driver versions have known stability issues and missing features", RHI="D3D12")
+DriverDenyList=(DriverVersion="<536.00", Reason="These driver versions have known stability issues and missing features", RHI="Vulkan")


[GPU_NVIDIA Linux]
SuggestedDriverVersion="535.86"

+DriverDenyList=(DriverVersion="<535.00", Reason="These driver versions have known stability issues and missing features")


[GPU_AMD Windows]
SuggestedDriverVersion="24.3.1"

+DriverDenyList=(DriverVersion="<27.20.20913.2000", Reason="These driver versions have known stability issues and missing features", RHI="D3D11")

+DriverDenyList=(DriverDate="<2-19-2024", Reason="These driver versions have known stability issues and missing features", RHI="D3D12")
+DriverDenyList=(DriverDate="<2-19-2024", Reason="These driver versions have known stability issues and missing features", RHI="Vulkan")


[GPU_AMD Linux]
SuggestedDriverVersion="23.3.1"

+DriverDenyList=(DriverVersion="<23.3.1", Reason="These driver versions have known stability issues and missing features")


[GPU_Intel Windows]
SuggestedDriverVersion="101.5333"

+DriverDenyList=(DriverDate="<8-6-2020", Reason="These driver versions have known stability issues and missing features", RHI="D3D11")

+DriverDenyList=(DriverVersion="<101.5333", Reason="These driver versions have known stability issues and missing features", RHI="D3D12")
+DriverDenyList=(DriverVersion="<101.5333", Reason="These driver versions have known stability issues and missing features", RHI="Vulkan")


[GPU_Intel Linux]
SuggestedDriverVersion="30.0.101.1340"
