[EditorLayouts]
UnrealEd_Layout_v1.5=("Type": "Layout","Name": "UnrealEd_Layout_v1.5","PrimaryAreaIndex": 0,"Areas": [("SizeCoefficient": 1,"Type": "Area","Orientation": "Orient_Horizontal","WindowPlacement": "Placement_NoWindow","Nodes": [("SizeCoefficient": 2,"Type": "Stack","HideTabWell": false,"ForegroundTab": "LevelEditor","Tabs": [("TabId": "LevelEditor","TabState": "OpenedTab"),("TabId": "DockedToolkit","TabState": "ClosedTab")])]),("SizeCoefficient": 1,"Type": "Area","Orientation": "Orient_Vertical","WindowPlacement": "Placement_Automatic","WindowSize_X": 1664,"WindowSize_Y": 910,"Nodes": [("SizeCoefficient": 1,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "StandaloneToolkit","TabState": "ClosedTab")])]),("SizeCoefficient": 1,"Type": "Area","Orientation": "Orient_Horizontal","WindowPlacement": "Placement_Automatic","WindowSize_X": 1664,"WindowSize_Y": 910,"Nodes": [("SizeCoefficient": 1,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "EditorSettings","TabState": "ClosedTab"),("TabId": "ProjectSettings","TabState": "ClosedTab"),("TabId": "PluginsEditor","TabState": "ClosedTab")])])])
LevelEditor_Layout_v1.8=("Type": "Layout","Name": "LevelEditor_Layout_v1.8","PrimaryAreaIndex": 0,"Areas": [("SizeCoefficient": 1,"Type": "Area","Orientation": "Orient_Horizontal","WindowPlacement": "Placement_NoWindow","Nodes": [("SizeCoefficient": 1,"Type": "Splitter","Orientation": "Orient_Vertical","Nodes": [("SizeCoefficient": 0.75,"Type": "Splitter","Orientation": "Orient_Horizontal","Nodes": [("SizeCoefficient": 0.15000000596046448,"Type": "Stack","HideTabWell": true,"ForegroundTab": "None","Tabs": [("TabId": "VerticalModeToolbar","TabState": "ClosedTab")]),("SizeCoefficient": 0.30000001192092896,"Type": "Splitter","Orientation": "Orient_Vertical","Nodes": [("SizeCoefficient": 0.5,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "TopLeftModeTab","TabState": "ClosedTab"),("TabId": "PlacementBrowser","TabState": "ClosedTab")]),("SizeCoefficient": 0.5,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "BottomLeftModeTab","TabState": "ClosedTab")])]),("SizeCoefficient": 1,"Type": "Stack","HideTabWell": true,"ForegroundTab": "LevelEditorViewport","Tabs": [("TabId": "LevelEditorViewport","TabState": "OpenedTab")])]),("SizeCoefficient": 0.40000000596046448,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "EmbeddedSequenceID","TabState": "ClosedTab"),("TabId": "ContentBrowserTab1","TabState": "ClosedTab"),("TabId": "Sequencer","TabState": "ClosedTab"),("TabId": "OutputLog","TabState": "ClosedTab")])]),("SizeCoefficient": 0.30000001192092896,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "TakeRecorder","TabState": "ClosedTab")]),("SizeCoefficient": 0.25,"Type": "Splitter","Orientation": "Orient_Vertical","Nodes": [("SizeCoefficient": 0.40000000596046448,"Type": "Stack","HideTabWell": false,"ForegroundTab": "LevelEditorSceneOutliner","Tabs": [("TabId": "TopRightModeTab","TabState": "ClosedTab"),("TabId": "LevelEditorSceneOutliner","TabState": "OpenedTab"),("TabId": "LevelEditorLayerBrowser","TabState": "ClosedTab")]),("SizeCoefficient": 1,"Type": "Stack","HideTabWell": false,"ForegroundTab": "LevelEditorSelectionDetails","Tabs": [("TabId": "BottomRightModeTab","TabState": "ClosedTab"),("TabId": "LevelEditorSelectionDetails","TabState": "OpenedTab"),("TabId": "WorldSettingsTab","TabState": "ClosedTab")])])])])
LayoutName=NSLOCTEXT("LayoutNamespace", "Default_Editor_Layout", "Default Editor Layout")
LayoutDescription=NSLOCTEXT("LayoutNamespace", "Default_layout_that_the_Unreal_Editor_automatically_generates", "Default layout that the Unreal Editor automatically generates")

