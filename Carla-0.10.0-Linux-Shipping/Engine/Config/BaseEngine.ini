
[SectionsToSave]
+Section=GameNetDriver StatelessConnectHandlerComponent
+Section=/Script/InterchangeEngine.InterchangeProjectSettings
+Section=/Script/TraceTools.LocalTraceFilterPresetContainer
+Section=/Script/TraceTools.SharedTraceFilterPresetContainer
+Section=Trace.FilterPresetList

[/Script/EngineSettings.GameMapsSettings]
GameInstanceClass=/Script/Engine.GameInstance
EditorStartupMap=/Engine/Maps/Templates/OpenWorld
GameDefaultMap=/Engine/Maps/Templates/OpenWorld
ServerDefaultMap=/Engine/Maps/Entry
GlobalDefaultGameMode="/Script/Engine.GameModeBase"
LocalMapOptions=

[InstallBundleManager]
ModuleName=NullInstallBundleManager
EditorModuleName=NullInstallBundleManager

[URL]
Protocol=unreal
Name=Player
SaveExt=usa
Port=7777

[HTTP]
HttpConnectionTimeout=30
HttpActivityTimeout=30
FlushSoftTimeLimitDefault=2.0
FlushHardTimeLimitDefault=4.0
FlushSoftTimeLimitShutdown=2.0
FlushHardTimeLimitShutdown=4.0
FlushSoftTimeLimitFullFlush=-1.0
FlushHardTimeLimitFullFlush=-1.0

[HTTP.Curl]
bAllowSeekFunction=false

[BackgroundHttp]
MaxActiveDownloads=4

TempFileTimeOutSeconds=259200

DeleteTempFilesWithoutURLMappingEntries=true

RemoveURLMappingEntriesWithoutPhysicalTempFiles=true

[WebSockets]
+WebSocketsProtocols="ws"
+WebSocketsProtocols="wss"
+WebSocketsProtocols="v10.stomp"
+WebSocketsProtocols="v11.stomp"
+WebSocketsProtocols="v12.stomp"
+WebSocketsProtocols="xmpp"
TextMessageMemoryLimit=1048576

[WebSockets.LibWebSockets]
ThreadStackSize=131072
ThreadTargetFrameTimeInSeconds=0.0333
ThreadMinimumSleepTimeInSeconds=0.0
MaxHttpHeaderData=32768
PingPongInterval=0

[Ping]
StackSize=1048576
MinPingSendWaitTimeMs=10.0

[Voice]
bEnabled=false
bDuckingOptOut=true

[SlateStyle]
DefaultFontName=/Engine/EngineFonts/Roboto

[PlatformMemoryBuckets]
LargestMemoryBucket_MinGB=32
LargerMemoryBucket_MinGB=12
DefaultMemoryBucket_MinGB=8
SmallerMemoryBucket_MinGB=6
SmallestMemoryBucket_MinGB=0

[CoreUObject.UninitializedScriptStructMembersCheck]
EngineModuleReflectedUninitializedPropertyVerbosity=Error
ProjectModuleReflectedUninitializedPropertyVerbosity=Error
ObjectReferenceReflectedUninitializedPropertyVerbosity=Error

[CoreUObject.ShortTypeNameInMetaDataCheck]
+MetaDataKeys=AllowedClasses
+MetaDataKeys=DisallowedClasses
+MetaDataKeys=BaseStruct
+MetaDataKeys=BaseClass
+MetaDataKeys=HasNativeMake
+MetaDataKeys=HasnativeBreak
+MetaDataKeys=RowType
+MetaDataKeys=BitmaskEnum
+MetaDataKeys=ActorClassToPoll
+MetaDataKeys=ComponentClass
+MetaDataKeys=MustImplement
+MetaDataKeys=MetaClass
+MetaDataKeys=MetaStruct
+MetaDataKeys=RequiredAssetDataTags

[/Script/Engine.Engine]
ConsoleClassName=/Script/Engine.Console
GameViewportClientClassName=/Script/Engine.GameViewportClient
LocalPlayerClassName=/Script/Engine.LocalPlayer
WorldSettingsClassName=/Script/Engine.WorldSettings
NavigationSystemClassName=/Script/NavigationSystem.NavigationSystemV1
NavigationSystemConfigClassName=/Script/NavigationSystem.NavigationSystemModuleConfig
AvoidanceManagerClassName=/Script/Engine.AvoidanceManager
PhysicsCollisionHandlerClassName=/Script/Engine.PhysicsCollisionHandler
LevelScriptActorClassName=/Script/Engine.LevelScriptActor
DefaultBlueprintBaseClassName=/Script/Engine.Actor
GameUserSettingsClassName=/Script/Engine.GameUserSettings
AIControllerClassName=/Script/AIModule.AIController
AssetManagerClassName=/Script/Engine.AssetManager
bAllowMatureLanguage=false
GameEngine=/Script/Engine.GameEngine
EditorEngine=/Script/UnrealEd.EditorEngine
UnrealEdEngine=/Script/UnrealEd.UnrealEdEngine
WireframeMaterialName=/Engine/EngineDebugMaterials/WireframeMaterial.WireframeMaterial
DefaultMaterialName=/Engine/EngineMaterials/WorldGridMaterial.WorldGridMaterial
DefaultLightFunctionMaterialName=/Engine/EngineMaterials/DefaultLightFunctionMaterial.DefaultLightFunctionMaterial
DefaultTextureName=/Engine/EngineResources/DefaultTexture.DefaultTexture
DefaultDiffuseTextureName=/Engine/EngineMaterials/DefaultDiffuse.DefaultDiffuse
DefaultBSPVertexTextureName=/Engine/EditorResources/BSPVertex.BSPVertex
HighFrequencyNoiseTextureName=/Engine/EngineMaterials/Good64x64TilingNoiseHighFreq.Good64x64TilingNoiseHighFreq
DefaultBokehTextureName=/Engine/EngineMaterials/DefaultBokeh.DefaultBokeh
DefaultBloomKernelTextureName=/Engine/EngineMaterials/DefaultBloomKernel.DefaultBloomKernel
DefaultFilmGrainTextureName=/Engine/EngineResources/FilmGrains/Marcie_Grain_v3_128_M2_000.Marcie_Grain_v3_128_M2_000
GeomMaterialName=/Engine/EngineDebugMaterials/GeomMaterial.GeomMaterial
DebugMeshMaterialName=/Engine/EngineDebugMaterials/DebugMeshMaterial.DebugMeshMaterial
EmissiveMeshMaterialName=/Engine/EngineMaterials/EmissiveMeshMaterial.EmissiveMeshMaterial
PreIntegratedSkinBRDFTextureName=/Engine/EngineMaterials/PreintegratedSkinBRDF.PreintegratedSkinBRDF
BlueNoiseScalarTextureName=/Engine/EngineMaterials/FastBlueNoise_scalar_128x128x64.FastBlueNoise_scalar_128x128x64
BlueNoiseVec2TextureName=/Engine/EngineMaterials/FastBlueNoise_vec2_128x128x64.FastBlueNoise_vec2_128x128x64
GGXLTCAmpTextureName=/Engine/EngineMaterials/LTC/GGX_LTCAmp.GGX_LTCAmp
GGXLTCMatTextureName=/Engine/EngineMaterials/LTC/GGX_LTCMat.GGX_LTCMat
SheenLTCTextureName=/Engine/EngineMaterials/LTC/Sheen_LTC.Sheen_LTC
GGXReflectionEnergyTextureName=/Engine/EngineMaterials/EnergyConservation/GGX_ReflectionEnergy.GGX_ReflectionEnergy
GGXTransmissionEnergyTextureName=/Engine/EngineMaterials/EnergyConservation/GGX_TransmissionEnergyArray.GGX_TransmissionEnergyArray
SheenEnergyTextureName=/Engine/EngineMaterials/EnergyConservation/Sheen_Energy.Sheen_Energy
SheenLegacyEnergyTextureName=/Engine/EngineMaterials/EnergyConservation/SheenLegacy_Energy.SheenLegacy_Energy
DiffuseEnergyTextureName=/Engine/EngineMaterials/EnergyConservation/Diffuse_Energy.Diffuse_Energy
GlintTextureName=/Engine/EngineMaterials/Substrate/GLints/AGlintTex2dArray.AGlintTex2dArray
GlintTexture2Name=/Engine/EngineMaterials/Substrate/GLints2/AGlintTex2dArray2.AGlintTex2dArray2
SimpleVolumeTextureName=/Engine/EngineMaterials/Substrate/Volume/SubstrateSimpleVolumeLUT3D.SubstrateSimpleVolumeLUT3D
SimpleVolumeEnvTextureName=/Engine/EngineMaterials/Substrate/Volume/SubstrateSimpleVolumeEnvLUT3D.SubstrateSimpleVolumeEnvLUT3D
MiniFontTextureName=/Engine/EngineMaterials/MiniFont.MiniFont
WeightMapPlaceholderTextureName=/Engine/EngineMaterials/WeightMapPlaceholderTexture.WeightMapPlaceholderTexture
WeightMapArrayPlaceholderTextureName=/Engine/EngineMaterials/WeightMapArrayPlaceholderTexture.WeightMapArrayPlaceholderTexture
LightMapDensityTextureName=/Engine/EngineMaterials/DefaultWhiteGrid.DefaultWhiteGrid
NaniteHiddenSectionMaterialName=/Engine/EngineMaterials/NaniteHiddenSectionMaterial.NaniteHiddenSectionMaterial
LevelColorationLitMaterialName=/Engine/EngineDebugMaterials/LevelColorationLitMaterial.LevelColorationLitMaterial
LevelColorationUnlitMaterialName=/Engine/EngineDebugMaterials/LevelColorationUnlitMaterial.LevelColorationUnlitMaterial
LightingTexelDensityName=/Engine/EngineDebugMaterials/MAT_LevelColorationLitLightmapUV.MAT_LevelColorationLitLightmapUV
ShadedLevelColorationUnlitMaterialName=/Engine/EngineDebugMaterials/ShadedLevelColorationUnlitMateri.ShadedLevelColorationUnlitMateri
ShadedLevelColorationLitMaterialName=/Engine/EngineDebugMaterials/ShadedLevelColorationLitMaterial.ShadedLevelColorationLitMaterial
RemoveSurfaceMaterialName=/Engine/EngineMaterials/RemoveSurfaceMaterial.RemoveSurfaceMaterial
VertexColorMaterialName=/Engine/EngineDebugMaterials/VertexColorMaterial.VertexColorMaterial
VertexColorViewModeMaterialName_ColorOnly=/Engine/EngineDebugMaterials/VertexColorViewMode_ColorOnly.VertexColorViewMode_ColorOnly
VertexColorViewModeMaterialName_AlphaAsColor=/Engine/EngineDebugMaterials/VertexColorViewMode_AlphaAsColor.VertexColorViewMode_AlphaAsColor
VertexColorViewModeMaterialName_RedOnly=/Engine/EngineDebugMaterials/VertexColorViewMode_RedOnly.VertexColorViewMode_RedOnly
VertexColorViewModeMaterialName_GreenOnly=/Engine/EngineDebugMaterials/VertexColorViewMode_GreenOnly.VertexColorViewMode_GreenOnly
VertexColorViewModeMaterialName_BlueOnly=/Engine/EngineDebugMaterials/VertexColorViewMode_BlueOnly.VertexColorViewMode_BlueOnly
TextureColorViewModeMaterialName=/Engine/EngineDebugMaterials/TextureColorViewMode.TextureColorViewMode
PhysicalMaterialMaskMaterialName=/Engine/EngineDebugMaterials/PhysicalMaterialMaskMaterial.PhysicalMaterialMaskMaterial
TexturePaintingMaskMaterialName=/Engine/EngineDebugMaterials/TexturePaintingMaskMaterial.TexturePaintingMaskMaterial
BoneWeightMaterialName=/Engine/EngineDebugMaterials/BoneWeightMaterial.BoneWeightMaterial
ClothPaintMaterialName=/Engine/EngineDebugMaterials/ClothMaterial.ClothMaterial
ClothPaintMaterialWireframeName=/Engine/EngineDebugMaterials/ClothMaterial_WF.ClothMaterial_WF
DebugEditorMaterialName=/Engine/EngineDebugMaterials/DebugEditorMaterial.DebugEditorMaterial
InvalidLightmapSettingsMaterialName=/Engine/EngineMaterials/M_InvalidLightmapSettings.M_InvalidLightmapSettings
PreviewShadowsIndicatorMaterialName=/Engine/EditorMaterials/PreviewShadowIndicatorMaterial.PreviewShadowIndicatorMaterial
EditorBrushMaterialName=/Engine/EngineMaterials/EditorBrushMaterial.EditorBrushMaterial
DefaultPhysMaterialName=/Engine/EngineMaterials/DefaultPhysicalMaterial.DefaultPhysicalMaterial
DefaultDeferredDecalMaterialName=/Engine/EngineMaterials/DefaultDeferredDecalMaterial.DefaultDeferredDecalMaterial
DefaultPostProcessMaterialName=/Engine/EngineMaterials/DefaultPostProcessMaterial.DefaultPostProcessMaterial
DefaultFlattenMaterialName=/Engine/EngineMaterials/FlattenMaterial.FlattenMaterial
DefaultHLODFlattenMaterialName=/Engine/EngineMaterials/FlattenMaterial_VT.FlattenMaterial_VT
DefaultLandscapeFlattenMaterialName=/Engine/EngineMaterials/FlattenMaterial_WS_Normal_VT.FlattenMaterial_WS_Normal_VT
TimecodeProviderClassName=None
ArrowMaterialName=/Engine/EngineMaterials/GizmoMaterial.GizmoMaterial
ParticleEventManagerClassPath="/Script/Engine.ParticleEventManager"
LightingOnlyBrightness=(R=0.3,G=0.3,B=0.3,A=1.0)
+ShaderComplexityColors=(R=0.0,G=1.0,B=0.127,A=1.0)
+ShaderComplexityColors=(R=0.0,G=1.0,B=0.0,A=1.0)
+ShaderComplexityColors=(R=0.046,G=0.52,B=0.0,A=1.0)
+ShaderComplexityColors=(R=0.215,G=0.215,B=0.0,A=1.0)
+ShaderComplexityColors=(R=0.52,G=0.046,B=0.0,A=1.0)
+ShaderComplexityColors=(R=0.7,G=0.0,B=0.0,A=1.0)
+ShaderComplexityColors=(R=1.0,G=0.0,B=0.0,A=1.0)
+ShaderComplexityColors=(R=1.0,G=0.0,B=0.5,A=1.0)
+ShaderComplexityColors=(R=1.0,G=0.9,B=0.9,A=1.0)
+QuadComplexityColors=(R=0.0,G=0.0,B=0.0,A=1.0)
+QuadComplexityColors=(R=0.0,G=0.0,B=0.4,A=1.0)
+QuadComplexityColors=(R=0.0,G=0.3,B=1.0,A=1.0)
+QuadComplexityColors=(R=0.0,G=0.7,B=0.4,A=1.0)
+QuadComplexityColors=(R=0.0,G=1.0,B=0.0,A=1.0)
+QuadComplexityColors=(R=0.8,G=0.8,B=0.0,A=1.0)
+QuadComplexityColors=(R=1.0,G=0.3,B=0.0,A=1.0)
+QuadComplexityColors=(R=0.7,G=0.0,B=0.0,A=1.0)
+QuadComplexityColors=(R=0.5,G=0.0,B=0.5,A=1.0)
+QuadComplexityColors=(R=0.7,G=0.3,B=0.7,A=1.0)
+QuadComplexityColors=(R=1.0,G=0.9,B=0.9,A=1.0)
+LightComplexityColors=(R=0.0,G=0.0,B=0.0,A=1.0)
+LightComplexityColors=(R=0.0,G=0.0,B=0.4,A=1.0)
+LightComplexityColors=(R=0.0,G=0.3,B=1.0,A=1.0)
+LightComplexityColors=(R=0.0,G=0.7,B=0.4,A=1.0)
+LightComplexityColors=(R=0.0,G=1.0,B=0.0,A=1.0)
+LightComplexityColors=(R=0.8,G=0.8,B=0.0,A=1.0)
+LightComplexityColors=(R=1.0,G=0.3,B=0.0,A=1.0)
+LightComplexityColors=(R=0.7,G=0.0,B=0.0,A=1.0)
+LightComplexityColors=(R=0.5,G=0.0,B=0.5,A=1.0)
+LightComplexityColors=(R=0.7,G=0.3,B=0.7,A=1.0)
+LightComplexityColors=(R=1.0,G=0.9,B=0.9,A=1.0)
+StationaryLightOverlapColors=(R=0.0,G=1.0,B=0.127,A=1.0)
+StationaryLightOverlapColors=(R=0.0,G=1.0,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=0.046,G=0.52,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=0.215,G=0.215,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=0.52,G=0.046,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=0.7,G=0.0,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=1.0,G=0.0,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=1.0,G=0.0,B=0.5,A=1.0)
+StationaryLightOverlapColors=(R=1.0,G=0.9,B=0.9,A=1.0)
+LODColorationColors=(R=1.0,G=1.0,B=1.0,A=1.0) ; white (LOD 0)
+LODColorationColors=(R=1.0,G=0.0,B=0.0,A=1.0) ; red (LOD 1)
+LODColorationColors=(R=0.0,G=1.0,B=0.0,A=1.0) ; green (etc...)
+LODColorationColors=(R=0.0,G=0.0,B=1.0,A=1.0) ; blue
+LODColorationColors=(R=1.0,G=1.0,B=0.0,A=1.0) ; yellow
+LODColorationColors=(R=1.0,G=0.0,B=1.0,A=1.0) ; fuchsia (bright purple)
+LODColorationColors=(R=0.0,G=1.0,B=1.0,A=1.0) ; cyan
+LODColorationColors=(R=0.5,G=0.0,B=0.5,A=1.0) ; purple
+StreamingAccuracyColors=(R=1.0,G=0.0,B=0.0,A=1.0)
+StreamingAccuracyColors=(R=0.8,G=0.5,B=0.0,A=1.0)
+StreamingAccuracyColors=(R=0.7,G=0.7,B=0.7,A=1.0)
+StreamingAccuracyColors=(R=0.0,G=0.8,B=0.5,A=1.0)
+StreamingAccuracyColors=(R=0.0,G=1.0,B=0.0,A=1.0)
+HLODColorationColors=(R=1.0,G=1.0,B=1.0,A=1.0) ; white (not part of HLOD)
+HLODColorationColors=(R=0.0,G=1.0,B=0.0,A=1.0) ; green (part of HLOD but being drawn outside of it)
+HLODColorationColors=(R=0.0,G=0.0,B=1.0,A=1.0) ; blue (HLOD level 0)
+HLODColorationColors=(R=1.0,G=1.0,B=0.0,A=1.0) ; yellow (HLOD level 1, etc...)
+HLODColorationColors=(R=1.0,G=0.0,B=1.0,A=1.0) ; purple
+HLODColorationColors=(R=0.0,G=1.0,B=1.0,A=1.0) ; cyan
+HLODColorationColors=(R=0.5,G=0.5,B=0.5,A=1.0) ; grey
GPUSkinCacheVisualizationExcludedColor=(R=1.0,G=0.0,B=0.0,A=1.0) ; red (not use skin cache on sk mesh)
GPUSkinCacheVisualizationIncludedColor=(R=0.0,G=1.0,B=0.0,A=1.0) ; green (use skin cache on sk mesh)
GPUSkinCacheVisualizationRecomputeTangentsColor=(R=0.0,G=0.0,B=1.0,A=1.0) ; blue (use recompute tangents on sk mesh)
GPUSkinCacheVisualizationLowMemoryThresholdInMB=2.0
GPUSkinCacheVisualizationHighMemoryThresholdInMB=10.0
GPUSkinCacheVisualizationLowMemoryColor=(R=0.0,G=1.0,B=0.0,A=1.0) ; green
GPUSkinCacheVisualizationMidMemoryColor=(R=1.0,G=1.0,B=0.0,A=1.0) ; yellow
GPUSkinCacheVisualizationHighMemoryColor=(R=1.0,G=0.0,B=0.0,A=1.0) ; red
+GPUSkinCacheVisualizationRayTracingLODOffsetColors=(R=1.0,G=1.0,B=0.0,A=1.0) ; yellow (RT_LOD == Raster_LOD)
+GPUSkinCacheVisualizationRayTracingLODOffsetColors=(R=1.0,G=0.0,B=0.0,A=1.0) ; red (RT_LOD == Raster_LOD + 1)
+GPUSkinCacheVisualizationRayTracingLODOffsetColors=(R=0.0,G=1.0,B=0.0,A=1.0) ; green (RT_LOD == Raster_LOD + 2)
+GPUSkinCacheVisualizationRayTracingLODOffsetColors=(R=0.0,G=0.0,B=1.0,A=1.0) ; blue (RT_LOD >= Raster_LOD + 3)
MaxPixelShaderAdditiveComplexityCount=2000
MaxES3PixelShaderAdditiveComplexityCount=800
bSubtitlesEnabled=True
bSubtitlesForcedOff=false
MaximumLoopIterationCount = 1000000
bCanBlueprintsTickByDefault=true
bOptimizeAnimBlueprintMemberVariableAccess=true
CameraRotationThreshold=45.0
CameraTranslationThreshold=10000
PrimitiveProbablyVisibleTime = 8.0
MaxOcclusionPixelsFraction = 0.1
MinLightMapDensity=0.0
IdealLightMapDensity=0.2
MaxLightMapDensity=0.8
RenderLightMapDensityGrayscaleScale=1.0
RenderLightMapDensityColorScale=1.0
bRenderLightMapDensityGrayscale=false
LightMapDensityVertexMappedColor=(R=0.65,G=0.65,B=0.25,A=1.0)
LightMapDensitySelectedColor=(R=1.0,G=0.2,B=1.0,A=1.0)
bPauseOnLossOfFocus=false
MaxParticleResize=0
MaxParticleResizeWarn=0
NetClientTicksPerSecond=200
+StatColorMappings=(StatName="AverageFPS",ColorMap=((In=15.0,Out=(R=255)),(In=30,Out=(R=255,G=255)),(In=45.0,Out=(G=255))))
+StatColorMappings=(StatName="Frametime",ColorMap=((In=1.0,Out=(G=255)),(In=25.0,Out=(G=255)),(In=29.0,Out=(R=255,G=255)),(In=33.0,Out=(R=255))))
+StatColorMappings=(StatName="Streaming fudge factor",ColorMap=((In=0.0,Out=(G=255)),(In=1.0,Out=(G=255)),(In=2.5,Out=(R=255,G=255)),(In=5.0,Out=(R=255)),(In=10.0,Out=(R=255))))
DisplayGamma=2.2
MinDesiredFrameRate=35.000000
+NetDriverDefinitions=(DefName="GameNetDriver",DriverClassName="/Script/OnlineSubsystemUtils.IpNetDriver",DriverClassNameFallback="/Script/OnlineSubsystemUtils.IpNetDriver")
+NetDriverDefinitions=(DefName="BeaconNetDriver",DriverClassName="/Script/OnlineSubsystemUtils.IpNetDriver",DriverClassNameFallback="/Script/OnlineSubsystemUtils.IpNetDriver")
+NetDriverDefinitions=(DefName="DemoNetDriver",DriverClassName="/Script/Engine.DemoNetDriver",DriverClassNameFallback="/Script/Engine.DemoNetDriver")
+IrisNetDriverConfigs=(NetDriverDefinition=GameNetDriver, bCanUseIris=true)
+IrisNetDriverConfigs=(NetDriverName=DemoNetDriver, bCanUseIris=false)
NetErrorLogInterval=1.0
SerializationOutOfBoundsErrorMessage=NSLOCTEXT("","SerializationOutOfBoundsErrorMessage","Corrupt data found, please verify your installation.")
SerializationOutOfBoundsErrorMessageCaption=NSLOCTEXT("","SerializationOutOfBoundsErrorMessageCaption","Serialization Error : Action Needed")
bSmoothFrameRate=false
SmoothedFrameRateRange=(LowerBound=(Type="ERangeBoundTypes::Inclusive",Value=22),UpperBound=(Type="ERangeBoundTypes::Exclusive",Value=62))
bCheckForMultiplePawnsSpawnedInAFrame=false
NumPawnsAllowedToBeSpawnedInAFrame=2
DefaultSelectedMaterialColor=(R=0.84,G=0.92,B=0.02,A=1.0)
bEnableOnScreenDebugMessages=true
DurationOfErrorsAndWarningsOnHUD=0
NearClipPlane=10.0
DefaultWorldPartitionHLODLayer=/Engine/Maps/Templates/HLODs/HLODLayer_Instanced.HLODLayer_Instanced

[/Script/Engine.OnlineEngineInterface]
ClassName=/Script/OnlineSubsystemUtils.OnlineEngineInterfaceImpl

[CoreRedirects]



+ClassRedirects=(OldName="AnimGraphNode_BlendSpace",NewName="/Script/AnimGraph.AnimGraphNode_BlendSpacePlayer")
+ClassRedirects=(OldName="AnimNotify_PlayParticleEffect_C",NewName="/Script/Engine.AnimNotify_PlayParticleEffect",OverrideClassName="/Script/CoreUObject.Class")
+ClassRedirects=(OldName="AnimNotify_PlaySound_C",NewName="/Script/Engine.AnimNotify_PlaySound",OverrideClassName="/Script/CoreUObject.Class")
+ClassRedirects=(OldName="MovieSceneMaterialParameterSection",NewName="/Script/MovieSceneTracks.MovieSceneParameterSection")

+PackageRedirects=(OldName="/Engine/EngineAnimNotifies/AnimNotify_PlayParticleEffect",Removed=True)
+PackageRedirects=(OldName="/Engine/EngineAnimNotifies/AnimNotify_PlaySound",Removed=True)

+StructRedirects=(OldName="AnimNode_ApplyAdditive",NewName="/Script/AnimGraphRuntime.AnimNode_ApplyAdditive")
+StructRedirects=(OldName="AnimNode_BlendListBase",NewName="/Script/AnimGraphRuntime.AnimNode_BlendListBase")
+StructRedirects=(OldName="AnimNode_BlendListByBool",NewName="/Script/AnimGraphRuntime.AnimNode_BlendListByBool")
+StructRedirects=(OldName="AnimNode_BlendListByEnum",NewName="/Script/AnimGraphRuntime.AnimNode_BlendListByEnum")
+StructRedirects=(OldName="AnimNode_BlendListByInt",NewName="/Script/AnimGraphRuntime.AnimNode_BlendListByInt")
+StructRedirects=(OldName="AnimNode_BlendSpace",NewName="/Script/AnimGraphRuntime.AnimNode_BlendSpacePlayer")
+StructRedirects=(OldName="AnimNode_BlendSpaceEvaluator",NewName="/Script/AnimGraphRuntime.AnimNode_BlendSpaceEvaluator")
+StructRedirects=(OldName="AnimNode_BlendSpacePlayer",NewName="/Script/AnimGraphRuntime.AnimNode_BlendSpacePlayer")
+StructRedirects=(OldName="AnimNode_LayeredBoneBlend",NewName="/Script/AnimGraphRuntime.AnimNode_LayeredBoneBlend")
+StructRedirects=(OldName="AnimNode_MeshSpaceRefPose",NewName="/Script/AnimGraphRuntime.AnimNode_MeshSpaceRefPose")
+StructRedirects=(OldName="AnimNode_RefPose",NewName="/Script/AnimGraphRuntime.AnimNode_RefPose")
+StructRedirects=(OldName="AnimNode_RotateRootBone",NewName="/Script/AnimGraphRuntime.AnimNode_RotateRootBone")
+StructRedirects=(OldName="AnimNode_RotationOffsetBlendSpace",NewName="/Script/AnimGraphRuntime.AnimNode_RotationOffsetBlendSpace")
+StructRedirects=(OldName="AnimNode_SequenceEvaluator",NewName="/Script/AnimGraphRuntime.AnimNode_SequenceEvaluator")
+StructRedirects=(OldName="AnimNode_Slot",NewName="/Script/AnimGraphRuntime.AnimNode_Slot")
+StructRedirects=(OldName="FormatTextArgument",NewName="/Script/Engine.FormatArgumentData")

+StructRedirects=(OldName="ChaosBreakEvent",NewName="/Script/Engine.ChaosEventType.ChaosBreakEvent")
+StructRedirects=(OldName="ChaosRemovalEvent",NewName="/Script/Engine.ChaosEventType.ChaosRemovalEvent")
+StructRedirects=(OldName="ChaosCrumblingEvent",NewName="/Script/Engine.ChaosEventType.ChaosCrumblingEvent")

+FunctionRedirects=(OldName="ConvertTransformToRelative",NewName="/Script/Engine.KismetMathLibrary.MakeRelativeTransform")
+PropertyRedirects=(OldName="KismetMathLibrary.MakeRelativeTransform.LocalTransform",NewName="A")
+PropertyRedirects=(OldName="KismetMathLibrary.MakeRelativeTransform.WorldTransform",NewName="RelativeTo")

+PropertyRedirects=(OldName="KismetMathLibrary.MakeRelativeTransform.ParentTransform",NewName="A")
+PropertyRedirects=(OldName="KismetMathLibrary.MakeRelativeTransform.Transform",NewName="RelativeTo")

+PropertyRedirects=(OldName="MaterialInstanceDynamic.K2_CopyMaterialInstanceParameters.SourceMaterialToCopyFrom",NewName="Source")
+PropertyRedirects=(OldName="MaterialInstanceDynamic.K2_InterpolateMaterialInstanceParams.MaterialA",NewName="SourceA")
+PropertyRedirects=(OldName="MaterialInstanceDynamic.K2_InterpolateMaterialInstanceParams.MaterialB",NewName="SourceB")
+PropertyRedirects=(OldName="MaterialInstanceDynamic.K2_InterpolateMaterialInstanceParams.MaterialInstanceA",NewName="SourceA")
+PropertyRedirects=(OldName="MaterialInstanceDynamic.K2_InterpolateMaterialInstanceParams.MaterialInstanceB",NewName="SourceB")

+PropertyRedirects=(OldName="AnimNode_Trail.TrailRelaxationCurve",NewName="AnimNode_Trail.TrailRelaxationSpeed")
+PropertyRedirects=(OldName="FormatArgumentData.TextValue",NewName="FormatArgumentData.ArgumentValue")
+PropertyRedirects=(OldName="LandscapeSplineMeshEntry.Offset",NewName="LandscapeSplineMeshEntry.CenterAdjust")
+PropertyRedirects=(OldName="MovieScenePossessable.ParentSpawnableGuid",NewName="MovieScenePossessable.ParentGuid")
+PropertyRedirects=(OldName="MultiLineEditableText.bAutoWrapText",NewName="MultiLineEditableText.AutoWrapText")
+PropertyRedirects=(OldName="MultiLineEditableTextBox.bAutoWrapText",NewName="MultiLineEditableTextBox.AutoWrapText")
+PropertyRedirects=(OldName="AnimNode_SequenceEvaluator.bShouldLoopWhenInSyncGroup",NewName="AnimNode_SequenceEvaluator.bShouldLoop")

+PropertyRedirects=(OldName="PoseSearchDatabase.Weights",NewName="PoseSearchDatabase.DefaultWeights")

+EnumRedirects=(OldName="EControllerHand",NewName="/Script/InputCore.EControllerHand")
+EnumRedirects=(OldName="AnimPhysConstraintType",NewName="/Script/AnimGraphRuntime.AnimPhysAngularConstraintType")
+EnumRedirects=(OldName="AnimPhysAxisType",NewName="/Script/AnimGraphRuntime.AnimPhysLinearConstraintType")
+EnumRedirects=(OldName="EEnvQueryParam",NewName="/Script/AIModule.EAIParamType")


+ClassRedirects=(OldName="CineCameraActor",NewName="/Script/CinematicCamera.CineCameraActor")
+ClassRedirects=(OldName="CineCameraComponent",NewName="/Script/CinematicCamera.CineCameraComponent")
+ClassRedirects=(OldName="MovieSceneShotSection",NewName="/Script/MovieSceneTracks.MovieSceneCameraCutSection")
+ClassRedirects=(OldName="MovieSceneShotTrack",NewName="/Script/MovieSceneTracks.MovieSceneCameraCutTrack")

+FunctionRedirects=(OldName="Actor.SetActorRotation",NewName="Actor.K2_SetActorRotation")
+FunctionRedirects=(OldName="KismetSystemLibrary.SetSupressViewportTransitionMessage",NewName="KismetSystemLibrary.SetSuppressViewportTransitionMessage")
+FunctionRedirects=(OldName="SteamVRFunctionLibrary.GetTrackingSpace",NewName="HeadMountedDisplayFunctionLibrary.GetTrackingOrigin")
+FunctionRedirects=(OldName="SteamVRFunctionLibrary.SetTrackingSpace",NewName="HeadMountedDisplayFunctionLibrary.SetTrackingOrigin")

+PropertyRedirects=(OldName="AudioEQEffect.HFFrequency",NewName="AudioEQEffect.FrequencyCenter2")
+PropertyRedirects=(OldName="AudioEQEffect.HFGain",NewName="AudioEQEffect.Gain2")
+PropertyRedirects=(OldName="AudioEQEffect.LFFrequency",NewName="AudioEQEffect.FrequencyCenter0")
+PropertyRedirects=(OldName="AudioEQEffect.LFGain",NewName="AudioEQEffect.Gain0")
+PropertyRedirects=(OldName="AudioEQEffect.MFBandwidth",NewName="AudioEQEffect.Bandwidth1")
+PropertyRedirects=(OldName="AudioEQEffect.MFCutoffFrequency",NewName="AudioEQEffect.FrequencyCenter1")
+PropertyRedirects=(OldName="AudioEQEffect.MFGain",NewName="AudioEQEffect.Gain1")
+PropertyRedirects=(OldName="BodyInstance.MassInKg",NewName="BodyInstance.MassInKgOverride")
+PropertyRedirects=(OldName="EnvQueryTest.SweetSpotValue",NewName="EnvQueryTest.ReferenceValue")
+PropertyRedirects=(OldName="EnvQueryTest.bDefineSweetSpot",NewName="EnvQueryTest.bDefineReferenceValue")
+PropertyRedirects=(OldName="MovieScene.ShotTrack",NewName="MovieScene.CameraCutTrack")
+PropertyRedirects=(OldName="MovieSceneShotSection.ShotNumber",NewName="MovieSceneShotSection.CameraCutNumber")


+ClassRedirects=(OldName="EdGraphPin",NewName="/Script/Engine.EdGraphPin_Deprecated")
+ClassRedirects=(OldName="HapticFeedbackEffect",NewName="/Script/Engine.HapticFeedbackEffect_Curve")
+ClassRedirects=(OldName="LandscapeProxy",NewName="/Script/Landscape.LandscapeStreamingProxy",InstanceOnly=True)

+StructRedirects=(OldName="HapticFeedbackDetails",NewName="/Script/Engine.HapticFeedbackDetails_Curve")
+StructRedirects=(OldName="AnimNode_SaveCachedPose",NewName="/Script/Engine.AnimNode_SaveCachedPose")

+FunctionRedirects=(OldName="SceneCaptureComponent2D.UpdateContent",NewName="SceneCaptureComponent2D.CaptureScene")
+FunctionRedirects=(OldName="SceneCaptureComponentCube.UpdateContent",NewName="SceneCaptureComponentCube.CaptureScene")

+PropertyRedirects=(OldName="Blueprint.PinWatches",NewName="Blueprint.DeprecatedPinWatches")
+PropertyRedirects=(OldName="Box2D.bIsValid",NewName="Box2D.IsValid")
+PropertyRedirects=(OldName="EdGraphNode.Pins",NewName="EdGraphNode.DeprecatedPins")
+PropertyRedirects=(OldName="PhysicsAsset.Profiles",NewName="PhysicsAsset.PhysicalAnimationProfiles")
+PropertyRedirects=(OldName="PrimitiveComponent.bReceiveCSMFromDynamicObjects",NewName="PrimitiveComponent.bReceiveCombinedCSMAndStaticShadowsFromStationaryLights")
+PropertyRedirects=(OldName="SplineComponent.bAlwaysRenderInEditor",NewName="SplineComponent.bDrawDebug")

+EnumRedirects=(OldName="ENoiseFunction",ValueChanges=(("NOISEFUNCTION_FastGradient","NOISEFUNCTION_GradientTex3D"), ("NOISEFUNCTION_Gradient","NOISEFUNCTION_ValueALU"), ("NOISEFUNCTION_Perlin","NOISEFUNCTION_GradientTex"), ("NOISEFUNCTION_Simplex","NOISEFUNCTION_SimplexTex")) )
+EnumRedirects=(OldName="EPathFollowingResult",ValueChanges=(("EPathFollowingResult::Skipped","EPathFollowingResult::Skipped_DEPRECATED")) )
+EnumRedirects=(OldName="EStereoLayerType",ValueChanges=(("EStereoLayerType::SLT_TorsoLocked","EStereoLayerType::SLT_TrackerLocked")) )


+ClassRedirects=(OldName="AnimGraphNode_OrientationDriver",NewName="/Script/AnimGraph.AnimGraphNode_PoseDriver")
+ClassRedirects=(OldName="K2Node_AIMoveTo",NewName="/Script/AIGraph.K2Node_AIMoveTo")
+StructRedirects=(OldName="AnimNode_OrientationDriver",NewName="/Script/AnimGraphRuntime.AnimNode_PoseDriver")

+FunctionRedirects=(OldName="KismetMathLibrary.GetDirectionVector",NewName="GetDirectionUnitVector")

+PropertyRedirects=(OldName="SCS_Node.VariableName",NewName="SCS_Node.InternalVariableName")

+EnumRedirects=(OldName="ESuggestProjVelocityTraceOption",ValueChanges=(("OnlyTraceWhileAsceding","OnlyTraceWhileAscending")) )


+ClassRedirects=(OldName="BackgroundBlurWidget",NewName="/Script/UMG.BackgroundBlur")
+ClassRedirects=(OldName="SoundClassGraph",NewName="/Script/AudioEditor.SoundClassGraph")
+ClassRedirects=(OldName="SoundClassGraphNode",NewName="/Script/AudioEditor.SoundClassGraphNode")
+ClassRedirects=(OldName="SoundClassGraphSchema",NewName="/Script/AudioEditor.SoundClassGraphSchema")
+ClassRedirects=(OldName="SoundCueGraph",NewName="/Script/AudioEditor.SoundCueGraph")
+ClassRedirects=(OldName="SoundCueGraphNode",NewName="/Script/AudioEditor.SoundCueGraphNode")
+ClassRedirects=(OldName="SoundCueGraphNode_Base",NewName="/Script/AudioEditor.SoundCueGraphNode_Base")
+ClassRedirects=(OldName="SoundCueGraphNode_Root",NewName="/Script/AudioEditor.SoundCueGraphNode_Root")
+ClassRedirects=(OldName="SoundCueGraphSchema",NewName="/Script/AudioEditor.SoundCueGraphSchema")

+StructRedirects=(OldName="AnimationNode_TwoWayBlend",NewName="/Script/AnimGraphRuntime.AnimNode_TwoWayBlend")
+StructRedirects=(OldName="AttenuationSettings",NewName="/Script/Engine.SoundAttenuationSettings")
+StructRedirects=(OldName="LevelSequencePlaybackSettings",NewName="/Script/MovieScene.MovieSceneSequencePlaybackSettings")

+FunctionRedirects=(OldName="BlueprintGameplayTagLibrary.DoGameplayTagsMatch",NewName="BlueprintGameplayTagLibrary.MatchesTag")
+FunctionRedirects=(OldName="BlueprintGameplayTagLibrary.DoesContainerHaveTag",NewName="BlueprintGameplayTagLibrary.HasTag")
+FunctionRedirects=(OldName="BlueprintGameplayTagLibrary.DoesContainerMatchAllTagsInContainer",NewName="BlueprintGameplayTagLibrary.HasAllTags")
+FunctionRedirects=(OldName="BlueprintGameplayTagLibrary.DoesContainerMatchAnyTagsInContainer",NewName="BlueprintGameplayTagLibrary.HasAnyTags")
+PropertyRedirects=(OldName="BlueprintGameplayTagLibrary.IsGameplayTagValid.TagContainer",NewName="GameplayTag")
+FunctionRedirects=(OldName="BlueprintGameplayTagLibrary.AddGameplayTagToContainer",NewName="BlueprintGameplayTagLibrary.AddGameplayTag")
+PropertyRedirects=(OldName="BlueprintGameplayTagLibrary.AddGameplayTag.InOutTagContainer",NewName="TagContainer")
+FunctionRedirects=(OldName="GameplayStatics.PredictProjectilePath",NewName="GameplayStatics.Blueprint_PredictProjectilePath_ByObjectType")
+FunctionRedirects=(OldName="KismetSystemLibrary.BoxOverlapActors_NEW",NewName="KismetSystemLibrary.BoxOverlapActors")
+FunctionRedirects=(OldName="KismetSystemLibrary.BoxOverlapComponents_NEW",NewName="KismetSystemLibrary.BoxOverlapComponents")
+FunctionRedirects=(OldName="KismetSystemLibrary.CapsuleOverlapActors_NEW",NewName="KismetSystemLibrary.CapsuleOverlapActors")
+FunctionRedirects=(OldName="KismetSystemLibrary.CapsuleOverlapComponents_NEW",NewName="KismetSystemLibrary.CapsuleOverlapComponents")
+FunctionRedirects=(OldName="KismetSystemLibrary.CapsuleTraceMulti_NEW",NewName="KismetSystemLibrary.CapsuleTraceMulti")
+FunctionRedirects=(OldName="KismetSystemLibrary.CapsuleTraceSingle_NEW",NewName="KismetSystemLibrary.CapsuleTraceSingle")
+FunctionRedirects=(OldName="KismetSystemLibrary.ComponentOverlapActors_NEW",NewName="KismetSystemLibrary.ComponentOverlapActors")
+FunctionRedirects=(OldName="KismetSystemLibrary.ComponentOverlapComponents_NEW",NewName="KismetSystemLibrary.ComponentOverlapComponents")
+FunctionRedirects=(OldName="KismetSystemLibrary.LineTraceMulti_NEW",NewName="KismetSystemLibrary.LineTraceMulti")
+FunctionRedirects=(OldName="KismetSystemLibrary.LineTraceSingle_NEW",NewName="KismetSystemLibrary.LineTraceSingle")
+FunctionRedirects=(OldName="KismetSystemLibrary.SphereOverlapActors_NEW",NewName="KismetSystemLibrary.SphereOverlapActors")
+FunctionRedirects=(OldName="KismetSystemLibrary.SphereOverlapComponents_NEW",NewName="KismetSystemLibrary.SphereOverlapComponents")
+FunctionRedirects=(OldName="KismetSystemLibrary.SphereTraceMulti_NEW",NewName="KismetSystemLibrary.SphereTraceMulti")
+FunctionRedirects=(OldName="KismetSystemLibrary.SphereTraceSingle_NEW",NewName="KismetSystemLibrary.SphereTraceSingle")

+PropertyRedirects=(OldName="MediaPlayer.Seek.InTime",NewName="Time")
+PropertyRedirects=(OldName="MediaPlayer.SetLooping.InLooping",NewName="Looping")

+EnumRedirects=(OldName="EFontLoadingPolicy",ValueChanges=(("EFontLoadingPolicy::PreLoad","EFontLoadingPolicy::LazyLoad")) )
+EnumRedirects=(OldName="ESoundDistanceModel",NewName="/Script/Engine.EAttenuationDistanceModel",ValueChanges=(("ATTENUATION_Custom","EAttenuationDistanceModel::Custom"),("ATTENUATION_Inverse","EAttenuationDistanceModel::Inverse"),("ATTENUATION_Linear","EAttenuationDistanceModel::Linear"),("ATTENUATION_LogReverse","EAttenuationDistanceModel::LogReverse"),("ATTENUATION_Logarithmic","EAttenuationDistanceModel::Logarithmic"),("ATTENUATION_NaturalSound","EAttenuationDistanceModel::NaturalSound")) )


+StructRedirects=(OldName="ClothingAssetData",NewName="/Script/Engine.ClothingAssetData_Legacy")
+StructRedirects=(OldName="ClothPhysicsProperties",NewName="/Script/Engine.ClothPhysicsProperties_Legacy")

+StructRedirects=(OldName="MovieSceneObjectBindingPtr",NewName="/Script/MovieScene.MovieSceneObjectBindingID")

+PropertyRedirects=(OldName="Box2D.IsValid",NewName="bIsValid")


+PropertyRedirects=(OldName="StaticMesh.bRequiresAreaWeightedSampling",NewName="StaticMesh.bSupportUniformlyDistributedSampling")
+PropertyRedirects=(OldName="FPostProcessSettings.BloomConvolutionPreFilter", NewName="FPostProcessSettings.BloomConvolutionPreFilter_DEPRECATED")
+PropertyRedirects=(OldName="FPostProcessSettings.bOverride_BloomConvolutionPreFilter", NewName="FPostProcessSettings.bOverride_BloomConvolutionPreFilter_DEPRECATED")


+ClassRedirects=(OldName="ARBlueprintFunctionLibrary", NewName="/Script/AugmentedReality.ARBlueprintLibrary")

+EnumRedirects=(OldName="EWidgetClipping",ValueChanges=(("EWidgetClipping::No","EWidgetClipping::Inherit"),("EWidgetClipping::Yes","EWidgetClipping::ClipToBounds"),("EWidgetClipping::YesWithoutIntersecting","EWidgetClipping::ClipToBoundsWithoutIntersecting"),("EWidgetClipping::YesAlways","EWidgetClipping::ClipToBoundsAlways")) )
+EnumRedirects=(OldName="EOrientPositionSelector",NewName="/Script/HeadMountedDisplay.EOrientPositionSelector")
+EnumRedirects=(OldName="EHMDTrackingOrigin",NewName="/Script/HeadMountedDisplay.EHMDTrackingOrigin")
+EnumRedirects=(OldName="ESteamVRTrackingSpace",NewName="/Script/HeadMountedDisplay.EHMDTrackingOrigin")
+EnumRedirects=(OldName="EHMDWornState",NewName="/Script/HeadMountedDisplay.EHMDWornState")
+EnumRedirects=(OldName="ESocialScreenModes",NewName="/Script/HeadMountedDisplay.ESpectatorScreenMode",ValueChanges=(("SystemMirror","ESpectatorScreenMode::SingleEyeCroppedToFill"),("SeparateTest","ESpectatorScreenMode::Undistorted"),("SeparateTexture","ESpectatorScreenMode::Texture"),("SeparateSoftwareMirror","ESpectatorScreenMode::Undistorted")) )
+FunctionRedirects=(OldName="SetSocialScreenMode",NewName="/Script/HeadMountedDisplay.HeadMountedDisplayFunctionLibrary.SetSpectatorScreenMode")
+FunctionRedirects=(OldName="SetSocialScreenTexture",NewName="/Script/HeadMountedDisplay.HeadMountedDisplayFunctionLibrary.SetSpectatorScreenTexture")
+PropertyRedirects=(OldName="Widget.ClipToBounds",NewName="Clipping")


+StructRedirects=(OldName="TargetReference",NewName="/Script/Engine.BoneSocketTarget")

+StructRedirects=(OldName="StringAssetReference",NewName="/Script/CoreUObject.SoftObjectPath")
+StructRedirects=(OldName="StringClassReference",NewName="/Script/CoreUObject.SoftClassPath")
+ClassRedirects=(OldName="AssetObjectProperty",NewName="/Script/CoreUObject.SoftObjectProperty")
+ClassRedirects=(OldName="AssetClassProperty",NewName="/Script/CoreUObject.SoftClassProperty")
+FunctionRedirects=(OldName="MakeStringAssetReference",NewName="/Script/Engine.KismetSystemLibrary.MakeSoftObjectPath")
+PropertyRedirects=(OldName="MakeSoftObjectPath.AssetLongPathname",NewName="PathString")
+FunctionRedirects=(OldName="SetAssetPropertyByName",NewName="/Script/Engine.KismetSystemLibrary.SetSoftObjectPropertyByName")
+FunctionRedirects=(OldName="SetAssetClassPropertyByName",NewName="/Script/Engine.KismetSystemLibrary.SetSoftClassPropertyByName")

+FunctionRedirects=(OldName="RandomUnitVectorInCone",NewName="/Script/Engine.KismetMathLibrary.RandomUnitVectorInConeInRadians")
+FunctionRedirects=(OldName="RandomUnitVectorInConeWithYawAndPitch",NewName="/Script/Engine.KismetMathLibrary.RandomUnitVectorInEllipticalConeInDegrees")
+PropertyRedirects=(OldName="KismetMathLibrary.RandomUnitVectorInConeInRadians.ConeHalfAngle",NewName="ConeHalfAngleInRadians")

+PropertyRedirects=(OldName="KismetMathLibrary.RandomPointInBoundingBox.Origin",NewName="Center")
+PropertyRedirects=(OldName="KismetMathLibrary.RandomPointInBoundingBox.BoxExtent",NewName="HalfSize")

+PropertyRedirects=(OldName="Widget.Visiblity",NewName="Widget.Visibility")
+PropertyRedirects=(OldName="WidgetBlueprintLibrary.SetInputMode_UIOnlyEx.Target",NewName="PlayerController")
+PropertyRedirects=(OldName="WidgetBlueprintLibrary.SetInputMode_GameAndUIEx.Target",NewName="PlayerController")
+PropertyRedirects=(OldName="WidgetBlueprintLibrary.SetInputMode_GameOnly.Target",NewName="PlayerController")

+PropertyRedirects=(OldName="FScalarParameterValue.ParameterName", NewName="FScalarParameterValue.ParameterName_DEPRECATED")
+PropertyRedirects=(OldName="FVectorParameterValue.ParameterName", NewName="FVectorParameterValue.ParameterName_DEPRECATED")
+PropertyRedirects=(OldName="FTextureParameterValue.ParameterName", NewName="FTextureParameterValue.ParameterName_DEPRECATED")
+PropertyRedirects=(OldName="FFontParameterValue.ParameterName", NewName="FFontParameterValue.ParameterName_DEPRECATED")

+ClassRedirects=(OldName="/Script/MovieSceneTracks.MovieSceneSubTrack",NewName="/Script/MovieScene.MovieSceneSubTrack")
+ClassRedirects=(OldName="/Script/MovieSceneTracks.MovieSceneSubSection",NewName="/Script/MovieScene.MovieSceneSubSection")
+FunctionRedirects=(OldName="InverseLerp",NewName="/Script/Engine.KismetMathLibrary.NormalizeToRange")
+PropertyRedirects=(OldName="NormalizeToRange.A",NewName="RangeMin")
+PropertyRedirects=(OldName="NormalizeToRange.B",NewName="RangeMax")
+ClassRedirects=(OldName="WebBrowserTexture",NewName="/Script/WebBrowserTexture.WebBrowserTexture")

+PropertyRedirects=(OldName="Widget.Opacity", NewName="Widget.RenderOpacity")
+FunctionRedirects=(OldName="Widget.GetOpacity", NewName="Widget.GetRenderOpacity")
+FunctionRedirects=(OldName="Widget.SetOpacity", NewName="Widget.SetRenderOpacity")
+EnumRedirects=(OldName="ENetDormancy",ValueChanges=(("DORN_MAX","DORM_MAX")))

+PackageRedirects=(OldName="/Script/EditorScriptingUtilitiesEditor", NewName="/Script/AssetScriptingUtilitiesEditor")

+PropertyRedirects=(OldName="PrimitiveComponent.GetOverlappingComponents.InOverlappingComponents",NewName="OutOverlappingComponents")


+ClassRedirects=(OldName="USkeletalMeshReductionSettings",NewName="/Script/Engine.SkeletalMeshLODSettings")
+PropertyRedirects=(OldName="SkeletalMeshLODGroupSettings.OptimizationSettings", NewName="ReductionSettings")
+PropertyRedirects=(OldName="SkeletalMeshLODSettings.Settings", NewName="LODGroups")

+FunctionRedirects=(OldName="/Script/HeadMountedDisplay.HeadMountedDisplayFunctionLibrary.AddDeviceVisualizationComponent",NewName="/Script/HeadMountedDisplay.XRAssetFunctionLibrary.AddDeviceVisualizationComponentBlocking")
+FunctionRedirects=(OldName="/Script/HeadMountedDisplay.HeadMountedDisplayFunctionLibrary.AddNamedDeviceVisualizationComponent",NewName="/Script/HeadMountedDisplay.XRAssetFunctionLibrary.AddNamedDeviceVisualizationComponentBlocking")
+EnumRedirects=(OldName="ESimulationSpace",ValueChanges=(("RootBoneSpace", "BaseBoneSpace")))
+EnumRedirects=(OldName="EColorVisionDeficiency", NewName="/Script/SlateCore.EColorVisionDeficiency", ValueChanges=(("CVD_NormalVision", "NormalVision"), ("CVD_Deuteranomly", "NormalVision"), ("CVD_Deuteranopia", "Deuteranope"), ("CVD_Protanomly", "NormalVision"), ("CVD_Protanopia", "Protanope"), ("CVD_Tritanomaly", "NormalVision"), ("CVD_Tritanopia", "Tritanope"), ("CVD_Achromatopsia", "NormalVision")))

+ClassRedirects=(OldName="NavigationSystem",NewName="/Script/NavigationSystem.NavigationSystemV1")
+ClassRedirects=(OldName="NavMeshBoundsVolume",NewName="/Script/NavigationSystem.NavMeshBoundsVolume")
+ClassRedirects=(OldName="NavArea",NewName="/Script/NavigationSystem.NavArea")
+ClassRedirects=(OldName="NavAreaMeta",NewName="/Script/NavigationSystem.NavAreaMeta")
+ClassRedirects=(OldName="NavArea_Default",NewName="/Script/NavigationSystem.NavArea_Default")
+ClassRedirects=(OldName="NavArea_LowHeight",NewName="/Script/NavigationSystem.NavArea_LowHeight")
+ClassRedirects=(OldName="NavArea_Null",NewName="/Script/NavigationSystem.NavArea_Null")
+ClassRedirects=(OldName="NavArea_Obstacle",NewName="/Script/NavigationSystem.NavArea_Obstacle")
+ClassRedirects=(OldName="NavAreaMeta_SwitchByAgent",NewName="/Script/NavigationSystem.NavAreaMeta_SwitchByAgent")
+ClassRedirects=(OldName="NavigationQueryFilter",NewName="/Script/NavigationSystem.NavigationQueryFilter")
+ClassRedirects=(OldName="NavMeshRenderingComponent",NewName="/Script/NavigationSystem.NavMeshRenderingComponent")
+ClassRedirects=(OldName="RecastNavMesh",NewName="/Script/NavigationSystem.RecastNavMesh")
+ClassRedirects=(OldName="RecastNavMeshDataChunk",NewName="/Script/NavigationSystem.RecastNavMeshDataChunk")
+ClassRedirects=(OldName="AbstractNavData",NewName="/Script/NavigationSystem.AbstractNavData")
+ClassRedirects=(OldName="CrowdManagerBase",NewName="/Script/NavigationSystem.CrowdManagerBase")
+ClassRedirects=(OldName="NavCollision",NewName="/Script/NavigationSystem.NavCollision")
+ClassRedirects=(OldName="NavigationData",NewName="/Script/NavigationSystem.NavigationData")
+ClassRedirects=(OldName="NavigationInvokerComponent",NewName="/Script/NavigationSystem.NavigationInvokerComponent")
+ClassRedirects=(OldName="NavigationPath",NewName="/Script/NavigationSystem.NavigationPath")
+ClassRedirects=(OldName="NavigationTestingActor",NewName="/Script/NavigationSystem.NavigationTestingActor")
+ClassRedirects=(OldName="NavLinkComponent",NewName="/Script/NavigationSystem.NavLinkComponent")
+ClassRedirects=(OldName="NavLinkCustomComponent",NewName="/Script/NavigationSystem.NavLinkCustomComponent")
+ClassRedirects=(OldName="NavLinkRenderingComponent",NewName="/Script/NavigationSystem.NavLinkRenderingComponent")
+ClassRedirects=(OldName="NavLinkTrivial",NewName="/Script/NavigationSystem.NavLinkTrivial")
+ClassRedirects=(OldName="NavModifierComponent",NewName="/Script/NavigationSystem.NavModifierComponent")
+ClassRedirects=(OldName="NavModifierVolume",NewName="/Script/NavigationSystem.NavModifierVolume")
+ClassRedirects=(OldName="NavRelevantComponent",NewName="/Script/NavigationSystem.NavRelevantComponent")
+ClassRedirects=(OldName="RecastFilter_UseDefaultArea",NewName="/Script/NavigationSystem.RecastFilter_UseDefaultArea")
+ClassRedirects=(OldName="NavigationGraph",NewName="/Script/NavigationSystem.NavigationGraph")
+ClassRedirects=(OldName="NavigationGraphNode",NewName="/Script/NavigationSystem.NavigationGraphNode")
+ClassRedirects=(OldName="NavigationGraphNodeComponent",NewName="/Script/NavigationSystem.NavigationGraphNodeComponent")
+ClassRedirects=(OldName="NavigationPathGenerator",NewName="/Script/NavigationSystem.NavigationPathGenerator")
+ClassRedirects=(OldName="NavLinkCustomInterface",NewName="/Script/NavigationSystem.NavLinkCustomInterface")
+ClassRedirects=(OldName="NavLinkHostInterface",NewName="/Script/NavigationSystem.NavLinkHostInterface")
+ClassRedirects=(OldName="NavNodeInterface",NewName="/Script/NavigationSystem.NavNodeInterface")
+ClassRedirects=(OldName="NavLinkProxy",NewName="/Script/AIModule.NavLinkProxy")
+StructRedirects=(OldName="NavigationFilterArea",NewName="/Script/NavigationSystem.NavigationFilterArea")
+StructRedirects=(OldName="NavigationFilterFlags",NewName="/Script/NavigationSystem.NavigationFilterFlags")
+StructRedirects=(OldName="NavGraphEdge",NewName="/Script/NavigationSystem.NavGraphEdge")
+StructRedirects=(OldName="NavGraphNode",NewName="/Script/NavigationSystem.NavGraphNode")
+StructRedirects=(OldName="NavCollisionCylinder",NewName="/Script/NavigationSystem.NavCollisionCylinder")
+StructRedirects=(OldName="NavCollisionBox",NewName="/Script/NavigationSystem.NavCollisionBox")
+StructRedirects=(OldName="SupportedAreaData",NewName="/Script/NavigationSystem.SupportedAreaData")
+FunctionRedirects=(OldName="NavigationSystemV1.SimpleMoveToActor",NewName="AIBlueprintHelperLibrary.SimpleMoveToActor")
+FunctionRedirects=(OldName="NavigationSystemV1.SimpleMoveToLocation",NewName="AIBlueprintHelperLibrary.SimpleMoveToLocation")

+PropertyRedirects=(OldName="UserWidget.bCanEverTick", NewName="bHasScriptImplementedTick")
+PropertyRedirects=(OldName="UserWidget.bCanEverPaint", NewName="bHasScriptImplementedPaint")

+PropertyRedirects=(OldName="MovieScene.FrameResolution",NewName="TickResolution")
+PropertyRedirects=(OldName="MovieScene.PlayRate",NewName="DisplayRate")
+ClassRedirects=(OldName="/Script/MovieSceneCapture.AutomatedLevelSequenceCapture", NewName="/Script/MovieSceneTools.AutomatedLevelSequenceCapture")
+PackageRedirects=(OldName="/Script/AssetScriptingUtilitiesEditor", NewName="/Script/EditorScriptingUtilities")
+ClassRedirects=(OldName="/Script/AssetScriptingUtilities.StaticMeshUtilitiesLibrary", NewName="/Script/EditorScriptingUtilities.EditorStaticMeshLibrary")
+ClassRedirects=(OldName="/Script/AssetScriptingUtilities.SkeletalMeshUtilitiesLibrary", NewName="/Script/EditorScriptingUtilities.EditorSkeletalMeshLibrary")
+FunctionRedirects=(OldName="StaticMeshUtilitiesLibrary.GetLODScreenSizes",NewName="EditorStaticMeshLibrary.GetLodScreenSizes")

+ClassRedirects=(OldName="AppleARKitFaceMeshComponent",NewName="/Script/AppleARKitFaceSupport.AppleARKitFaceMeshComponent")

+StructRedirects=(OldName="FrameNumber",NewName="/Script/CoreUObject.FrameNumber")
+StructRedirects=(OldName="FrameRate",NewName="/Script/CoreUObject.FrameRate")
+StructRedirects=(OldName="FrameTime",NewName="/Script/CoreUObject.FrameTime")
+StructRedirects=(OldName="QualifiedFrameTime",NewName="/Script/CoreUObject.QualifiedFrameTime")
+StructRedirects=(OldName="Timecode",NewName="/Script/CoreUObject.Timecode")

+EnumRedirects=(OldName="EMeshComponentUpdateFlag",NewName="/Script/Engine.EVisibilityBasedAnimTickOption")
+PropertyRedirects=(OldName="SkinnedMeshComponent.MeshComponentUpdateFlag", NewName="VisibilityBasedAnimTickOption")

+ClassRedirects=(OldName="LevelStreamingKismet",NewName="/Script/Engine.LevelStreamingDynamic")

+PackageRedirects=(OldName="/Script/ImmediatePhysicsEditor", NewName="/Script/AnimGraph")
+PackageRedirects=(OldName="/Script/ImmediatePhysics", NewName="/Script/AnimGraphRuntime")
+ClassRedirects=(OldName="/Script/ImmediatePhysicsEditor.AnimGraphNode_RigidBody",NewName="/Script/AnimGraph.AnimGraphNode_RigidBody")
+ClassRedirects=(OldName="AnimGraphNode_Ragdoll",NewName="/Script/AnimGraph.AnimGraphNode_RigidBody")
+StructRedirects=(OldName="/Script/ImmediatePhysics.AnimNode_RigidBody",NewName="/Script/AnimGraphRuntime.AnimNode_RigidBody")
+StructRedirects=(OldName="AnimNode_Ragdoll",NewName="/Script/AnimGraphRuntime.AnimNode_RigidBody")

+EnumRedirects=(OldName="/Script/Engine.ESceneTextureId",ValueChanges=(("PPI_ShadingModel","PPI_ShadingModelColor"),("PPI_LightingModel","PPI_ShadingModelColor")))
+FunctionRedirects=(OldName="NavigationSystemV1.SimpleMoveToLocation",NewName="AIBlueprintHelperLibrary.SimpleMoveToLocation")

+ClassRedirects=(OldName="LevelSequenceDirectorBlueprint",NewName="/Script/LevelSequence.LegacyLevelSequenceDirectorBlueprint")
+ClassRedirects=(OldName="LevelSequenceDirectorGeneratedClass",NewName="/Script/Engine.BlueprintGeneratedClass")

+FunctionRedirects=(OldName="UserWidget.PlayAnimationTo",NewName="UserWidget.PlayAnimationTimeRange")
+FunctionRedirects=(OldName="UserWidget.PlayAnimationAtTime",NewName="UserWidget.PlayAnimation")
+FunctionRedirects=(OldName="AddChildWrapBox", NewName="AddChildToWrapBox")

+PropertyRedirects=(OldName="GameplayStatics.CreatePlayer.bSpawnPawn",NewName="bSpawnPlayerController")

+ClassRedirects=(OldName="/Script/GeometryCollectionCore.GeometryCollection",NewName="/Script/GeometryCollectionEngine.GeometryCollection")
+ClassRedirects=(OldName="/Script/GeometryCollectionCore.GeometryCollectionCache",NewName="/Script/GeometryCollectionEngine.GeometryCollectionCache")

+FunctionRedirects=(OldName="Controller.OnPossess",NewName="Controller.ReceivePossess")
+FunctionRedirects=(OldName="Controller.OnUnPossess",NewName="Controller.ReceiveUnPossess")

+FunctionRedirects=(OldName="PlayerController.ClientPlayForceFeedback",NewName="PlayerController.K2_ClientPlayForceFeedback")

+FunctionRedirects=(OldName="EditorUtilityWidget.OnDefaultActionClicked",NewName="EditorUtilityWidget.Run")
+FunctionRedirects=(OldName="SkeletalMeshComponent.GetSubInstanceByName",NewName="KismetSystemLibrary.GetSubInstanceByTag")
+StructRedirects=(OldName="/Script/AnimGraphRuntime.AnimNode_Root",NewName="/Script/Engine.AnimNode_Root")
+FunctionRedirects=(OldName="Widget.SetRenderAngle", NewName="Widget.SetRenderTransformAngle")

+ClassRedirects=(OldName="/Script/CoreUObject.MulticastDelegateProperty",NewName="/Script/CoreUObject.MulticastInlineDelegateProperty")
+ClassRedirects=(OldName="EditorAutomationActor",NewName="/Script/Blutility.EditorUtilityActor")
+ClassRedirects=(OldName="EditorAutomationActorComponent",NewName="/Script/Blutility.EditorUtilityActorComponent")
+ClassRedirects=(OldName="EditorAutomationObject",NewName="/Script/Blutility.EditorUtilityObject")
+ClassRedirects=(OldName="LandscapeBlueprintCustomBrush",NewName="/Script/LandscapeEditorUtilities.LandscapeBlueprintBrush")

+PropertyRedirects=(OldName="LandscapeLayerBrush.BPCustomBrush",NewName="LandscapeLayerBrush.BlueprintBrush")
+PropertyRedirects=(OldName="StructVariableDescription.bDontEditoOnInstance",NewName="bDontEditOnInstance")
+PropertyRedirects=(OldName="KismetMathLibrary.DegAtan2.A",NewName="Y")
+PropertyRedirects=(OldName="KismetMathLibrary.DegAtan2.B",NewName="X")
+PropertyRedirects=(OldName="KismetMathLibrary.Atan2.A",NewName="Y")
+PropertyRedirects=(OldName="KismetMathLibrary.Atan2.B",NewName="X")

+FunctionRedirects=(OldName="NativeUserListEntry.IsListItemSelected", NewName="UserListEntryLibrary.IsListItemSelected")
+FunctionRedirects=(OldName="NativeUserListEntry.IsListItemExpanded", NewName="UserListEntryLibrary.IsListItemExpanded")
+FunctionRedirects=(OldName="NativeUserListEntry.GetOwningListView", NewName="UserListEntryLibrary.GetOwningListView")
+FunctionRedirects=(OldName="UserObjectListEntry.GetListItemObject", NewName="UserObjectListEntryLibrary.GetListItemObject")

+PropertyRedirects=(OldName="NavDataConfig.NavigationDataClassName", NewName="NavDataConfig.NavDataClass")
+FunctionRedirects=(OldName="Actor.GetComponentsByClass", NewName="Actor.K2_GetComponentsByClass")

+PackageRedirects=(OldName="/Script/ClothingSystemRuntime",NewName="/Script/ClothingSystemRuntimeNv")
+ClassRedirects=(OldName="/Script/ClothingSystemRuntime.ClothingSimulationFactoryNv",NewName="/Script/ClothingSystemRuntimeNv.ClothingSimulationFactoryNv")
+ClassRedirects=(OldName="/Script/ClothingSystemRuntime.ClothingSimulationInteractorNv",NewName="/Script/ClothingSystemRuntimeNv.ClothingSimulationInteractorNv")
+FunctionRedirects=(OldName="ClothingSystemRuntime.ClothingSimulationInteractorNv.SetAnimDriveSpringStiffness",NewName="ClothingSystemRuntimeNv.ClothingSimulationInteractorNv.SetAnimDriveSpringStiffness")
+FunctionRedirects=(OldName="ClothingSystemRuntime.ClothingSimulationInteractorNv.SetAnimDriveDamperStiffness",NewName="ClothingSystemRuntimeNv.ClothingSimulationInteractorNv.SetAnimDriveDamperStiffness")
+FunctionRedirects=(OldName="ClothingSystemRuntime.ClothingSimulationInteractorNv.EnableGravityOverride",NewName="ClothingSystemRuntimeNv.ClothingSimulationInteractorNv.EnableGravityOverride")
+FunctionRedirects=(OldName="ClothingSystemRuntime.ClothingSimulationInteractorNv.DisableGravityOverride",NewName="ClothingSystemRuntimeNv.ClothingSimulationInteractorNv.DisableGravityOverride")

+ClassRedirects=(OldName="ClothingAsset",NewName="/Script/ClothingSystemRuntimeCommon.ClothingAssetCommon")
+StructRedirects=(OldName="ClothLODData",NewName="/Script/ClothingSystemRuntimeCommon.ClothLODDataCommon")
+StructRedirects=(OldName="ClothConfig",NewName="/Script/ClothingSystemRuntimeCommon.ClothConfig_Legacy")
+StructRedirects=(OldName="ClothParameterMask_PhysMesh",NewName="/Script/ClothingSystemRuntimeCommon.ClothParameterMask_Legacy")
+StructRedirects=(OldName="ClothConstraintSetup",NewName="/Script/ClothingSystemRuntimeCommon.ClothConstraintSetup_Legacy")
+EnumRedirects=(OldName="EClothingWindMethod",NewName="/Script/ClothingSystemRuntimeCommon.EClothingWindMethod_Legacy")

+ClassRedirects=(OldName="/Script/ClothingSystemRuntimeNv.ClothingAssetNv",NewName="/Script/ClothingSystemRuntimeCommon.ClothingAssetCommon")
+ClassRedirects=(OldName="/Script/ClothingSystemRuntimeNv.ClothLODDataNv",NewName="/Script/ClothingSystemRuntimeCommon.ClothLODDataCommon_Legacy")
+EnumRedirects=(OldName="MaskTarget_PhysMesh",NewName="/Script/ClothingSystemRuntimeCommon.EWeightMapTargetCommon",ValueChanges=(("MaskTarget_PhysMesh::MaxDistance","EWeightMapTargetCommon::MaxDistance"),("MaskTarget_PhysMesh::BackstopDistance","EWeightMapTargetCommon::BackstopDistance"),("MaskTarget_PhysMesh::BackstopRadius","EWeightMapTargetCommon::BackstopRadius"),("MaskTarget_PhysMesh::AnimDriveMultiplier","EWeightMapTargetCommon::AnimDriveStiffness")))

+ClassRedirects=(OldName="/Script/ClothingSystemRuntimeCommon.ClothLODDataCommon",NewName="/Script/ClothingSystemRuntimeCommon.ClothLODDataCommon_Legacy")

+ClassRedirects=(OldName="/Script/ClothingSystemRuntimeInterface.ClothPhysicalMeshDataBase",NewName="/Script/ClothingSystemRuntimeInterface.ClothPhysicalMeshDataBase_Legacy")
+ClassRedirects=(OldName="/Script/ClothingSystemRuntimeNv.ClothPhysicalMeshDataNv",NewName="/Script/ClothingSystemRuntimeNv.ClothPhysicalMeshDataNv_Legacy")

+EnumRedirects=(OldName="/Script/ClothingSystemRuntimeCommon.EWeightMapTargetCommon",ValueChanges=(("EWeightMapTargetCommon::AnimDriveMultiplier","EWeightMapTargetCommon::AnimDriveStiffness")))
+EnumRedirects=(OldName="EChaosWeightMapTarget",ValueChanges=(("EChaosWeightMapTarget::AnimDriveMultiplier","EChaosWeightMapTarget::AnimDriveStiffness")))

+FunctionRedirects=(OldName="SkeletalMeshComponent.GetSubInstanceByTag",NewName="SkeletalMeshComponent.GetLinkedAnimGraphInstanceByTag")
+FunctionRedirects=(OldName="SkeletalMeshComponent.GetSubInstancesByTag",NewName="SkeletalMeshComponent.GetLinkedAnimGraphInstancesByTag")
+PropertyRedirects=(OldName="SkeletalMeshComponent.GetLinkedAnimGraphInstancesByTag.OutSubInstances",NewName="OutLinkedInstances")
+FunctionRedirects=(OldName="SkeletalMeshComponent.SetSubInstanceClassByTag",NewName="SkeletalMeshComponent.LinkAnimGraphByTag")
+FunctionRedirects=(OldName="SkeletalMeshComponent.SetLayerOverlay",NewName="SkeletalMeshComponent.LinkAnimClassLayers")
+FunctionRedirects=(OldName="SkeletalMeshComponent.ClearLayerOverlay",NewName="SkeletalMeshComponent.UnlinkAnimClassLayers")
+FunctionRedirects=(OldName="SkeletalMeshComponent.GetLayerSubInstanceByGroup",NewName="SkeletalMeshComponent.GetLinkedAnimLayerInstanceByGroup")
+FunctionRedirects=(OldName="SkeletalMeshComponent.GetLayerSubInstanceByClass",NewName="SkeletalMeshComponent.GetLinkedAnimLayerInstanceByClass")
+FunctionRedirects=(OldName="AnimInstance.GetSubInstanceByTag",NewName="AnimInstance.GetLinkedAnimGraphInstanceByTag")
+FunctionRedirects=(OldName="AnimInstance.GetSubInstancesByTag",NewName="AnimInstance.GetLinkedAnimGraphInstancesByTag")
+PropertyRedirects=(OldName="AnimInstance.GetLinkedAnimGraphInstancesByTag.OutSubInstances",NewName="OutLinkedInstances")
+FunctionRedirects=(OldName="AnimInstance.SetSubInstanceClassByTag",NewName="AnimInstance.LinkAnimGraphByTag")
+FunctionRedirects=(OldName="AnimInstance.SetLayerOverlay",NewName="AnimInstance.LinkAnimClassLayers")
+FunctionRedirects=(OldName="AnimInstance.ClearLayerOverlay",NewName="AnimInstance.UnlinkAnimClassLayers")
+FunctionRedirects=(OldName="AnimInstance.GetLayerSubInstanceByGroup",NewName="AnimInstance.GetLinkedAnimLayerInstanceByGroup")
+FunctionRedirects=(OldName="AnimInstance.GetLayerSubInstanceByClass",NewName="AnimInstance.GetLinkedAnimLayerInstanceByClass")
+StructRedirects=(OldName="AnimNode_SubInstance",NewName="/Script/Engine.AnimNode_LinkedAnimGraph")
+StructRedirects=(OldName="AnimNode_SubInput",NewName="/Script/Engine.AnimNode_LinkedInputPose")
+StructRedirects=(OldName="AnimNode_Layer",NewName="/Script/Engine.AnimNode_LinkedAnimLayer")
+ClassRedirects=(OldName="/Script/AnimGraph.AnimGraphNode_SubInstanceBase",NewName="/Script/AnimGraph.AnimGraphNode_LinkedAnimGraphBase")
+ClassRedirects=(OldName="/Script/AnimGraph.AnimGraphNode_SubInstance",NewName="/Script/AnimGraph.AnimGraphNode_LinkedAnimGraph")
+ClassRedirects=(OldName="/Script/AnimGraph.AnimGraphNode_SubInput",NewName="/Script/AnimGraph.AnimGraphNode_LinkedInputPose")
+ClassRedirects=(OldName="/Script/AnimGraph.AnimGraphNode_Layer",NewName="/Script/AnimGraph.AnimGraphNode_LinkedAnimLayer")
+PropertyRedirects=(OldName="PersonaPreviewSceneDescription.SubInstanceTag",NewName="LinkedAnimGraphTag")
+EnumRedirects=(OldName="/Script/Engine.EPreviewAnimationBlueprintApplicationMethod",NewName="/Script/Engine.EPreviewAnimationBlueprintApplicationMethod",ValueChanges=(("OverlayLayer", "LinkedLayers"), ("SubInstance", "LinkedAnimGraph")))
+PropertyRedirects=(OldName="AnimClassData.SubInstanceNodeProperties",NewName="LinkedAnimGraphNodeProperties")
+PropertyRedirects=(OldName="AnimClassData.LayerNodeProperties",NewName="LinkedAnimLayerNodeProperties")
+ClassRedirects=(OldName="/Script/MeshEditingToolset.BaseBrushTool",NewName="/Script/InteractiveToolsFramework.BaseBrushTool")
+ClassRedirects=(OldName="/Script/MeshEditingToolset.BrushBaseProperties",NewName="/Script/InteractiveToolsFramework.BrushBaseProperties")

+EnumRedirects=(OldName="ECurveBlendOption",ValueChanges=(("ECurveBlendOption::MaxWeight", "ECurveBlendOption::Override")))
+ClassRedirects=(OldName="/Script/OnlineBlueprintSupport.K2Node_LatentOnlineCall", NewName="/Script/BlueprintGraph.K2Node_AsyncAction")
+ClassRedirects=(OldName="/Script/Kismet.K2Node_AsyncAction", NewName="/Script/BlueprintGraph.K2Node_AsyncAction")

+ClassRedirects=(OldName="MovieSceneSpawnTrack",NewName="/Script/MovieScene.MovieSceneSpawnTrack")
+ClassRedirects=(OldName="MovieSceneSpawnSection",NewName="/Script/MovieScene.MovieSceneSpawnSection")
+ClassRedirects=(OldName="MovieSceneBoolSection",NewName="/Script/MovieScene.MovieSceneBoolSection")
+ClassRedirects=(OldName="PhysicalMaterialPropertyBase",NewName="/Script/PhysicsCore.PhysicalMaterialPropertyBase")
+ClassRedirects=(OldName="PhysicalMaterial",NewName="/Script/PhysicsCore.PhysicalMaterial")
+ClassRedirects=(OldName="ChaosPhysicalMaterial",NewName="/Script/PhysicsCore.ChaosPhysicalMaterial")
+ClassRedirects=(OldName="SubmixEffectReverbFastPreset",NewName="/Script/AudioMixer.SubmixEffectReverbPreset")
+EnumRedirects=(OldName="EPhysicalSurface",NewName="/Script/PhysicsCore.EPhysicalSurface")
+EnumRedirects=(OldName="ECollisionTraceFlag",NewName="/Script/PhysicsCore.ECollisionTraceFlag")
+EnumRedirects=(OldName="EPhysicsType",NewName="/Script/PhysicsCore.EPhysicsType",ValueChanges=(("PhysType_Fixed", "PhysType_Kinematic"), ("PhysType_Unfixed", "PhysType_Simulated")))
+EnumRedirects=(OldName="EBodyCollisionResponse",NewName="/Script/PhysicsCore.EBodyCollisionResponse")
+EnumRedirects=(OldName="EFrictionCombineMode",NewName="/Script/PhysicsCore.EFrictionCombineMode")
+EnumRedirects=(OldName="ELinearConstraintMotion",NewName="/Script/PhysicsCore.ELinearConstraintMotion")
+EnumRedirects=(OldName="ERadialImpulseFalloff",NewName="/Script/PhysicsCore.ERadialImpulseFalloff")
+EnumRedirects=(OldName="ESleepFamily",NewName="/Script/PhysicsCore.ESleepFamily")
+EnumRedirects=(OldName="EAngularConstraintMotion",NewName="/Script/PhysicsCore.EAngularConstraintMotion")
+EnumRedirects=(OldName="EConstraintFrame",NewName="/Script/PhysicsCore.EConstraintFrame")
+StructRedirects=(OldName="/Script/AssetRegistry.ARFilter",NewName="/Script/CoreUObject.ARFilter")
+StructRedirects=(OldName="/Script/AssetRegistry.AssetBundleData",NewName="/Script/CoreUObject.AssetBundleData")
+StructRedirects=(OldName="/Script/AssetRegistry.AssetBundleEntry",NewName="/Script/CoreUObject.AssetBundleEntry")
+StructRedirects=(OldName="/Script/AssetRegistry.AssetData",NewName="/Script/CoreUObject.AssetData")
+StructRedirects=(OldName="SubmixEffectReverbFastSettings",NewName="/Script/AudioMixer.SubmixEffectReverbSettings")

+FunctionRedirects=(OldName="KismetInputLibrary.Key_IsFloatAxis",NewName="KismetInputLibrary.Key_IsAxis1D")
+FunctionRedirects=(OldName="KismetMathLibrary.Conv_VectorToQuaterion",NewName="KismetMathLibrary.Conv_VectorToQuaternion")
+FunctionRedirects=(OldName="KismetMathLibrary.Conv_Vector4ToQuaterion",NewName="KismetMathLibrary.Conv_Vector4ToQuaternion")

+FunctionRedirects=(OldName="PinComponentToARPin",NewName="/Script/AugmentedReality.ARBlueprintLibrary.PinComponentToARPin")
+FunctionRedirects=(OldName="IsWMRAnchorStoreReady",NewName="/Script/AugmentedReality.ARBlueprintLibrary.IsARPinLocalStoreReady")
+FunctionRedirects=(OldName="RemoveAllARPinsFromWMRAnchorStore",NewName="/Script/AugmentedReality.ARBlueprintLibrary.RemoveAllARPinsFromLocalStore")
+FunctionRedirects=(OldName="AzureSpatialAnchorsLibrary.CreateCloudAnchor",NewName="AzureSpatialAnchorsLibrary.ConstructCloudAnchor")

+PackageRedirects=(OldName="/Script/FieldSystemCore",NewName="/Script/Chaos")
+PackageRedirects=(OldName="/Script/GeometryCollectionSimulationCore",NewName="/Script/Chaos")

+ClassRedirects=(OldName="/VirtualCamera/VirtualCameraGameMode.VirtualCameraGameMode",NewName="/VirtualCamera/Deprecated/Deprecated_VirtualCameraGameMode.Deprecated_VirtualCameraGameMode")
+ClassRedirects=(OldName="/VirtualCamera/VirtualCameraPawn.VirtualCameraPawn",NewName="/VirtualCamera/Deprecated/Deprecated_VirtualCameraPawn.Deprecated_VirtualCameraPawn")
+ClassRedirects=(OldName="/VirtualCamera/VirtualCameraPlayerController.VirtualCameraPlayerController",NewName="/VirtualCamera/Deprecated/Deprecated_VirtualCameraPlayerController.Deprecated_VirtualCameraPlayerController")
+ClassRedirects=(OldName="/VirtualCamera/Widgets/VirtualCameraJoysticks.VirtualCameraJoysticks",NewName="/VirtualCamera/Deprecated/Widgets/Deprecated_VirtualCameraJoysticks.Deprecated_VirtualCameraJoysticks")

+PackageRedirects=(OldName="/VirtualCamera/Widgets/SequenceButton",NewName="/VirtualCamera/Deprecated/Widgets/SequenceButton")
+PackageRedirects=(OldName="/VirtualCamera/Widgets/SequenceMenu",NewName="/VirtualCamera/Deprecated/Widgets/SequenceMenu")
+PackageRedirects=(OldName="/VirtualCamera/Widgets/SequenceMenu",NewName="/VirtualCamera/Deprecated/Widgets/SequenceMenu")

+PackageRedirects=(OldName="/VirtualCamera/Textures/VPLeftJoystick",NewName="/VirtualCamera/Deprecated/Textures/VPLeftJoystick")
+PackageRedirects=(OldName="/VirtualCamera/Textures/VPLeftJoystickBG",NewName="/VirtualCamera/Deprecated/Textures/VPLeftJoystickBG")
+PackageRedirects=(OldName="/VirtualCamera/Textures/VPRightJoystick",NewName="/VirtualCamera/Deprecated/Textures/VPRightJoystick")
+PackageRedirects=(OldName="/VirtualCamera/Textures/VPRightJoystickBG",NewName="/VirtualCamera/Deprecated/Textures/VPRightJoystickBG")

+PackageRedirects=(OldName="/VCamCore/Assets/VCam_EmptyVisibleUMG",NewName="/VirtualCamera/VCamCore/Assets/VCam_EmptyVisibleUMG")

+PackageRedirects=(OldName="/Game/VirtualCamera/Blueprints/Modifiers/SinModifier", NewName="/VirtualCamera/VCamCore/Blueprints/Modifiers/SinModifier")
+PackageRedirects=(OldName="/Game/VirtualCamera/Blueprints/Modifiers/FollowModifier", NewName="/VirtualCamera/VCamCore/Blueprints/Modifiers/FollowModifier")
+PackageRedirects=(OldName="/Game/VirtualCamera/Blueprints/Modifiers/GamepadModifier", NewName="/VirtualCamera/VCamCore/Blueprints/Modifiers/GamepadModifier")

+PackageRedirects=(OldName="/Game/VirtualCamera/Blueprints/TestAJA", NewName="/VirtualCamera/VCamCore/Blueprints/TestAJA")
+PackageRedirects=(OldName="/Game/VirtualCamera/Assets/TestComposureMaterial_AOverB", NewName="/VirtualCamera/VCamCore/Assets/TestComposureMaterial_AOverB")
+PackageRedirects=(OldName="/Game/VirtualCamera/Assets/TestComposureRenderTargetOutput", NewName="/VirtualCamera/VCamCore/Assets/TestComposureRenderTargetOutput")
+PackageRedirects=(OldName="/Game/VirtualCamera/Assets/VCamSample_AOverBComposureMaterial", NewName="/VirtualCamera/VCamCore/Assets/VCamSample_AOverBComposureMaterial")
+PackageRedirects=(OldName="/Game/VirtualCamera/Assets/VCamSample_ComposureRenderTargetOutput", NewName="/VirtualCamera/VCamCore/Assets/VCamSample_ComposureRenderTargetOutput")

+EnumRedirects=(OldName="EOscillatorWaveform",NewName="/Script/EngineCameras.EOscillatorWaveform")
+EnumRedirects=(OldName="EInitialOscillatorOffset",NewName="/Script/EngineCameras.EInitialOscillatorOffset")
+StructRedirects=(OldName="FOscillator",NewName="/Script/EngineCameras.FOscillator")
+StructRedirects=(OldName="ROscillator",NewName="/Script/EngineCameras.ROscillator")
+StructRedirects=(OldName="VOscillator",NewName="/Script/EngineCameras.VOscillator")
+EnumRedirects=(OldName="ECameraAnimPlaySpace",NewName="/Script/Engine.ECameraShakePlaySpace")
+FunctionRedirects=(OldName="PlayerController.ClientPlayCameraShake",NewName="ClientStartCameraShake")
+FunctionRedirects=(OldName="PlayerController.ClientPlayCameraShakeFromSource",NewName="ClientStartCameraShakeFromSource")
+FunctionRedirects=(OldName="PlayerCameraManager.PlayCameraShake",NewName="StartCameraShake")
+FunctionRedirects=(OldName="PlayerCameraManager.PlayCameraShakeFromSource",NewName="StartCameraShakeFromSource")
+FunctionRedirects=(OldName="PlayerCameraManager.StartMatineeCameraShake",NewName="StartCameraShake")
+FunctionRedirects=(OldName="PlayerCameraManager.StartMatineeCameraShakeFromSource",NewName="StartCameraShakeFromSource")
+FunctionRedirects=(OldName="CameraShakeSourceComponent.Play",NewName="Start")
+FunctionRedirects=(OldName="CameraShakeSourceComponent.PlayCameraShake",NewName="StartCameraShake")
+PropertyRedirects=(OldName="CameraShakeSourceComponent.bAutoPlay",NewName="bAutoStart")
+PropertyRedirects=(OldName="FieldSystemComponent.IsChaosField",NewName="bIsChaosField")
+PropertyRedirects=(OldName="FieldSystemComponent.IsGlobalField",NewName="bIsWorldField")

+EnumRedirects=(OldName="TextureCompressionSettings",ValueChanges=(("TC_ReflectionCapture","TC_EncodedReflectionCapture")))
+EnumRedirects=(OldName="EEnvQueryTrace",ValueChanges=(("Geometry","GeometryByChannel")))
+EnumRedirects=(OldName="/Script/Engine.ENetworkFailure",NewName="/Script/NetCore.ENetworkFailure")
+ClassRedirects=(OldName="WaterMeshActor",NewName="/Script/Water.WaterZone")

+StructRedirects=(OldName="/Script/ControlRig.RigUnit_SpringInterpQuaternion",NewName="/Script/ControlRig.RigUnit_SpringInterpQuaternionV2")
+ClassRedirects=(OldName="RigVMStructNode",NewName="/Script/RigVMDeveloper.RigVMUnitNode")
+EnumRedirects=(OldName="/Script/Slate.ETextTransformPolicy", NewName="/Script/SlateCore.ETextTransformPolicy")
+PropertyRedirects=(OldName="BlendProfile.BlendProfileMode", NewName="Mode")
+PropertyRedirects=(OldName="RigVMInjectionInfo.StructNode", NewName="UnitNode")
+ClassRedirects=(OldName="/Script/UnrealEd.ProjectPackagingSettings", NewName="/Script/DeveloperToolSettings.ProjectPackagingSettings")
+ClassRedirects=(OldName="/Script/UnrealEd.CookerSettings", NewName="/Script/DeveloperToolSettings.CookerSettings")
+ClassRedirects=(OldName="/Script/MeshDescription.MeshDescriptionBulkDataWrapper",NewName="/Script/Engine.StaticMeshDescriptionBulkData")
+StructRedirects=(OldName="/Script/Engine.FilePath",NewName="/Script/CoreUObject.FilePath")
+StructRedirects=(OldName="/Script/Engine.DirectoryPath",NewName="/Script/CoreUObject.DirectoryPath")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetNumFrames.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetNumKeys.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetAnimationTrackNames.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetRawTrackPositionData.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetRawTrackRotationData.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetRawTrackScaleData.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetRawTrackData.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.IsValidRawAnimationTrackName.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetAnimationNotifyEvents.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetAnimationNotifyEventNames.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddAnimationNotifyEvent.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddAnimationNotifyStateEvent.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddAnimationNotifyEventObject.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddAnimationNotifyStateEventObject.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.RemoveAnimationNotifyEventsByName.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.RemoveAnimationNotifyEventsByTrack.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.ReplaceAnimNotifyStates.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.ReplaceAnimNotifies.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.CopyAnimNotifiesFromSequence.SrcAnimSequence",NewName="SourceAnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.CopyAnimNotifiesFromSequence.DestAnimSequence",NewName="DestinationAnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetAnimationNotifyTrackNames.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddAnimationNotifyTrack.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.RemoveAnimationNotifyTrack.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.RemoveAllAnimationNotifyTracks.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.IsValidAnimNotifyTrackName.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetAnimationNotifyEventsForTrack.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddMetaData.AnimationSequence",NewName="AnimationAsset")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddMetaDataObject.AnimationSequence",NewName="AnimationAsset")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.RemoveAllMetaData.AnimationSequence",NewName="AnimationAsset")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.RemoveMetaData.AnimationSequence",NewName="AnimationAsset")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.RemoveMetaDataOfClass.AnimationSequence",NewName="AnimationAsset")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetMetaData.AnimationSequence",NewName="AnimationAsset")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetMetaDataOfClass.AnimationSequence",NewName="AnimationAsset")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.ContainsMetaDataOfClass.AnimationSequence",NewName="AnimationAsset")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetBonePoseForTime.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetBonePoseForFrame.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetBonePosesForTime.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetBonePosesForFrame.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetSequenceLength.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetRateScale.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.SetRateScale.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetFrameAtTime.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetTimeAtFrame.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.IsValidTime.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.FindBonePathToRoot.AnimationSequence",NewName="AnimationSequenceBase")

+PropertyRedirects=(OldName="AssetRegistry.ScanPathsSynchronous.bIgnoreBlackListScanFilters",NewName="bIgnoreDenyListScanFilters")
+PropertyRedirects=(OldName="LandscapeComponent.LayerWhitelist", NewName="LayerAllowList")
+EnumRedirects=(OldName="ELandscapeLayerPaintingRestriction",ValueChanges=(("UseComponentWhitelist","UseComponentAllowList")) )
+PropertyRedirects=(OldName="DataLayer.bIsDynamicallyLoaded", NewName="DataLayer.bIsRuntime")
+PropertyRedirects=(OldName="DataLayer.InitialState", NewName="DataLayer.InitialRuntimeState")
+PropertyRedirects=(OldName="DataLayerSubsystem.OnDataLayerStateChanged", NewName="DataLayerSubsystem.OnDataLayerRuntimeStateChanged")
+FunctionRedirects=(OldName="WorldDataLayers.OnDataLayerStateChanged", NewName="WorldDataLayers.OnDataLayerRuntimeStateChanged")

+ClassRedirects=(OldName="/Script/Engine.BlendSpaceBase",NewName="/Script/Engine.BlendSpace")
+FunctionRedirects=(OldName="Image.SetBrushSize",NewName="SetDesiredSizeOverride")
+ClassRedirects=(OldName="/Script/PropertyAccess.AnimBlueprintClassSubsystem_PropertyAccess",NewName="/Script/Engine.AnimBlueprintClassSubsystem_PropertyAccess")
+PackageRedirects=(OldName="/Script/PropertyAccess", Removed=true)
+StructRedirects=(OldName="AnimInstanceSubsystemData",NewName="/Script/Engine.AnimSubsystemInstance")
+ClassRedirects=(OldName="/Script/AnimationModifiers.AnimationBlueprintLibrary",NewName="/Script/AnimationBlueprintLibrary.AnimationBlueprintLibrary")
+PropertyRedirects=(OldName="GeometryCollectionComponent.Simulating", NewName="SimulatePhysics")
+PropertyRedirects=(OldName="GeometryCollectionComponent.ApplyExternalStrain.Index", NewName="GeometryCollectionComponent.ApplyExternalStrain.ItemIndex")
+PropertyRedirects=(OldName="GeometryCollectionComponent.CrumbleCluster.Index", NewName="GeometryCollectionComponent.CrumbleCluster.ItemIndex")
+PropertyRedirects=(OldName="ControlRigComponent.OnPreUpdateDelegate", NewName="OnPreForwardsSolveDelegate")
+PropertyRedirects=(OldName="ControlRigComponent.OnPostUpdateDelegate", NewName="OnPostForwardsSolveDelegate")
+PropertyRedirects=(OldName="AnimNode_Mirror.BlendTimeOnMirrorStateChange",NewName="AnimNode_Mirror.BlendTime")
+PropertyRedirects=(OldName="AnimNode_Mirror.bResetChildOnMirrorStateChange",NewName="AnimNode_Mirror.bResetChild")
+PropertyRedirects=(OldName="PlayerState.Ping",NewName="CompressedPing")
+ClassRedirects=(OldName="/Script/MovieSceneTracks.MovieSceneVectorSection",NewName="/Script/MovieSceneTracks.MovieSceneFloatVectorSection")
+ClassRedirects=(OldName="/Script/MovieSceneTracks.MovieSceneVectorTrack",NewName="/Script/MovieSceneTracks.MovieSceneFloatVectorTrack")
+ClassRedirects=(OldName="/Script/MovieSceneTracks.MovieSceneVectorTrackExtensions",NewName="/Script/SequencerScripting.MovieSceneFloatVectorTrackExtensions")
+StructRedirects=(OldName="/Script/MovieSceneTracks.MovieSceneVectorKeyStructBase",NewName="/Script/MovieSceneTracks.MovieSceneFloatVectorKeyStructBase")
+StructRedirects=(OldName="/Script/MovieSceneTracks.MovieSceneVectorKeyStruct",NewName="/Script/MovieSceneTracks.MovieSceneVector3fKeyStruct")

+StructRedirects=(OldName="AudioComponentParam", NewName="/Script/AudioExtensions.AudioParameter")
+PropertyRedirects=(OldName="AudioParameter.SoundWaveParam", NewName="AudioParameter.ObjectParam")

+FunctionRedirects=(OldName="AudioComponent.SetBoolParameter", NewName="AudioParameterControllerInterface.SetBoolParameter")
+FunctionRedirects=(OldName="AudioComponent.SetFloatParameter", NewName="AudioParameterControllerInterface.SetFloatParameter")
+FunctionRedirects=(OldName="AudioComponent.SetIntParameter", NewName="AudioParameterControllerInterface.SetIntParameter")
+PropertyRedirects=(OldName="AudioComponent.InstanceParameters", NewName="AudioComponent.DefaultParameters")

+FunctionRedirects=(OldName="SoundGeneratorParameterInterface.SetBoolParameter", NewName="AudioParameterControllerInterface.SetBoolParameter")
+FunctionRedirects=(OldName="SoundGeneratorParameterInterface.SetFloatParameter", NewName="AudioParameterControllerInterface.SetFloatParameter")
+FunctionRedirects=(OldName="SoundGeneratorParameterInterface.SetIntParameter", NewName="AudioParameterControllerInterface.SetIntParameter")
+ClassRedirects=(OldName="/Script/AudioExtensions.AudioParameterInterface", NewName="/Script/AudioExtensions.AudioParameterControllerInterface")

+EnumRedirects=(OldName="ESoundwaveSampleRateSettings",ValueChanges=(("ESoundwaveSampleRateSettings::MatchDevice","ESoundwaveSampleRateSettings::Max")) )

+PropertyRedirects=(OldName="GameplayCueNotify_SoundInfo.SoundCue",NewName="GameplayCueNotify_SoundInfo.Sound")

+ClassRedirects=(OldName="/Script/LiveLink.LiveLinkRetargetAsset", NewName="/Script/LiveLinkAnimationCore.LiveLinkRetargetAsset")
+ClassRedirects=(OldName="/Script/LiveLink.LiveLinkRemapAsset", NewName="/Script/LiveLinkAnimationCore.LiveLinkRemapAsset")
+ClassRedirects=(OldName="/Script/LiveLink.LiveLinkInstance", NewName="/Script/LiveLinkAnimationCore.LiveLinkInstance")
+StructRedirects=(OldName="/Script/LiveLink.AnimNode_LiveLinkPose", NewName="/Script/LiveLinkAnimationCore.AnimNode_LiveLinkPose")
+StructRedirects=(OldName="/Script/LiveLink.LiveLinkInstanceProxy", NewName="/Script/LiveLinkAnimationCore.LiveLinkInstanceProxy")

+EnumRedirects=(OldName="ELevelInstanceRuntimeBehavior",ValueChanges=(("Embedded","Partitioned")) )

+PackageRedirects=(OldName="/Engine/VT/LightmapVirtualTextureSpace_0_Compressed",Removed=True)

+ClassRedirects=(OldName="ComputeGraph", NewName="/Script/ComputeFramework.ComputeGraph")
+ClassRedirects=(OldName="ComputeGraphComponent", NewName="/Script/ComputeFramework.ComputeGraphComponent")
+ClassRedirects=(OldName="ComputeKernel", NewName="/Script/ComputeFramework.ComputeKernel")
+ClassRedirects=(OldName="ComputeKernelFromText", NewName="/Script/ComputeFramework.ComputeKernelFromText")
+ClassRedirects=(OldName="ComputeKernelSource", NewName="/Script/ComputeFramework.ComputeKernelSource")
+ClassRedirects=(OldName="ComputeKernel", NewName="/Script/ComputeFramework.ComputeKernel")

+ClassRedirects=(OldName="PackedLevelInstance", NewName="/Script/Engine.PackedLevelActor")
+ClassRedirects=(OldName="EditorStyleSettings",NewName="/Script/UnrealEd.EditorStyleSettings")
+EnumRedirects=(OldName="EAssetEditorOpenLocation",NewName="/Script/UnrealEd.EAssetEditorOpenLocation")
+EnumRedirects=(OldName="ELogCategoryColorizationMode",NewName="/Script/OutputLog.ELogCategoryColorizationMode")
+EnumRedirects=(OldName="/Script/UnrealEd.ELogCategoryColorizationMode",NewName="/Script/OutputLog.ELogCategoryColorizationMode")

+PropertyRedirects=(OldName="PostProcessSettings.LocalExposureContrastReduction", NewName="PostProcessSettings.LocalExposureContrastScale")
+PropertyRedirects=(OldName="PostProcessSettings.bOverride_LocalExposureContrastReduction", NewName="PostProcessSettings.bOverride_LocalExposureContrastScale")

+FunctionRedirects=(OldName="KismetStringLibrary.Conv_FloatToString", NewName="Conv_DoubleToString")
+PropertyRedirects=(OldName="KismetStringLibrary.Conv_DoubleToString.InFloat", NewName="Conv_DoubleToString.InDouble")
+FunctionRedirects=(OldName="KismetStringLibrary.Conv_StringToFloat", NewName="Conv_StringToDouble")
+FunctionRedirects=(OldName="KismetStringLibrary.BuildString_Float", NewName="BuildString_Double")
+PropertyRedirects=(OldName="KismetStringLibrary.BuildString_Double.InFloat", NewName="BuildString_Double.InDouble")

+FunctionRedirects=(OldName="KismetMathLibrary.MakeVector_NetQuantize", NewName="MakeVector")
+FunctionRedirects=(OldName="KismetMathLibrary.MakeVector_NetQuantize10", NewName="MakeVector")
+FunctionRedirects=(OldName="KismetMathLibrary.MakeVector_NetQuantize100", NewName="MakeVector")
+FunctionRedirects=(OldName="KismetMathLibrary.MakeVector_NetQuantizeNormal", NewName="MakeVector")
+FunctionRedirects=(OldName="KismetMathLibrary.BreakVector_NetQuantize", NewName="BreakVector")
+FunctionRedirects=(OldName="KismetMathLibrary.BreakVector_NetQuantize10", NewName="BreakVector")
+FunctionRedirects=(OldName="KismetMathLibrary.BreakVector_NetQuantize100", NewName="BreakVector")
+FunctionRedirects=(OldName="KismetMathLibrary.BreakVector_NetQuantizeNormal", NewName="BreakVector")
+FunctionRedirects=(OldName="KismetMathLibrary.Multiply_FloatFloat", NewName="Multiply_DoubleDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.Divide_FloatFloat", NewName="Divide_DoubleDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.Add_FloatFloat", NewName="Add_DoubleDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.Subtract_FloatFloat", NewName="Subtract_DoubleDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.Less_FloatFloat", NewName="Less_DoubleDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.Greater_FloatFloat", NewName="Greater_DoubleDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.LessEqual_FloatFloat", NewName="LessEqual_DoubleDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.GreaterEqual_FloatFloat", NewName="GreaterEqual_DoubleDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.EqualEqual_FloatFloat", NewName="EqualEqual_DoubleDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.NotEqual_FloatFloat", NewName="NotEqual_DoubleDouble")

+StructRedirects=(OldName="/Script/Engine.ActorComponentDuplicatedObjectData", NewName="/Script/Engine.DataCacheDuplicatedObjectData")

+ClassRedirects=(OldName="RigVMTemplateNode", NewName="/Script/RigVMDeveloper.RigVMUnitNode")

+StructRedirects=(OldName="/Script/SequencerScripting.SequencerBindingProxy", NewName="/Script/MovieScene.MovieSceneBindingProxy")

+FunctionRedirects=(OldName="KismetSystemLibrary.MakeLiteralFloat", NewName="MakeLiteralDouble")

+FunctionRedirects=(OldName="KismetTextLibrary.Conv_FloatToText", NewName="Conv_DoubleToText")
+PropertyRedirects=(OldName="KismetTextLibrary.Conv_DoubleToText.InDouble", NewName="Conv_DoubleToText.Value")

+PropertyRedirects=(OldName="AITask_MoveTo.AIMoveTo.bUseContinuosGoalTracking", NewName="AITask_MoveTo.AIMoveTo.bUseContinuousGoalTracking")

+PropertyRedirects=(OldName="RectLightComponent.SetSourceTexture.bNewValue", NewName="NewValue")
+PropertyRedirects=(OldName="RectLightComponent.SetSourceWidth.bNewValue", NewName="NewValue")

+PropertyRedirects=(OldName="RecastNavMeshTileGenerationDebug.bHeightfieldSolidPostRadiusFiltering", NewName="bHeightfieldSolidPostInclusionBoundsFiltering")

+PropertyRedirects=(OldName="SimSpaceSettings.MasterAlpha", NewName="SimSpaceSettings.WorldAlpha")

+PropertyRedirects=(OldName="SkinnedMeshComponent.MasterPoseComponent", NewName="LeaderPoseComponent")
+PropertyRedirects=(OldName="SkinnedMeshComponent.bUseBoundsFromMasterPoseComponent", NewName="bUseBoundsFromLeaderPoseComponent")
+PropertyRedirects=(OldName="SkinnedMeshComponent.bIgnoreMasterPoseComponentLOD", NewName="bIgnoreLeaderPoseComponentLOD")
+FunctionRedirects=(OldName="SkinnedMeshComponent.SetMasterPoseComponent", NewName="SetLeaderPoseComponent")
+PropertyRedirects=(OldName="SkinnedMeshComponent.SetLeaderPoseComponent.NewMasterBoneComponent",NewName="SkinnedMeshComponent.SetLeaderPoseComponent.NewLeaderBoneComponent")

+PropertyRedirects=(OldName="SkeletalMeshComponent.bPropagateCurvesToSlaves", NewName="bPropagateCurvesToFollowers")
+FunctionRedirects=(OldName="SkeletalMeshComponent.BindClothToMasterPoseComponent", NewName="BindClothToLeaderPoseComponent")
+FunctionRedirects=(OldName="SkeletalMeshComponent.UnbindClothFromMasterPoseComponent", NewName="UnbindClothFromLeaderPoseComponent")

+FunctionRedirects=(OldName="SkinnedMeshComponent.SetSkeletalMesh", NewName="SetSkinnedAssetAndUpdate")

+PropertyRedirects=(OldName="AGameplayAbilityWorldReticle.MasterPC", NewName="PrimaryPC")
+PropertyRedirects=(OldName="AGameplayAbilityTargetActor.MasterPC", NewName="PrimaryPC")

+ClassRedirects=(OldName="CameraShake",NewName="/Script/EngineCameras.LegacyCameraShake")
+ClassRedirects=(OldName="MatineeCameraShake",NewName="/Script/EngineCameras.LegacyCameraShake")
+ClassRedirects=(OldName="MatineeCameraShakePattern",NewName="/Script/EngineCameras.LegacyCameraShakePattern")
+ClassRedirects=(OldName="MatineeCameraShakeFunctionLibrary", NewName="/Script/EngineCameras.LegacyCameraShakeFunctionLibrary")
+FunctionRedirects=(OldName="LegacyCameraShake.StartMatineeCameraShake", NewName="LegacyCameraShake.StartLegacyCameraShake")
+FunctionRedirects=(OldName="LegacyCameraShake.StartMatineeCameraShakeFromSource", NewName="LegacyCameraShake.StartLegacyCameraShakeFromSource")
+FunctionRedirects=(OldName="LegacyCameraShakeFunctionLibrary.Conv_MatineeCameraShake", NewName="LegacyCameraShakeFunctionLibrary.Conv_LegacyCameraShake")

+PropertyRedirects=(OldName="AnimationSettings.BoneTimecodeCustomAttributeNameSettings",NewName="BoneTimecodeAnimationAttributeNameSettings")
+PropertyRedirects=(OldName="AnimationSettings.BoneCustomAttributesNames",NewName="BoneAnimationAttributesNames")
+PropertyRedirects=(OldName="AnimationSettings.BoneNamesWithCustomAttributes",NewName="BoneNamesWithAnimationAttributes")

+ClassRedirects=(OldName="/Script/Engine.PlatformInputDeviceMapperLibrary",NewName="/Script/Engine.InputDeviceLibrary")
+ClassRedirects=(OldName="/Script/Engine.PlatformInputDeviceMapperLibrary",NewName="/Script/Engine.InputDeviceLibrary")
+StructRedirects=(OldName="/Script/Engine.SetDevicePropertyParams", NewName="/Script/Engine.ActivateDevicePropertyParams")
+FunctionRedirects=(OldName="InputDeviceSubsystem.GetPlayerControllerFromInputDevice", NewName="InputDeviceLibrary.GetPlayerControllerFromInputDevice")
+FunctionRedirects=(OldName="InputDeviceSubsystem.GetPlayerControllerFromPlatformUser", NewName="InputDeviceLibrary.GetPlayerControllerFromPlatformUser")
+FunctionRedirects=(OldName="InputDeviceSubsystem.IsDevicePropertyHandleValid", NewName="InputDeviceLibrary.IsDevicePropertyHandleValid")
+PropertyRedirects=(OldName="/Script/NavigationSystem.RecastNavMeshTileGenerationDebug.bHeightfieldSolidFromRasterization",NewName="bHeightfieldFromRasterization")
+PropertyRedirects=(OldName="/Script/NavigationSystem.RecastNavMeshTileGenerationDebug.bHeightfieldSolidPostInclusionBoundsFiltering",NewName="bHeightfieldPostInclusionBoundsFiltering")
+PropertyRedirects=(OldName="/Script/NavigationSystem.RecastNavMeshTileGenerationDebug.bHeightfieldSolidPostHeightFiltering",NewName="bHeightfieldPostHeightFiltering")
+PropertyRedirects=(OldName="/Script/NavigationSystem.RecastNavMeshTileGenerationDebug.bHeightfieldSolidBounds",NewName="bHeightfieldBounds")

+StructRedirects=(OldName="/Script/AssetTools.RevisionInfo", NewName="/Script/AssetDefinition.RevisionInfo")
+PropertyRedirects=(OldName="DecalComponent.SetFadeIn.Duaration", NewName="DecalComponent.SetFadeIn.Duration")
+PropertyRedirects=(OldName="BlendSpace.PerBoneBlend", NewName="BlendSpace.ManualPerBoneOverrides")
+ClassRedirects=(OldName="/Script/MovieRenderPipelineCore.MoviePipelineMasterConfig", NewName="/Script/MovieRenderPipelineCore.MoviePipelinePrimaryConfig")
+FunctionRedirects=(OldName="KismetMathLibrary.Conv_FloatToLinearColor", NewName="Conv_DoubleToLinearColor")
+FunctionRedirects=(OldName="KismetMathLibrary.Conv_ByteToFloat", NewName="Conv_ByteToDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.Conv_IntToFloat", NewName="Conv_IntToDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.Conv_BoolToFloat", NewName="Conv_BoolToDouble")
+FunctionRedirects=(OldName="KismetMathLibrary.Conv_FloatToVector", NewName="Conv_DoubleToVector")
+PropertyRedirects=(OldName="KismetMathLibrary.Conv_DoubleToLinearColor.InFloat", NewName="Conv_DoubleToLinearColor.InDouble")
+PropertyRedirects=(OldName="KismetMathLibrary.Conv_DoubleToVector.InFloat", NewName="Conv_DoubleToVector.InDouble")

+PropertyRedirects=(OldName="KismetSystemLibrary.MakeTopLevelAssetPath.FullPathOrPackageName", NewName="KismetSystemLibrary.MakeTopLevelAssetPath.PackageName")
+PropertyRedirects=(OldName="/Script/NavigationSystem.NavigationSystemV1.GeometryExportVertexCountWarningThreshold",NewName="/Script/NavigationSystem.NavigationSystemV1.GeometryExportTriangleCountWarningThreshold")
+PropertyRedirects=(OldName="ISMComponentDescriptor.bIsLocalToWorldDeterminantNegative", NewName="ISMComponentDescriptor.bReverseCulling")

+PropertyRedirects=(OldName="MaterialInstanceBasePropertyOverrides.MaxWorldPositionOffsetDistance", NewName="MaterialInstanceBasePropertyOverrides.MaxWorldPositionOffsetDisplacement")
+PropertyRedirects=(OldName="MaterialInstanceBasePropertyOverrides.bOverride_MaxWorldPositionOffsetDistance", NewName="MaterialInstanceBasePropertyOverrides.bOverride_MaxWorldPositionOffsetDisplacement")
+PropertyRedirects=(OldName="Material.MaxWorldPositionOffsetDistance", NewName="Material.MaxWorldPositionOffsetDisplacement")

+PropertyRedirects=(OldName="/Script/Engine.SoundAttenuationSettings.OmniRadius", NewName="NonSpatializedRadiusStart")

+FunctionRedirects=(OldName="KismetMathLibrary.BreakVector3f", NewName="BreakVector")

+StructRedirects=(OldName="/Script/UMG.FieldNotificationId", NewName="/Script/FieldNotification.FieldNotificationId")
+ClassRedirects=(OldName="/Script/UMG.NotifyFieldValueChanged", NewName="/Script/FieldNotification.NotifyFieldValueChanged")
+ClassRedirects=(OldName="/Script/UMGEditor.DetailsView", NewName="/Script/ScriptableEditorWidgets.DetailsView")
+ClassRedirects=(OldName="/Script/UMGEditor.SinglePropertyView", NewName="/Script/ScriptableEditorWidgets.SinglePropertyView")
+ClassRedirects=(OldName="/Script/UMGEditor.PropertyViewBase", NewName="/Script/ScriptableEditorWidgets.PropertyViewBase")
+StructRedirects=(OldName="/Script/AnimGraphRuntime.AnimNode_StateResult", NewName="/Script/Engine.AnimNode_StateResult")

+ClassRedirects=(OldName="HeadMountedDisplayFunctionLibrary", NewName="/Script/XRBase.HeadMountedDisplayFunctionLibrary")
+ClassRedirects=(OldName="MotionTrackedDeviceFunctionLibrary", NewName="/Script/XRBase.MotionTrackedDeviceFunctionLibrary")
+ClassRedirects=(OldName="VRNotificationsComponent", NewName="/Script/XRBase.VRNotificationsComponent")
+ClassRedirects=(OldName="XRAssetFunctionLibrary", NewName="/Script/XRBase.XRAssetFunctionLibrary")
+ClassRedirects=(OldName="AsyncTask_LoadXRDeviceVisComponent", NewName="/Script/XRBase.AsyncTask_LoadXRDeviceVisComponent")
+ClassRedirects=(OldName="XRDeviceVisualizationComponent", NewName="/Script/XRBase.XRDeviceVisualizationComponent")
+ClassRedirects=(OldName="XRLoadingScreenFunctionLibrary", NewName="/Script/XRBase.XRLoadingScreenFunctionLibrary")

+EnumRedirects=(OldName="ECameraShakeUpdateResultFlags",NewName="/Script/Engine.ECameraShakePatternUpdateResultFlags")
+StructRedirects=(OldName="CameraShakeStartParams",NewName="/Script/Engine.CameraShakePatternStartParams")
+StructRedirects=(OldName="CameraShakeUpdateParams",NewName="/Script/Engine.CameraShakePatternUpdateParams")
+StructRedirects=(OldName="CameraShakeScrubParams",NewName="/Script/Engine.CameraShakePatternScrubParams")
+StructRedirects=(OldName="CameraShakeStopParams",NewName="/Script/Engine.CameraShakePatternStopParams")
+StructRedirects=(OldName="CameraShakeUpdateResult",NewName="/Script/Engine.CameraShakePatternUpdateResult")

+ClassRedirects=(OldName="/Script/InterchangeImport.MaterialExpressionMaterialXExponential", NewName="/Script/Engine.MaterialExpressionExponential")
+ClassRedirects=(OldName="/Script/InterchangeImport.MaterialExpressionMaterialXHsvToRgb", NewName="/Script/Engine.MaterialExpressionHsvToRgb")
+ClassRedirects=(OldName="/Script/InterchangeImport.MaterialExpressionMaterialXLength", NewName="/Script/Engine.MaterialExpressionLength")
+ClassRedirects=(OldName="/Script/InterchangeImport.MaterialExpressionMaterialXLogarithm", NewName="/Script/Engine.MaterialExpressionLogarithm")
+ClassRedirects=(OldName="/Script/InterchangeImport.MaterialExpressionMaterialXRgbToHsv", NewName="/Script/Engine.MaterialExpressionRgbToHsv")
+ClassRedirects=(OldName="/Script/InterchangeImport.MaterialExpressionExponential", NewName="/Script/Engine.MaterialExpressionExponential")
+ClassRedirects=(OldName="/Script/InterchangeImport.MaterialExpressionHsvToRgb", NewName="/Script/Engine.MaterialExpressionHsvToRgb")
+ClassRedirects=(OldName="/Script/InterchangeImport.MaterialExpressionLength", NewName="/Script/Engine.MaterialExpressionLength")
+ClassRedirects=(OldName="/Script/InterchangeImport.MaterialExpressionLogarithm", NewName="/Script/Engine.MaterialExpressionLogarithm")
+ClassRedirects=(OldName="/Script/InterchangeImport.MaterialExpressionRgbToHsv", NewName="/Script/Engine.MaterialExpressionRgbToHsv")


+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataBSDF",							NewName="/Script/Engine.MaterialExpressionSubstrateBSDF")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataLegacyConversion",				NewName="/Script/Engine.MaterialExpressionSubstrateShadingModels")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataSlabBSDF",						NewName="/Script/Engine.MaterialExpressionSubstrateSlabBSDF")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataSimpleClearCoatBSDF",			NewName="/Script/Engine.MaterialExpressionSubstrateSimpleClearCoatBSDF")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataVolumetricFogCloudBSDF",		NewName="/Script/Engine.MaterialExpressionSubstrateVolumetricFogCloudBSDF")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataUnlitBSDF",					NewName="/Script/Engine.MaterialExpressionSubstrateUnlitBSDF")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataHairBSDF",						NewName="/Script/Engine.MaterialExpressionSubstrateHairBSDF")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataEyeBSDF",						NewName="/Script/Engine.MaterialExpressionSubstrateEyeBSDF")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataSingleLayerWaterBSDF",			NewName="/Script/Engine.MaterialExpressionSubstrateSingleLayerWaterBSDF")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataLightFunction",				NewName="/Script/Engine.MaterialExpressionSubstrateLightFunction")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataPostProcess",					NewName="/Script/Engine.MaterialExpressionSubstratePostProcess")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataUI",							NewName="/Script/Engine.MaterialExpressionSubstrateUI")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataConvertToDecal",				NewName="/Script/Engine.MaterialExpressionSubstrateConvertToDecal")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataHorizontalMixing",				NewName="/Script/Engine.MaterialExpressionSubstrateHorizontalMixing")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataVerticalLayering",				NewName="/Script/Engine.MaterialExpressionSubstrateVerticalLayering")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataAdd",							NewName="/Script/Engine.MaterialExpressionSubstrateAdd")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataWeight",						NewName="/Script/Engine.MaterialExpressionSubstrateWeight")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataUtilityBase",					NewName="/Script/Engine.MaterialExpressionSubstrateUtilityBase")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataTransmittanceToMFP",			NewName="/Script/Engine.MaterialExpressionSubstrateTransmittanceToMFP")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataMetalnessToDiffuseAlbedoF0",	NewName="/Script/Engine.MaterialExpressionSubstrateMetalnessToDiffuseAlbedoF0")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataHazinessToSecondaryRoughness",	NewName="/Script/Engine.MaterialExpressionSubstrateHazinessToSecondaryRoughness")
+ClassRedirects=(OldName="/Script/Engine.MaterialExpressionStrataThinFilm",						NewName="/Script/Engine.MaterialExpressionSubstrateThinFilm")
+StructRedirects=(OldName="/Script/Engine.StrataMaterialInput",									NewName="/Script/Engine.SubstrateMaterialInput")

+PackageRedirects=(OldName="/Engine/Functions/Strata/SMF_UE4Disney.SMF_UE4Disney",		NewName="/Engine/Functions/Substrate/SMF_UE4Legacy.SMF_UE4Legacy")
+PackageRedirects=(OldName="/Engine/Functions/Substrate/SMF_UE4Disney.SMF_UE4Disney",	NewName="/Engine/Functions/Substrate/SMF_UE4Legacy.SMF_UE4Legacy")

+EnumRedirects=(OldName="/Script/DataValidation.EDataValidationUsecase",NewName="/Script/CoreUObject.EDataValidationUsecase")
+FunctionRedirects=(OldName="EditorValidatorBase.ValidateLoadedAsset", NewName="EditorValidatorBase.K2_ValidateLoadedAsset")
+FunctionRedirects=(OldName="EditorValidatorBase.CanValidate", NewName="EditorValidatorBase.K2_CanValidate")
+FunctionRedirects=(OldName="EditorValidatorBase.CanValidateAsset", NewName="EditorValidatorBase.K2_CanValidateAsset")

+EnumRedirects=(OldName="/Script/Engine.ESubmixSendMethod",NewName="/Script/Engine.ESendLevelControlMethod")
+PropertyRedirects=(OldName="AttenuationSubmixSendSettings.Submix",NewName="AttenuationSubmixSendSettings.SoundSubmix")
+PropertyRedirects=(OldName="AttenuationSubmixSendSettings.SubmixSendMethod",NewName="AttenuationSubmixSendSettings.SendLevelControlMethod")
+PropertyRedirects=(OldName="AttenuationSubmixSendSettings.SubmixSendLevelMin",NewName="AttenuationSubmixSendSettings.MinSendLevel")
+PropertyRedirects=(OldName="AttenuationSubmixSendSettings.SubmixSendLevelMax",NewName="AttenuationSubmixSendSettings.MaxSendLevel")
+PropertyRedirects=(OldName="AttenuationSubmixSendSettings.SubmixSendDistanceMin",NewName="AttenuationSubmixSendSettings.MinSendDistance")
+PropertyRedirects=(OldName="AttenuationSubmixSendSettings.SubmixSendDistanceMax",NewName="AttenuationSubmixSendSettings.MaxSendDistance")
+PropertyRedirects=(OldName="AttenuationSubmixSendSettings.ManualSubmixSendLevel",NewName="AttenuationSubmixSendSettings.SendLevel")
+PropertyRedirects=(OldName="AttenuationSubmixSendSettings.CustomSubmixSendCurve",NewName="AttenuationSubmixSendSettings.CustomSendLevelCurve")

+EnumRedirects=(OldName="ETargetingTraceType", ValueChanges=(("Sweep", "Sphere")) )

+EnumRedirects=(OldName="/Script/SequencerScripting.ESequenceTimeUnit", NewName="/Script/MovieScene.EMovieSceneTimeUnit")


+StructRedirects=(OldName="/Script/Engine.HLODSubActor",							NewName="/Script/Engine.WorldPartitionRuntimeCellObjectMapping")
+PropertyRedirects=(OldName="WorldPartitionRuntimeCellObjectMapping.ActorPackage",	NewName="Package")
+PropertyRedirects=(OldName="WorldPartitionRuntimeCellObjectMapping.ActorPath",		NewName="Path")
+PropertyRedirects=(OldName="WorldPartitionRuntimeCellObjectMapping.ActorGuid",		NewName="ActorInstanceGuid")

+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetAnimationCurveNames.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddCurve.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.RemoveCurve.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.RemoveAllCurveData.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddTransformationCurveKey.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddTransformationCurveKeys.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddFloatCurveKey.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddFloatCurveKeys.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddVectorCurveKey.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.AddVectorCurveKeys.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetFloatKeys.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetVectorKeys.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.GetTransformationKeys.AnimationSequence",NewName="AnimationSequenceBase")
+PropertyRedirects=(OldName="AnimationBlueprintLibrary.DoesCurveExist.AnimationSequence",NewName="AnimationSequenceBase")

+EnumRedirects=(OldName="/Script/HeadMountedDisplay.EHMDTrackingOrigin",ValueChanges=(("Floor","LocalFloor"),("Eye","Local")) )

+StructRedirects=(OldName="FNetworkPhysicsDatas",NewName="FNetworkPhysicsData")

+PackageRedirects=(OldName="/Script/WindowsTargetPlatform", NewName="/Script/WindowsTargetPlatformSettings")
+PackageRedirects=(OldName="/Script/MacTargetPlatform", NewName="/Script/MacTargetPlatformSettings")
+PackageRedirects=(OldName="/Script/StructUtils", NewName="/Script/CoreUObject")
+PackageRedirects=(OldName="/Script/StructUtilsEngine", NewName="/Script/Engine")
+PackageRedirects=(OldName="/Script/StructUtilsNodes", NewName="/Script/BlueprintGraph")

+ClassRedirects=(OldName="/Script/Engine.UserDefinedStruct",NewName="/Script/CoreUObject.UserDefinedStruct")
+ClassRedirects=(OldName="/Script/StructUtils.StructUtilsFunctionLibrary",NewName="/Script/Engine.BlueprintInstancedStructLibrary")
+ClassRedirects=(OldName="/Script/StructUtilsEngine.StructUtilsFunctionLibrary",NewName="/Script/Engine.BlueprintInstancedStructLibrary")
+ClassRedirects=(OldName="/Script/Solaris.VerseClass",NewName="/Script/CoreUObject.VerseClass")
+ClassRedirects=(OldName="/Script/Solaris.VerseEnum",NewName="/Script/CoreUObject.VerseEnum")
+ClassRedirects=(OldName="/Script/Solaris.VerseStruct",NewName="/Script/CoreUObject.VerseStruct")

+StructRedirects=(OldName="/Script/StructUtils.UniqueScriptStructPtr", NewName="/Script/CoreUObject.InstancedStruct")
+StructRedirects=(OldName="/Script/StructUtils.InstancedStructArray", NewName="/Script/CoreUObject.InstancedStructContainer")

+EnumRedirects=(OldName="/Script/Engine.EUserDefinedStructureStatus",NewName="/Script/CoreUObject.EUserDefinedStructureStatus")
+EnumRedirects=(OldName="/Script/Solaris.EVerseFalse",NewName="/Script/CoreUObject.EVerseFalse")
+EnumRedirects=(OldName="/Script/Solaris.EVerseTrue",NewName="/Script/CoreUObject.EVerseTrue")
+EnumRedirects=(OldName="/Script/Solaris.EVersePackageScope",NewName="/Script/CoreUObject.EVersePackageScope")
+EnumRedirects=(OldName="/Script/Solaris.EVersePackageType",NewName="/Script/CoreUObject.EVersePackageType")

+PropertyRedirects=(OldName="K2Node_CallFunction.bIsPureFunc", NewName="bDefaultsToPureFunc")
+FunctionRedirects=(OldName="KismetMathLibrary.FMod", NewName="FMod64")
+FunctionRedirects=(OldName="KismetMathLibrary.MinimumAreaRectangle", NewName="MinAreaRectangle")
+PropertyRedirects=(OldName="KismetMathLibrary.MinAreaRectangle.InVerts", NewName="InPoints")

+PropertyRedirects=(OldName="NetworkPhysicsSettingsResimulation.bOverrideResimulationErrorThreshold", NewName="bOverrideResimulationErrorPositionThreshold")
+PropertyRedirects=(OldName="NetworkPhysicsSettingsResimulation.ResimulationErrorThreshold", NewName="ResimulationErrorPositionThreshold")

+ClassRedirects=(OldName="/Script/GameplayCameras.CompositeCameraShakePattern", NewName="/Script/EngineCameras.CompositeCameraShakePattern")
+ClassRedirects=(OldName="/Script/GameplayCameras.DefaultCameraShakeBase", NewName="/Script/EngineCameras.DefaultCameraShakeBase")
+ClassRedirects=(OldName="/Script/GameplayCameras.LegacyCameraShake", NewName="/Script/EngineCameras.LegacyCameraShake")
+ClassRedirects=(OldName="/Script/GameplayCameras.LegacyCameraShakePattern", NewName="/Script/EngineCameras.LegacyCameraShakePattern")
+ClassRedirects=(OldName="/Script/GameplayCameras.LegacyCameraShakeFunctionLibrary", NewName="/Script/EngineCameras.LegacyCameraShakeFunctionLibrary")
+ClassRedirects=(OldName="/Script/GameplayCameras.PerlinNoiseCameraShakePattern", NewName="/Script/EngineCameras.PerlinNoiseCameraShakePattern")
+ClassRedirects=(OldName="/Script/GameplayCameras.SimpleCameraShakePattern", NewName="/Script/EngineCameras.SimpleCameraShakePattern")
+ClassRedirects=(OldName="/Script/GameplayCameras.WaveOscillatorCameraShakePattern", NewName="/Script/EngineCameras.WaveOscillatorCameraShakePattern")
+ClassRedirects=(OldName="/Script/GameplayCameras.CameraAnimationCameraModifier", NewName="/Script/EngineCameras.CameraAnimationCameraModifier")
+ClassRedirects=(OldName="/Script/GameplayCameras.GameplayCamerasFunctionLibrary", NewName="/Script/EngineCameras.EngineCameraAnimationFunctionLibrary")
+ClassRedirects=(OldName="/Script/GameplayCameras.GameplayCamerasSubsystem", NewName="/Script/EngineCameras.EngineCamerasSubsystem")

+StructRedirects=(OldName="/Script/GameplayCameras.PerlinNoiseShaker", NewName="/Script/EngineCameras.PerlinNoiseShaker")
+StructRedirects=(OldName="/Script/GameplayCameras.WaveOscillator", NewName="/Script/EngineCameras.WaveOscillator")
+StructRedirects=(OldName="/Script/GameplayCameras.CameraAnimationParams", NewName="/Script/EngineCameras.CameraAnimationParams")
+StructRedirects=(OldName="/Script/GameplayCameras.CameraAnimationHandle", NewName="/Script/EngineCameras.CameraAnimationHandle")
+StructRedirects=(OldName="/Script/GameplayCameras.ActiveCameraAnimationInfo", NewName="/Script/EngineCameras.ActiveCameraAnimationInfo")

+EnumRedirects=(OldName="/Script/GameplayCameras.EInitialWaveOscillatorOffsetType", NewName="/Script/EngineCameras.EInitialWaveOscillatorOffsetType")
+EnumRedirects=(OldName="/Script/GameplayCameras.ECameraAnimationPlaySpace", NewName="/Script/EngineCameras.ECameraAnimationPlaySpace")
+EnumRedirects=(OldName="/Script/GameplayCameras.ECameraAnimationEasingType", NewName="/Script/EngineCameras.ECameraAnimationEasingType")

+FunctionRedirects=(OldName="SkeletalMeshComponent.SetAnimClass",NewName="SetAnimInstanceClass")
+FunctionRedirects=(OldName="SkeletalMeshComponent.K2_SetAnimInstanceClass",NewName="SetAnimInstanceClass")


+FunctionRedirects=(OldName="/Script/GameplayTags.GameplayTagAssetInterface.GetOwnedGameplayTags", NewName="BP_GetOwnedGameplayTags")
+PropertyRedirects=(OldName="GameplayTagAssetInterface.BP_GetOwnedGameplayTags.TagContainer", NewName="ReturnValue")

[CoreUObject.Metadata]
+MetadataRedirects=(OldKey="K2Protected", NewKey="BlueprintProtected")
+MetadataRedirects=(OldKey="K2UnsafeForConstructionScripts", NewKey="UnsafeDuringActorConstruction")
+MetadataRedirects=(OldKey="KismetType", NewKey="BlueprintType")
+MetadataRedirects=(OldKey="KismetInternalUseOnly", NewKey="BlueprintInternalUseOnly")
+MetadataRedirects=(OldKey="KismetSpawnableComponent", NewKey="BlueprintSpawnableComponent")
+MetadataRedirects=(OldKey="K2ExposeToSpawn", NewKey="ExposeOnSpawn")
+MetadataRedirects=(OldKey="K2Category", NewKey="Category")
+MetadataRedirects=(OldKey="KismetDeprecated", NewKey="DeprecatedFunction")
+MetadataRedirects=(OldKey="K2CompactNode", NewKey="CompactNodeTitle")
+MetadataRedirects=(OldKey="MenuCategory", NewKey="Category")
+MetadataRedirects=(OldKey="ArrayPointerParm", NewKey="TargetArrayParm")
+MetadataRedirects=(OldKey="FriendlyName", NewKey="DisplayName")

[EnumRemap]

[PlatformInterface]
CloudStorageInterfaceClassName=
InGameAdManagerClassName=

[/Script/Engine.UserInterfaceSettings]
UIScaleRule=ShortestSide
UIScaleCurve=(EditorCurveData=(Keys=((Time=480,Value=0.444),(Time=720,Value=0.666),(Time=1080,Value=1.0),(Time=8640,Value=8.0))),ExternalCurve=None)
bLoadWidgetsOnDedicatedServer=True
bAllowHighDPIInGameMode=False


[/Script/IrisCore.PartialNetObjectAttachmentHandlerConfig]
MaxPartCount=4096

[/Script/IrisCore.NetBlobHandlerDefinitions]
+NetBlobHandlerDefinitions=(ClassName=NetRPCHandler)
+NetBlobHandlerDefinitions=(ClassName=PartialNetObjectAttachmentHandler )
+NetBlobHandlerDefinitions=(ClassName=NetObjectBlobHandler)

[/Script/IrisCore.DataStreamDefinitions]
+DataStreamDefinitions=(DataStreamName=NetToken, ClassName=/Script/IrisCore.NetTokenDataStream, DefaultSendStatus=EDataStreamSendStatus::Send, bAutoCreate=true)
+DataStreamDefinitions=(DataStreamName=Replication, ClassName=/Script/IrisCore.ReplicationDataStream, DefaultSendStatus=EDataStreamSendStatus::Send, bAutoCreate=true)

[/Script/IrisCore.NetObjectPrioritizerDefinitions]
+NetObjectPrioritizerDefinitions=(PrioritizerName=DefaultPrioritizer, ClassName=/Script/IrisCore.SphereNetObjectPrioritizer, ConfigClassName=/Script/IrisCore.SphereNetObjectPrioritizerConfig)
+NetObjectPrioritizerDefinitions=(PrioritizerName=PlayerStatePrioritizer, ClassName=/Script/IrisCore.NetObjectCountLimiter, ConfigClassName=/Script/Engine.PlayerStateCountLimiterConfig)

[/Script/IrisCore.NetObjectFilterDefinitions]
+NetObjectFilterDefinitions=(FilterName=Spatial, ClassName=/Script/IrisCore.NetObjectGridWorldLocFilter, ConfigClassName=/Script/IrisCore.NetObjectGridFilterConfig)
+NetObjectFilterDefinitions=(FilterName=NotRouted, ClassName=/Script/IrisCore.FilterOutNetObjectFilter, ConfigClassName=/Script/IrisCore.FilterOutNetObjectFilterConfig)

[/Script/IrisCore.ReplicationFilteringConfig]
bEnableObjectScopeHysteresis=true
DefaultHysteresisFrameCount=6

[/Script/IrisCore.ObjectReplicationBridgeConfig]
RequiredNetDriverChannelClassName=/Script/Engine.DataStreamChannel


DefaultSpatialFilterName=Spatial
!FilterConfigs=ClearArray
+FilterConfigs=(ClassName=/Script/Engine.Actor, DynamicFilterName=None))
+FilterConfigs=(ClassName=/Script/Engine.Info, DynamicFilterName=None)
+FilterConfigs=(ClassName=/Script/Engine.PlayerState, DynamicFilterName=None)

+PrioritizerConfigs=(ClassName=/Script/Engine.PlayerState, PrioritizerName=PlayerState, bForceEnableOnAllInstances=true)

+DeltaCompressionConfigs=(ClassName=/Script/Engine.Pawn))
+DeltaCompressionConfigs=(ClassName=/Script/Engine.PlayerState))
+DeltaCompressionConfigs=(ClassName=/Script/GameplayDebugger.GameplayDebuggerCategoryReplicator))

+CriticalClassConfigs=(ClassName=/Script/Engine.PlayerController)

[/Script/IrisCore.ReplicationStateDescriptorConfig]
+SupportsStructNetSerializerList=(StructName=GameplayCueParameters)
+SupportsStructNetSerializerList=(StructName=GameplayAbilityTargetData_LocationInfo)
+SupportsStructNetSerializerList=(StructName=GameplayAbilityTargetData_ActorArray)
+SupportsStructNetSerializerList=(StructName=GameplayAbilityTargetData_SingleTargetHit)
+SupportsStructNetSerializerList=(StructName=NetLevelVisibilityTransactionId)
+SupportsStructNetSerializerList=(StructName=Vector2D)
+SupportsStructNetSerializerList=(StructName=Vector2f)
+SupportsStructNetSerializerList=(StructName=GameplayDebuggerNetPack)
+SupportsStructNetSerializerList=(StructName=RootMotionSource)
+SupportsStructNetSerializerList=(StructName=RootMotionSource_ConstantForce)
+SupportsStructNetSerializerList=(StructName=RootMotionSource_JumpForce)
+SupportsStructNetSerializerList=(StructName=RootMotionSource_MoveToDynamicForce)
+SupportsStructNetSerializerList=(StructName=RootMotionSource_MoveToForce)
+SupportsStructNetSerializerList=(StructName=RootMotionSource_RadialForce)


[/Script/Engine.GameEngine]
MaxDeltaTime=0
ServerFlushLogInterval=30

[Engine.StartupPackages]
+Package=/Engine/EngineMaterials/BlinkingCaret
+Package=/Engine/EngineMaterials/DefaultBokeh
+Package=/Engine/EngineMaterials/DefaultBloomKernel
+Package=/Engine/EngineMaterials/DefaultDeferredDecalMaterial
+Package=/Engine/EngineMaterials/DefaultDiffuse
+Package=/Engine/EngineMaterials/DefaultLightFunctionMaterial
+Package=/Engine/EngineMaterials/WorldGridMaterial
+Package=/Engine/EngineMaterials/DefaultMaterial
+Package=/Engine/EngineMaterials/DefaultNormal
+Package=/Engine/EngineMaterials/DefaultPhysicalMaterial
+Package=/Engine/EngineMaterials/DefaultWhiteGrid
+Package=/Engine/EngineMaterials/EditorBrushMaterial
+Package=/Engine/EngineMaterials/EmissiveMeshMaterial
+Package=/Engine/EngineMaterials/Good64x64TilingNoiseHighFreq
+Package=/Engine/EngineMaterials/Grid
+Package=/Engine/EngineMaterials/Grid_N
+Package=/Engine/EngineMaterials/LandscapeHolePhysicalMaterial
+Package=/Engine/EngineMaterials/MiniFont
+Package=/Engine/EngineMaterials/PaperDiffuse
+Package=/Engine/EngineMaterials/PaperNormal
+Package=/Engine/EngineMaterials/PhysMat_Rubber
+Package=/Engine/EngineMaterials/PreintegratedSkinBRDF
+Package=/Engine/EngineMaterials/RemoveSurfaceMaterial
+Package=/Engine/EngineMaterials/WeightMapPlaceholderTexture
+Package=/Engine/EngineMaterials/NaniteHiddenSectionMaterial

+Package=/Engine/EngineDebugMaterials/BoneWeightMaterial
+Package=/Engine/EngineDebugMaterials/DebugMeshMaterial
+Package=/Engine/EngineDebugMaterials/GeomMaterial
+Package=/Engine/EngineDebugMaterials/HeatmapGradient
+Package=/Engine/EngineDebugMaterials/LevelColorationLitMaterial
+Package=/Engine/EngineDebugMaterials/LevelColorationUnlitMaterial
+Package=/Engine/EngineDebugMaterials/MAT_LevelColorationLitLightmapUV
+Package=/Engine/EngineDebugMaterials/ShadedLevelColorationLitMaterial
+Package=/Engine/EngineDebugMaterials/ShadedLevelColorationUnlitMateri
+Package=/Engine/EngineDebugMaterials/TangentColorMap
+Package=/Engine/EngineDebugMaterials/VertexColorMaterial
+Package=/Engine/EngineDebugMaterials/VertexColorViewMode_AlphaAsColor
+Package=/Engine/EngineDebugMaterials/VertexColorViewMode_BlueOnly
+Package=/Engine/EngineDebugMaterials/VertexColorViewMode_ColorOnly
+Package=/Engine/EngineDebugMaterials/VertexColorViewMode_GreenOnly
+Package=/Engine/EngineDebugMaterials/VertexColorViewMode_RedOnly
+Package=/Engine/EngineDebugMaterials/WireframeMaterial

+Package=/Engine/EngineSounds/WhiteNoise

+Package=/Engine/EngineFonts/SmallFont
+Package=/Engine/EngineFonts/TinyFont
+Package=/Engine/EngineFonts/Roboto
+Package=/Engine/EngineFonts/RobotoTiny

+Package=/Engine/EngineMaterials/DefaultTextMaterialTranslucent
+Package=/Engine/EngineFonts/RobotoDistanceField

[Core.Log]

[Core.System]
+Paths=../../../Engine/Content
+Paths=%GAMEDIR%Content
CutdownPaths=%GAMEDIR%CutdownPackages
ZeroEngineVersionWarning=True
UseStrictEngineVersioning=True
CanStripEditorOnlyExportsAndImports=True
CanSkipEditorReferencedPackagesWhenCooking=False
CanUseUnversionedPropertySerialization=True
TestUnversionedPropertySerializationWhenCooking=False
DetailedCallstacksInNonMonolithicBuilds=True
UseSeperateBulkDataFiles=False
GameThreadHeartBeatStartSuspended=False
GameThreadHeartBeatHitchDuration=-1.0
GameThreadHeartBeatStackWalk=false
IgnoreInlineBulkDataReloadEnsures=False
HangDuration=0.0
AssetLogShowsDiskPath=True
AssetLogShowsAbsolutePath=True
TaskThreadStackSize=393216
PoolThreadStackSize=131072
DefaultThreadStackSize=1048576

[Core.System.Experimental]
EnablePackageSidecarSaving=False
AllowVirtualizationOptOut =False

[Core.ContentVirtualization]
SystemName=None
LazyInit=False
InitPreSlate=False

[Core.VirtualizationModule]
EnablePayloadVirtualization=True
EnableCacheOnPull=True
EnableCacheOnPush=True
MinPayloadLength=0
BackendGraph=ContentVirtualizationBackendGraph_None
VirtualizationProcessTag="#virtualized"
AllowSubmitIfVirtualizationFailed=false
FilterMode=OptOut
FilterMapContent=True
LazyInitConnections=False
DisableLazyInitIfInteractive=False
UseLegacyErrorHandling=False
ForceCachingOnPull=False
PullErrorAdditionalMsg=""
ConnectionHelpUrl=""
UnattendedRetryCount=0
UnattendedRetryTimer=0

[/Script/Virtualization.VirtualizationFilterSettings]
!ExcludePackagePaths=ClearArray

[ContentVirtualizationBackendGraph_None]
CacheStorageHierarchy=()
PersistentStorageHierarchy=()

[ContentVirtualizationBackendGraph_LocalCache]
CacheStorageHierarchy=(Entry=LocalFileCache)
PersistentStorageHierarchy=()
LocalFileCache=(Type=FileSystem)

[/Script/Engine.StreamingSettings]
s.MinBulkDataSizeForAsyncLoading=131072
s.AsyncLoadingThreadEnabled=True
s.EventDrivenLoaderEnabled=True
s.WarnIfTimeLimitExceeded=False
s.TimeLimitExceededMultiplier=1.5
s.TimeLimitExceededMinTime=0.005
s.UseBackgroundLevelStreaming=True
s.PriorityAsyncLoadingExtraTime=15.0
s.LevelStreamingActorsUpdateTimeLimit = 5.0
s.PriorityLevelStreamingActorsUpdateExtraTime=5.0
s.LevelStreamingComponentsRegistrationGranularity = 10
s.UnregisterComponentsTimeLimit = 1.0
s.LevelStreamingComponentsUnregistrationGranularity = 5
s.MaxPackageSummarySize=16384
s.FlushStreamingOnExit=True
+FixedBootOrder=/Script/Engine/Default__SoundBase
+FixedBootOrder=/Script/Engine/Default__MaterialInterface
+FixedBootOrder=/Script/Engine/Default__DeviceProfileManager

[/Script/Engine.GarbageCollectionSettings]
gc.MaxObjectsNotConsideredByGC=1
gc.FlushStreamingOnGC=0
gc.NumRetriesBeforeForcingGC=10
gc.AllowParallelGC=True
gc.TimeBetweenPurgingPendingKillObjects=61.1
gc.MaxObjectsInEditor=25165824
gc.IncrementalBeginDestroyEnabled=True
gc.CreateGCClusters=True
gc.MinGCClusterSize=5
gc.AssetClustreringEnabled=False
gc.ActorClusteringEnabled=False
gc.VerifyUObjectsAreNotFGCObjects=False
gc.GarbageEliminationEnabled=True

[Internationalization]
+LocalizationPaths=../../../Engine/Content/Localization/Engine
+CultureDisplayNameSubstitutes="Taiwan;Chinese Taipei"
+CultureDisplayNameSubstitutes="ja;\u53f0\u6e7e;\u30c1\u30e3\u30a4\u30cb\u30fc\u30ba\u30bf\u30a4\u30da\u30a4"
+CultureDisplayNameSubstitutes="\u53f0\u6e7e;\u4e2d\u534e\u53f0\u5317"
+CultureDisplayNameSubstitutes="\u53f0\u7063;\u4e2d\u83ef\u53f0\u5317"
+CultureDisplayNameSubstitutes="\ub300\ub9cc;\uc911\ud654 \ud0c0\uc774\ubca0\uc774"

[Audio]
UnfocusedVolumeMultiplier=0.0
UseAudioThread=true
EnableAudioMixer=false
DeferStartupPrecache=false
+AllWaveFormats=BINKA
+AllWaveFormats=RADA
+AllWaveFormats=ADPCM
+AllWaveFormats=PCM
+AllWaveFormats=OPUS
+AllWaveFormats=OGG
+FormatModuleHints=AudioFormatOPUS
+FormatModuleHints=AudioFormatOGG
+FormatModuleHints=AudioFormatADPCM
+FormatModuleHints=AudioFormatBINK
+FormatModuleHints=AudioFormatRAD
+AudioInfoModules=OpusAudioDecoder
+AudioInfoModules=VorbisAudioDecoder
+AudioInfoModules=AdpcmAudioDecoder
+AudioInfoModules=BinkAudioDecoder
+AudioInfoModules=RadAudioDecoder

FallbackFormat=BINKA
PlatformFormat=BINKA
PlatformStreamingFormat=BINKA

[AudioChannelAzimuthMap]
FrontLeft=330
FrontRight=30
FrontCenter=0
BackLeft=210
BackRight=150
FrontLeftOfCenter=15
FrontRightOfCenter=345
BackCenter=180
SideLeft=250
SideRight=110

[AudioDefaultChannelOrder]
FrontLeft=0
FrontRight=1
FrontCenter=2
LowFrequency=3
SideLeft=4
SideRight=5
BackLeft=6
BackRight=7

[/Script/Engine.AudioSettings]
DefaultSoundClassName=/Engine/EngineSounds/Master.Master
DefaultMediaSoundClassName=/Engine/EngineSounds/Master.Master
DefaultSoundSubmixName=/Engine/EngineSounds/MasterSubmix.MasterSubmix
MasterSubmix=/Engine/EngineSounds/Submixes/MasterSubmixDefault.MasterSubmixDefault
ReverbSubmix=/Engine/EngineSounds/Submixes/MasterReverbSubmixDefault.MasterReverbSubmixDefault
EQSubmix=/Engine/EngineSounds/Submixes/MasterEQSubmixDefault.MasterEQSubmixDefault
AmbisonicSubmix=/Engine/EngineSounds/Submixes/MasterAmbisonicSubmixDefault.MasterAmbisonicSubmixDefault
LowPassFilterResonance=0.9
MaximumConcurrentStreams=2
DialogueFilenameFormat="{DialogueGuid}_{ContextId}"

[/Script/Engine.SoundGroups]
+SoundGroupProfiles=(SoundGroup=SOUNDGROUP_Default, bAlwaysDecompressOnLoad=false, DecompressedDuration=5)
+SoundGroupProfiles=(SoundGroup=SOUNDGROUP_Effects, bAlwaysDecompressOnLoad=false, DecompressedDuration=5)
+SoundGroupProfiles=(SoundGroup=SOUNDGROUP_UI, bAlwaysDecompressOnLoad=false, DecompressedDuration=5)
+SoundGroupProfiles=(SoundGroup=SOUNDGROUP_Music, bAlwaysDecompressOnLoad=false, DecompressedDuration=0)
+SoundGroupProfiles=(SoundGroup=SOUNDGROUP_Voice, bAlwaysDecompressOnLoad=false, DecompressedDuration=0)

[/Script/Engine.Player]
ConfiguredInternetSpeed=100000
ConfiguredLanSpeed=100000

[/Script/Engine.NetDriver]
+ChannelDefinitions=(ChannelName=Control, ClassName=/Script/Engine.ControlChannel, StaticChannelIndex=0, bTickOnCreate=true, bServerOpen=false, bClientOpen=true, bInitialServer=false, bInitialClient=true)
+ChannelDefinitions=(ChannelName=Voice, ClassName=/Script/Engine.VoiceChannel, StaticChannelIndex=1, bTickOnCreate=true, bServerOpen=true, bClientOpen=true, bInitialServer=true, bInitialClient=true)
+ChannelDefinitions=(ChannelName=DataStream, ClassName=/Script/Engine.DataStreamChannel, StaticChannelIndex=2, bTickOnCreate=true, bServerOpen=true, bClientOpen=true, bInitialServer=true, bInitialClient=true)
+ChannelDefinitions=(ChannelName=Actor, ClassName=/Script/Engine.ActorChannel, StaticChannelIndex=-1, bTickOnCreate=false, bServerOpen=true, bClientOpen=false, bInitialServer=false, bInitialClient=false)

[/Script/Engine.RendererSettings]
VisualizeCalibrationColorMaterialPath=/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor
VisualizeCalibrationGrayscaleMaterialPath=/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale

[/Script/OnlineSubsystemUtils.IpNetDriver]
AllowPeerConnections=False
AllowPeerVoice=False
ConnectionTimeout=60.0
InitialConnectTimeout=60.0
RecentlyDisconnectedTrackingTime=120
TimeoutMultiplierForUnoptimizedBuilds=1
KeepAliveTime=0.2
MaxClientRate=100000
MaxInternetClientRate=100000
RelevantTimeout=5.0
SpawnPrioritySeconds=1.0
ServerTravelPause=4.0
NetServerMaxTickRate=30
MaxNetTickRate=120
NetConnectionClassName="/Script/OnlineSubsystemUtils.IpConnection"
MaxPortCountToTry=512
ResolutionConnectionTimeout=20.0

[DDoSDetection]
bDDoSDetection=false
bDDoSAnalytics=false
DDoSLogSpamLimit=64
HitchTimeQuotaMS=500
HitchFrameTolerance=3
+DetectionSeverity=Burst
+DetectionSeverity=PersistentBurst
+DetectionSeverity=DDoS
+DetectionSeverity=ExpensiveDDoS
+DetectionSeverity=DebilitatingDDoS

[DDoSDetection.Burst]
bSendEscalateAnalytics=false
EscalateQuotaPacketsPerSec=800
EscalateQuotaDisconnPacketsPerSec=3200
EscalateQuotaBadPacketsPerSec=400

[DDoSDetection.PersistentBurst]
bSendEscalateAnalytics=false
EscalateQuotaPacketsPerSec=1600
EscalateQuotaDisconnPacketsPerSec=3200
EscalateQuotaBadPacketsPerSec=800
EscalateTimeQuotaMSPerFrame=10
CooloffTime=10

[DDoSDetection.DDoS]
bSendEscalateAnalytics=false
EscalateTimeQuotaMSPerFrame=50
PacketLimitPerFrame=1000
PacketTimeLimitMSPerFrame=16
CooloffTime=10

[DDoSDetection.ExpensiveDDoS]
bSendEscalateAnalytics=true
EscalateTimeQuotaMSPerFrame=66
PacketLimitPerFrame=100
PacketTimeLimitMSPerFrame=2
CooloffTime=10

[DDoSDetection.DebilitatingDDoS]
bSendEscalateAnalytics=true
PacketLimitPerFrame=0
NetConnPacketTimeLimitMSPerFrame=100
CooloffTime=10

[GameNetDriver RPCDoSDetection]
bRPCDoSDetection=false
bRPCDoSAnalytics=false
HitchTimeQuotaMS=1000
HitchSuspendDetectionTimeMS=1000
InitialConnectToleranceMS=2000
RPCBlockWhitelist=ServerUpdateCamera
RPCBlockWhitelist=ServerMove
RPCBlockWhitelist=ServerMovePacked
+DetectionSeverity=Normal
+DetectionSeverity=Hitch
+DetectionSeverity=Burst
+DetectionSeverity=PersistentBurst
+DetectionSeverity=DoS
+DetectionSeverity=ExpensiveDoS
+DetectionSeverity=Kick

[BeaconNetDriver RPCDoSDetection]
bRPCDoSDetection=false
bRPCDoSAnalytics=false
HitchTimeQuotaMS=1000
HitchSuspendDetectionTimeMS=1000
InitialConnectToleranceMS=2000
+DetectionSeverity=Normal
+DetectionSeverity=Hitch
+DetectionSeverity=Burst
+DetectionSeverity=PersistentBurst
+DetectionSeverity=DoS
+DetectionSeverity=ExpensiveDoS
+DetectionSeverity=Kick

[RPCDoSDetection.Normal]
EscalateQuotaRPCsPerFrame=256
EscalateTimeQuotaMSPerFrame=10

[RPCDoSDetection.Hitch]
EscalateQuotaRPCsPerPeriod=512
EscalateTimeQuotaMSPerPeriod=50
EscalateQuotaTimePeriod=2
CooloffTime=5
AutoEscalateTime=10

[RPCDoSDetection.Burst]
EscalateQuotaRPCsPerPeriod=1024
EscalateTimeQuotaMSPerPeriod=512
EscalateQuotaTimePeriod=8
CooloffTime=5
AutoEscalateTime=8

[RPCDoSDetection.PersistentBurst]
bLogEscalate=true
bSendEscalateAnalytics=true
bTrackRecentRPCs=true
EscalateQuotaRPCsPerPeriod=2048
EscalateTimeQuotaMSPerPeriod=1024
EscalateQuotaTimePeriod=8
EscalationCountTolerance=15
EscalationTimeToleranceMS=8000
CooloffTime=5
AutoEscalateTime=8

[RPCDoSDetection.DoS]
bLogEscalate=true
bSendEscalateAnalytics=true
bTrackRecentRPCs=true
EscalateQuotaRPCsPerPeriod=4096
EscalateTimeQuotaMSPerPeriod=2048
EscalateQuotaTimePeriod=8
EscalationCountTolerance=10
EscalationTimeToleranceMS=8000
RPCRepeatLimitPerPeriod=512
RPCRepeatLimitMSPerPeriod=1024
RPCRepeatLimitTimePeriod=4
CooloffTime=10
AutoEscalateTime=11

[RPCDoSDetection.ExpensiveDoS]
bLogEscalate=true
bSendEscalateAnalytics=true
bTrackRecentRPCs=true
EscalateQuotaRPCsPerPeriod=32768
EscalateTimeQuotaMSPerPeriod=8192
EscalateQuotaTimePeriod=16
EscalationCountTolerance=5
EscalationTimeToleranceMS=8000
RPCRepeatLimitPerPeriod=512
RPCRepeatLimitMSPerPeriod=1024
RPCRepeatLimitTimePeriod=4
CooloffTime=10

[RPCDoSDetection.Kick]
bLogEscalate=true
bSendEscalateAnalytics=true
bKickPlayer=true


[/Script/Engine.DemoNetDriver]
NetConnectionClassName="/Script/Engine.DemoNetConnection"
DemoSpectatorClass=Engine.PlayerController
SpawnPrioritySeconds=60.0
!ChannelDefinitions=CLEAR_ARRAY
+ChannelDefinitions=(ChannelName=Control, ClassName=/Script/Engine.ControlChannel, StaticChannelIndex=0, bTickOnCreate=true, bServerOpen=false, bClientOpen=true, bInitialServer=false, bInitialClient=true)
+ChannelDefinitions=(ChannelName=Actor, ClassName=/Script/Engine.ActorChannel, StaticChannelIndex=-1, bTickOnCreate=false, bServerOpen=true, bClientOpen=false, bInitialServer=false, bInitialClient=false)

[TextureStreaming]
NeverStreamOutRenderAssets=False
MinTextureResidentMipCount=7
PoolSize=160
MemoryMargin=5
MinFudgeFactor=1
LoadMapTimeLimit=20.0
LightmapStreamingFactor=0.2
ShadowmapStreamingFactor=0.2
MaxLightmapRadius=10000.0
AllowStreamingLightmaps=True
UseDynamicStreaming=True
BoostPlayerTextures=3.0

[/Script/UnrealEd.EditorEngine]
LocalPlayerClassName=/Script/Engine.LocalPlayer
GameCommandLine=-log
FOVAngle=90.000000
GodMode=True
UseAxisIndicator=True
HeightMapExportClassName="TerrainHeightMapExporterTextT3D"
bCustomCameraAlignEmitter=true
CustomCameraAlignEmitterDistance=100.0
bDrawSocketsInGMode=false
bSmoothFrameRate=false
SmoothedFrameRateRange=(LowerBound=(Type="ERangeBoundTypes::Inclusive",Value=5),UpperBound=(Type="ERangeBoundTypes::Inclusive",Value=120))
UseOldStyleMICEditorGroups=true
InEditorGameURLOptions=

[/Script/UnrealEd.UnrealEdEngine]
AutoSaveIndex=0
+TemplateMapInfos=(Thumbnail="/Engine/Maps/Templates/Thumbnails/OpenWorld.OpenWorld",Map="/Engine/Maps/Templates/OpenWorld", DisplayName=NSLOCTEXT("NewLevelDialog", "OpenWorld", "Open World"), Category="OpenWorld")
+TemplateMapInfos=(Thumbnail="/Engine/Maps/Templates/Thumbnails/Basic.Basic",Map="/Engine/Maps/Templates/Template_Default", DisplayName=NSLOCTEXT("NewLevelDialog", "Basic", "Basic"))

+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Cross
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Cross_Mat
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/PhAT_BoneSelectedMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/PhAT_ElemSelectedMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/PhAT_JointLimitMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/PhAT_NoCollisionMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/PhAT_UnselectedMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/TargetIcon
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Tick
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Tick_Mat
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetGridVertexColorMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetGridVertexColorMaterial_Ma
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetMaterial_Current
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetMaterial_X
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetMaterial_Y
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetMaterial_Z
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetVertexColorMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/LevelGridMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/TilingAAGrid
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/TilingAALineBoxFiltered
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/TilingAALineIntegral
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/ParticleSystems/PSysThumbnail_NoImage
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/ParticleSystems/PSysThumbnail_OOD
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Thumbnails/FloorPlaneMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Thumbnails/SkySphereMaterial

+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMeshes/EditorCube
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMeshes/EditorCylinder
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMeshes/EditorPlane
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMeshes/EditorSkySphere
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMeshes/EditorSphere

+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/Bad
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/Bkgnd
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/BkgndHi
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/BSPVertex
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/MatInstActSprite
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/SceneManager
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/SmallFont
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Actor
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_TextRenderActorIcon
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Emitter
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_ExpoHeightFog
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_KBSJoint
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_KHinge
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_KPrismatic
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_LevelSequence
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_NavP
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Note
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Player
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_RadForce
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_ReflActorIcon
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Thruster
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Trigger
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_VectorFieldVol
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/AI/S_NavLink
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightDirectional
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightDirectionalMove
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightError
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightPoint
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightPointMove
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightSpot
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightSpotMove
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/BlinkingCaret
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultBokeh
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultBloomKernel
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultDeferredDecalMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultDiffuse
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultLightFunctionMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/WorldGridMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultNormal
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultPhysicalMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultWhiteGrid
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/EditorBrushMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/Good64x64TilingNoiseHighFreq
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/Grid
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/Grid_N
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/HighResScreenshot
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/HighResScreenshotMask
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/HighResScreenshotCaptureRegion
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/LandscapeHolePhysicalMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/MiniFont
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/PaperDiffuse
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/PaperNormal
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/PhysMat_Rubber
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/PreintegratedSkinBRDF
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/RemoveSurfaceMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/WeightMapPlaceholderTexture
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/NaniteHiddenSectionMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/Blends/MLB_Horizontal

+PackagesToBeFullyLoadedAtStartup=/Engine/EngineFonts/SmallFont
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineFonts/TinyFont
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineFonts/Roboto
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineFonts/RobotoTiny

+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/Black
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/DefaultTexture
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/DefaultTextureCube
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/M_StreamingPause
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/WhiteSquareTexture
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/GradientTexture0

[DevOptions.Shaders]
bAllowCompilingThroughWorkers=True
bAllowAsynchronousShaderCompiling=True
NumUnusedShaderCompilingThreads=3
NumUnusedShaderCompilingThreadsDuringGame=4
ShaderCompilerCoreCountThreshold=12
PercentageUnusedShaderCompilingThreads=50
MaxShaderJobBatchSize=10
bPromptToRetryFailedShaderCompiles=False
bDebugBreakOnPromptToRetryShaderCompile=False
bLogJobCompletionTimes=False
ProcessGameThreadTargetTime=.01
WorkerTimeToLive=20
BuildWorkerTimeToLive=1200
WorkerProcessPriority=-1

bUseVirtualCores = False

bForceUseSCWMemoryPressureLimits = False

bDoNotChunkPSOCache=True

[ShaderCompiler]
r.Shaders.Symbols=0
r.Shaders.GenerateSymbols=0
r.Shaders.WriteSymbols=0
r.Shaders.WriteSymbols.Zip=0
r.Shaders.SymbolPathOverride=""
r.Shaders.AllowUniqueSymbols=0

[ShaderCompiler_BuildMachine]
r.Shaders.Symbols=0
r.Shaders.GenerateSymbols=0
r.Shaders.WriteSymbols=0
r.Shaders.WriteSymbols.Zip=0
r.Shaders.SymbolPathOverride=""
r.Shaders.AllowUniqueSymbols=0

[Rendering.AutoScreenPercentage]
MinDisplayResolution=720
MinRenderingResolution=720
MidDisplayResolution=2160
MidRenderingResolution=1080
MaxDisplayResolution=4320
MaxRenderingResolution=1440

[LogFiles]
PurgeLogsDays=5
MaxLogFilesOnDisk=10
LogTimes=True

[Kismet]
AllowDerivedBlueprints=true
CompileDisplaysBinaryBackend=false
CompileDisplaysAnimBlueprintBackend=false
bTurnOffEditorConstructionScript=false
bLogPrintStringSource=true
PrintStringDuration=2.0
bUseLocalGraphVariables=false
bPersistentUberGraphFrame=true
bReinstanceOnlyWhenNecessary=true
bChangeDefaultValueWithoutReinstancing=true
bDisplaysLayout=false
bOptimizeExecutionFlowStack=true
bOptimizeAdjacentStates=true
bEnableInheritableComponents=true
bDeferDependencyLoads=true
bForceDisableCookedDependencyDeferring=false
bExecutionAfterReturn=false
bCanSuppressAccessViolation=false
bIgnoreCompileOnLoadErrorsOnBuildMachine=false
bAllowNativeComponentClassOverrides=true
bUseLegacyAnimBlueprintThreadSafetyChecks=true
bThreadSafetyStrictPrivateMemberChecks=false

[/Script/Engine.Blueprint]
bRecompileOnLoad=true

[/Script/Engine.LevelScriptBlueprint]
bRecompileOnLoad=true

[/Script/Engine.AnimBlueprint]
bRecompileOnLoad=true

[CustomStats]
+LD=Streaming fudge factor
+LD=FrameTime
+LD=Terrain Smooth Time
+LD=Terrain Render Time
+LD=Terrain Triangles
+LD=Static Mesh Tris
+LD=Skel Mesh Tris
+LD=Skel Verts CPU Skin
+LD=Skel Verts GPU Skin
+LD=30+ FPS
+LD=Total CPU rendering time
+LD=Total GPU rendering time
+LD=Occluded primitives
+LD=Projected shadows
+LD=Visible static mesh elements
+LD=Visible dynamic primitives
+LD=Texture Pool Size
+LD=Physical Memory Used
+LD=Virtual Memory Used
+LD=Audio Memory Used
+LD=Texture Memory Used
+LD=360 Texture Memory Used
+LD=Animation Memory
+LD=Navigation Memory
+LD=Vertex Lighting Memory
+LD=StaticMesh Vertex Memory
+LD=StaticMesh Index Memory
+LD=SkeletalMesh Vertex Memory
+LD=SkeletalMesh Index Memory
+MEMLEAN=Virtual Memory Used
+MEMLEAN=Audio Memory Used
+MEMLEAN=Animation Memory
+MEMLEAN=Vertex Lighting Memory
+MEMLEAN=StaticMesh Vertex Memory
+MEMLEAN=StaticMesh Index Memory
+MEMLEAN=SkeletalMesh Vertex Memory
+MEMLEAN=SkeletalMesh Index Memory
+MEMLEAN=VertexShader Memory
+MEMLEAN=PixelShader Memory
+MEMLEAN=Navigation Memory
+GameThread=Async Loading Time
+GameThread=Audio Update Time
+GameThread=FrameTime
+GameThread=HUD Time
+GameThread=Input Time
+GameThread=Kismet Time
+GameThread=Move Actor Time
+GameThread=RHI Game Tick
+GameThread=RedrawViewports
+GameThread=Script time
+GameThread=Tick Time
+GameThread=Update Components Time
+GameThread=World Tick Time
+GameThread=Async Work Wait
+GameThread=PerFrameCapture
+GameThread=DynamicLightEnvComp Tick
+Mobile=ES2 Draw Calls
+Mobile=ES2 Draw Calls (UP)
+Mobile=ES2 Triangles Drawn
+Mobile=ES2 Triangles Drawn (UP)
+Mobile=ES2 Program Count
+Mobile=ES2 Program Count (PP)
+Mobile=ES2 Program Changes
+Mobile=ES2 Uniform Updates (Bytes)
+Mobile=ES2 Base Texture Binds
+Mobile=ES2 Detail Texture Binds
+Mobile=ES2 Lightmap Texture Binds
+Mobile=ES2 Environment Texture Binds
+Mobile=ES2 Bump Offset Texture Binds
+Mobile=Frustum Culled primitives
+Mobile=Statically occluded primitives
+SplitScreen=Processed primitives
+SplitScreen=Mesh draw calls
+SplitScreen=Mesh Particles
+SplitScreen=Particle Draw Calls

[MemReportCommands]
+Cmd="Mem FromReport"
+Cmd="LogCountedInstances"
+Cmd="obj list -resourcesizesort"
+Cmd="rhi.DumpMemory"
+Cmd="rhi.DumpResourceMemory"
+Cmd="rhi.dumpresourcememory summary name=Lumen"
+Cmd="rhi.dumpresourcememory summary name=Nanite"
+Cmd="rhi.dumpresourcememory summary name=Shadow"
+Cmd="rhi.dumpresourcememory summary name=DistanceFields"
+Cmd="rhi.dumpresourcememory summary name=GPUScene"
+Cmd="rhi.dumpresourcememory summary name=SkinCache"
+Cmd="rhi.dumpresourcememory summary name=TangentsRHIBuffer"
+Cmd="rhi.dumpresourcememory summary name=IndexBuffer"
+Cmd="rhi.dumpresourcememory summary name=VertexBuffer"
+Cmd="rhi.dumpresourcememory summary name=TexCoordRHIBuffer"
+Cmd="rhi.dumpresourcememory summary name=None"
+Cmd="rhi.dumpresourcememory summary Type=TextureCube"
+Cmd="rhi.dumpresourcememory summary name=CaptureCube"
+Cmd="rhi.dumpresourcememory summary name=MorphDeltasVB"
+Cmd="rhi.dumpresourcememory summary name=VertexIndicesVB"
+Cmd="rhi.dumpresourcememory summary name=VirtualTexture"
+Cmd="rhi.dumpresourcememory summary name=VirtualPhysicalTexture"
+Cmd="rhi.dumpresourcememory summary name=VolumetricFog"
+Cmd="rhi.dumpresourcememory summary name=Hair"
+Cmd="rhi.dumpresourcememory summary name=Subsurface"
+Cmd="obj list class=SkeletalMesh -resourcesizesort"
+Cmd="obj list class=StaticMesh -resourcesizesort"
+Cmd="listtextures nonvt"
+Cmd="listtextures uncompressed"
+Cmd="LogOutStatLevels"
+Cmd="ListSpawnedActors"
+Cmd="wp.DumpDataLayers"
+Cmd="wp.DumpStreamingSources"

[MemReportFullCommands]
+Cmd="Mem FromReport"
+Cmd="LogCountedInstances"
+Cmd="obj list -resourcesizesort"
+Cmd="rhi.DumpMemory"
+Cmd="rhi.DumpResourceMemory"
+Cmd="rhi.dumpresourcememory summary name=Lumen"
+Cmd="rhi.dumpresourcememory summary name=Nanite"
+Cmd="rhi.dumpresourcememory summary name=Shadow"
+Cmd="rhi.dumpresourcememory summary name=DistanceFields"
+Cmd="rhi.dumpresourcememory summary name=GPUScene"
+Cmd="rhi.dumpresourcememory summary name=SkinCache"
+Cmd="rhi.dumpresourcememory summary name=TangentsRHIBuffer"
+Cmd="rhi.dumpresourcememory summary name=IndexBuffer"
+Cmd="rhi.dumpresourcememory summary name=VertexBuffer"
+Cmd="rhi.dumpresourcememory summary name=TexCoordRHIBuffer"
+Cmd="rhi.dumpresourcememory summary name=None"
+Cmd="rhi.dumpresourcememory summary Type=TextureCube"
+Cmd="rhi.dumpresourcememory summary name=CaptureCube"
+Cmd="rhi.dumpresourcememory summary name=MorphDeltasVB"
+Cmd="rhi.dumpresourcememory summary name=VertexIndicesVB"
+Cmd="rhi.dumpresourcememory summary name=VirtualTexture"
+Cmd="rhi.dumpresourcememory summary name=VirtualPhysicalTexture"
+Cmd="rhi.dumpresourcememory summary name=VolumetricFog"
+Cmd="rhi.dumpresourcememory summary name=Hair"
+Cmd="rhi.dumpresourcememory summary name=Subsurface"
+Cmd="obj list class=SkeletalMesh -resourcesizesort"
+Cmd="obj list class=StaticMesh -resourcesizesort"
+Cmd="listtextures nonvt"
+Cmd="listtextures uncompressed"
+Cmd="LogOutStatLevels"
+Cmd="ListSpawnedActors"
+Cmd="wp.DumpDataLayers"
+Cmd="wp.DumpStreamingSources"
+Cmd="DumpParticleMem"
+Cmd="ConfigMem"
+Cmd="r.DumpRenderTargetPoolMemory"
+Cmd="ListTextures"
+Cmd="ListParticleSystems -alphasort"
+Cmd="obj list class=SoundWave -alphasort"
+Cmd="obj list class=SkeletalMesh -alphasort"
+Cmd="obj list class=StaticMesh -alphasort"
+Cmd="obj list class=Level -alphasort"
+Cmd="obj list class=StaticMeshComponent -alphasort"

[MemoryPools]
FLightPrimitiveInteractionInitialBlockSize=512
PersistentAllocatorReserveSizeMB=512

[ProcessLimits]
VirtualMemoryLimitInKB=0

[SystemSettings]

con.DebugEarlyDefault = True
con.DebugEarlyCheat = True
con.DebugLateDefault = True
con.DebugLateCheat = True

!LogNamedEventFilters=ClearArray
+LogNamedEventFilters="Frame *"

[SystemSettingsEditor]

r.VSync=0

r.RHICmdBypass=0

[SystemSettingsSplitScreen2]

[OnlineSubsystem]
bHasVoiceEnabled=true
VoiceNotificationDelta=0.33
MaxLocalTalkers=1
MaxRemoteTalkers=16
PollingIntervalInMs=20
bUseBuildIdOverride=false
BuildIdOverride=0
!AdditionalModulesToLoad=Clear
+AdditionalModulesToLoad=HTTP
+AdditionalModulesToLoad=XMPP

[OnlineSubsystemSteam]
bEnabled=false
SteamDevAppId=0
GameServerQueryPort=27015
bInitServerOnClient=false
bRelaunchInSteam=false
GameVersion=*******
bVACEnabled=1
bAllowP2PPacketRelay=true
P2PConnectionTimeout=90

[/Script/OnlineSubsystemSteam.SteamNetDriver]
NetConnectionClassName="/Script/OnlineSubsystemSteam.SteamNetConnection"

[/Script/SteamSockets.SteamSocketsNetDriver]
NetConnectionClassName="/Script/SteamSockets.SteamSocketsNetConnection"
ConnectionTimeout=80.0
InitialConnectTimeout=120.0
NetServerMaxTickRate=30
MaxNetTickRate=120
KeepAliveTime=0.2
MaxClientRate=100000
MaxInternetClientRate=100000
RelevantTimeout=5.0
SpawnPrioritySeconds=1.0
ServerTravelPause=4.0

[OnlineSubsystemAmazon]
bEnabled=false

[OnlineSubsystemGoogle]
bEnabled=false

[OnlineSubsystemGoogle.OnlineIdentityGoogle]
LoginRedirectUrl="http://127.0.0.1"
+LoginDomains=".google.com"
RedirectPort=9001

[OnlineSubsystemFacebook]
bEnabled=false
APIVer="v15.0"

[OnlineSubsystemFacebook.OnlineIdentityFacebook]
LoginUrl="https://www.facebook.com/`ver/dialog/oauth"
LoginRedirectUrl="https://www.facebook.com/connect/login_success.html"
MeURL="https://graph.facebook.com/`ver/me?access_token=`token"
+LoginDomains=".facebook.com"
bUsePopup=true

[/Script/Engine.DataDrivenConsoleVariableSettings]
@CVarsArray=Name

[OnlineSubsystemFacebook.OnlineSharingFacebook]
PermissionsURL="https://graph.facebook.com/`ver/me/permissions?access_token=`token"

[OnlineSubsystemFacebook.OnlineFriendsFacebook]
FriendsUrl="https://graph.facebook.com/`ver/me/friends?fields=`fields&access_token=`token"

[OnlineSubsystemSamsung]
bEnabled=true

[OnlineSubsystemSamsung.OnlinePurchaseSamsung]
QueryReceiptsResumeFailDelaySeconds=2.0
CheckoutResumeFailDelaySeconds=2.0
bIncludeSamsungLocText=true

[OnlineSubsystemApple]
bEnabled=false

[/Script/OnlineSubsystemEOS.EOSSettings]
+AuthScopeFlags="BasicProfile"
+AuthScopeFlags="FriendsList"
+AuthScopeFlags="Presence"
bPreferPersistentAuth=false

[OnlineSubsystemNull]
bEnabled=true
bAutoLoginAtStartup=true
bRequireLoginCredentials=false
bAddUserNumToNullId=false
bForceStableNullId=false
bForceOfflineMode=false

Achievement_0_Id=null-ach-0
Achievement_0_bIsHidden=false
Achievement_0_Title="Achievement 0"
Achievement_0_LockedDesc="Achieve achievement 0"
Achievement_0_UnlockedDesc="Achievement 0 achieved"

Achievement_1_Id=null-ach-1
Achievement_1_bIsHidden=false
Achievement_1_Title="Achievement 1"
Achievement_1_LockedDesc="Achieve achievement 1"
Achievement_1_UnlockedDesc="Achievement 1 achieved"

Achievement_2_Id=null-ach-2
Achievement_2_bIsHidden=false
Achievement_2_Title="Achievement 2"
Achievement_2_LockedDesc="Achieve achievement 2"
Achievement_2_UnlockedDesc="Achievement 2 achieved"

Achievement_3_Id=null-ach-3
Achievement_3_bIsHidden=false
Achievement_3_Title="Achievement 3"
Achievement_3_LockedDesc="Achieve achievement 3"
Achievement_3_UnlockedDesc="Achievement 3 achieved"

Achievement_4_Id=null-ach-4
Achievement_4_bIsHidden=false
Achievement_4_Title="Achievement 4"
Achievement_4_LockedDesc="Achieve achievement 4"
Achievement_4_UnlockedDesc="Achievement 4 achieved"

Achievement_5_Id=null-ach-5
Achievement_5_bIsHidden=false
Achievement_5_Title="Achievement 5"
Achievement_5_LockedDesc="Achieve achievement 5"
Achievement_5_UnlockedDesc="Achievement 5 achieved"

Achievement_6_Id=null-ach-6
Achievement_6_bIsHidden=false
Achievement_6_Title="Achievement 6"
Achievement_6_LockedDesc="Achieve achievement 6"
Achievement_6_UnlockedDesc="Achievement 6 achieved"

Achievement_7_Id=null-ach-7
Achievement_7_bIsHidden=false
Achievement_7_Title="Achievement 7"
Achievement_7_LockedDesc="Achieve achievement 7"
Achievement_7_UnlockedDesc="Achievement 7 achieved"

Achievement_8_Id=null-ach-8
Achievement_8_bIsHidden=false
Achievement_8_Title="Achievement 8"
Achievement_8_LockedDesc="Achieve achievement 8"
Achievement_8_UnlockedDesc="Achievement 8 achieved"

Achievement_9_Id=null-ach-9
Achievement_9_bIsHidden=false
Achievement_9_Title="Achievement 9"
Achievement_9_LockedDesc="Achieve achievement 9"
Achievement_9_UnlockedDesc="Achievement 9 achieved"

[/Script/OnlineSubsystemUtils.OnlineBeacon]
BeaconConnectionInitialTimeout=5.0
BeaconConnectionTimeout=45.0

[/Script/OnlineSubsystemUtils.OnlineBeaconHost]
ListenPort=15000

[/Script/OnlineSubsystemUtils.PartyBeaconHost]
bLogoutOnSessionTimeout=true
SessionTimeoutSecs=10
TravelSessionTimeoutSecs=45

[/Script/OnlineSubsystemUtils.SpectatorBeaconHost]
bLogoutOnSessionTimeout=true
SessionTimeoutSecs=10
TravelSessionTimeoutSecs=45

[/Script/Lobby.LobbyBeaconClient]
BeaconConnectionInitialTimeout=90.0
BeaconConnectionTimeout=45.0

[OnlineServices.Lobbies]
+SchemaDescriptors=(Id="LobbyBase", CategoryIds=("Lobby", "LobbyMember"))
+SchemaCategoryDescriptors=(Id="Lobby", ServiceDescriptorId="Lobby")
+SchemaCategoryDescriptors=(Id="LobbyMember", ServiceDescriptorId="LobbyMember")
+SchemaCategoryAttributeDescriptors=(SchemaId="LobbyBase", CategoryId="Lobby", AttributeIds=("SchemaCompatibilityId"))
+SchemaCategoryAttributeDescriptors=(SchemaId="LobbyBase", CategoryId="LobbyMember")
+SchemaAttributeDescriptors=(Id="SchemaCompatibilityId", Type="Int64", Flags=("Public", "SchemaCompatibilityId"))

[StaticMeshLODSettings]
LevelArchitecture=(NumLODs=4,MaxNumStreamedLODs=0,bSupportLODStreaming=0,LightMapResolution=32,LODPercentTriangles=50,PixelError=12,SilhouetteImportance=4,Name=LOCTEXT("LevelArchitectureLOD","Level Architecture"))
SmallProp=(NumLODs=4,MaxNumStreamedLODs=0,bSupportLODStreaming=0,LODPercentTriangles=50,PixelError=10,Name=LOCTEXT("SmallPropLOD","Small Prop"))
LargeProp=(NumLODs=4,MaxNumStreamedLODs=0,bSupportLODStreaming=0,LODPercentTriangles=50,PixelError=10,Name=LOCTEXT("LargePropLOD","Large Prop"))
Deco=(NumLODs=4,MaxNumStreamedLODs=0,bSupportLODStreaming=0,LODPercentTriangles=50,PixelError=10,Name=LOCTEXT("DecoLOD","Deco"))
Vista=(NumLODs=1,MaxNumStreamedLODs=0,bSupportLODStreaming=0,Name=LOCTEXT("VistaLOD","Vista"))
Foliage=(NumLODs=1,MaxNumStreamedLODs=0,bSupportLODStreaming=0,Name=LOCTEXT("FoliageLOD","Foliage"))
HighDetail=(NumLODs=6,MaxNumStreamedLODs=0,bSupportLODStreaming=0,LODPercentTriangles=50,PixelError=6,Name=LOCTEXT("HighDetailLOD","High Detail"))

[TextureTracking]

[RuntimeAssetCache]
+BucketConfigs=(Name="DefaultBucket", Size=10000000)
PathToRAC=RuntimeAssetCache

[Zen]
AutoLaunch=true

[Zen.AutoLaunch]
DataPath=%ENGINEVERSIONAGNOSTICINSTALLEDUSERDIR%Zen/Data
LimitProcessLifetime=true
AllowRemoteNetworkService=false
LocalDataCachePathEnvOverride=UE-LocalDataCachePath
LocalDataCachePathCommandLineOverride=LocalDataCachePath
LocalDataCachePathEditorOverrideSetting=LocalDerivedDataCache
ExtraArgs=--http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet

[Zen.ConnectExisting]
HostName=localhost
Port=8558

[DerivedDataBackendGraph]
Root=(Type=Hierarchical, Inner=Pak, Inner=EnginePak, Inner=ZenLocal, Inner=Local, Inner=ZenShared, Inner=Shared, Inner=Cloud)
Local=(Type=FileSystem, DeleteOnly=true, UnusedFileAge=8, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache, CommandLineOverride=LocalDataCachePath)
Shared=(Type=FileSystem, UnusedFileAge=10, FoldersToClean=10, ConsiderSlowAt=70, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)
Cloud=(Type=Cloud, ServerID=Cloud, EnvHostOverride=UE-CloudDataCacheHost, CommandLineHostOverride=CloudDataCacheHost, CommandLineOAuthSecretOverride="CloudDataCacheOAuthSecret", OAuthAccessTokenEnvOverride="UE-CloudDataCacheAccessToken", EnvHttpVersionOverride="UE-CloudDataCacheHttpVersion", CommandLineHttpVersionOverride="CloudDataCacheHttpVersion", EnvOAuthProviderIdentifierOverride="UE-CloudDataCacheOAuthProviderIdentifier", CommandLineOAuthProviderIdentifierOverride="CloudDataCacheOAuthProviderIdentifier")
Pak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
EnginePak=(Type=ReadPak, Filename="%ENGINEDIR%DerivedDataCache/DDC.ddp")
ZenLocal=(Type=Zen, ServerID=Local)
ZenShared=(Type=Zen, ServerID=Shared)

[InstalledDerivedDataBackendGraph]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=Pak, Inner=CompressedPak, Inner=EnginePak, Inner=EnterprisePak, Inner=ZenLocal, Inner=Local, Inner=ZenShared, Inner=Shared, Inner=Cloud)
Local=(Type=FileSystem, DeleteOnly=true, UnusedFileAge=8, Path="%ENGINEVERSIONAGNOSTICUSERDIR%DerivedDataCache", EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache)
Shared=(Type=FileSystem, UnusedFileAge=10, FoldersToClean=10, ConsiderSlowAt=70, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)
Cloud=(Type=Cloud, ServerID=Cloud, EnvHostOverride=UE-CloudDataCacheHost, CommandLineHostOverride=CloudDataCacheHost, CommandLineOAuthSecretOverride="CloudDataCacheOAuthSecret", OAuthAccessTokenEnvOverride="UE-CloudDataCacheAccessToken", EnvHttpVersionOverride="UE-CloudDataCacheHttpVersion", CommandLineHttpVersionOverride="CloudDataCacheHttpVersion", EnvOAuthProviderIdentifierOverride="UE-CloudDataCacheOAuthProviderIdentifier", CommandLineOAuthProviderIdentifierOverride="CloudDataCacheOAuthProviderIdentifier")
Pak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
CompressedPak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/Compressed.ddp", Compressed=true)
EnginePak=(Type=ReadPak, Filename=../../../Engine/DerivedDataCache/Compressed.ddp, Compressed=true)
EnterprisePak=(Type=ReadPak, Filename=../../../Enterprise/DerivedDataCache/Compressed.ddp, Compressed=true)
ZenLocal=(Type=Zen, ServerID=Local)
ZenShared=(Type=Zen, ServerID=Shared)

[NoZenLocalFallback]
Root=(Type=Hierarchical, Inner=Pak, Inner=EnginePak, Inner=Local, Inner=ZenShared, Inner=Shared, Inner=Cloud)
Local=(Type=FileSystem, UnusedFileAge=34, PromptIfMissing=true, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache, CommandLineOverride=LocalDataCachePath)
Shared=(Type=FileSystem, UnusedFileAge=10, FoldersToClean=10, ConsiderSlowAt=70, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)
Cloud=(Type=Cloud, ServerID=Cloud, EnvHostOverride=UE-CloudDataCacheHost, CommandLineHostOverride=CloudDataCacheHost, CommandLineOAuthSecretOverride="CloudDataCacheOAuthSecret", OAuthAccessTokenEnvOverride="UE-CloudDataCacheAccessToken", EnvHttpVersionOverride="UE-CloudDataCacheHttpVersion", CommandLineHttpVersionOverride="CloudDataCacheHttpVersion", EnvOAuthProviderIdentifierOverride="UE-CloudDataCacheOAuthProviderIdentifier", CommandLineOAuthProviderIdentifierOverride="CloudDataCacheOAuthProviderIdentifier")
Pak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
EnginePak=(Type=ReadPak, Filename="%ENGINEDIR%DerivedDataCache/DDC.ddp")
ZenShared=(Type=Zen, ServerID=Shared)

[InstalledNoZenLocalFallback]
Root=(Type=Hierarchical, Inner=Pak, Inner=CompressedPak, Inner=EnginePak, Inner=EnterprisePak, Inner=Local, Inner=ZenShared, Inner=Shared, Inner=Cloud)
Local=(Type=FileSystem, UnusedFileAge=34, Path="%ENGINEVERSIONAGNOSTICUSERDIR%DerivedDataCache", EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache)
Shared=(Type=FileSystem, UnusedFileAge=10, FoldersToClean=10, ConsiderSlowAt=70, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)
Cloud=(Type=Cloud, ServerID=Cloud, EnvHostOverride=UE-CloudDataCacheHost, CommandLineHostOverride=CloudDataCacheHost, CommandLineOAuthSecretOverride="CloudDataCacheOAuthSecret", OAuthAccessTokenEnvOverride="UE-CloudDataCacheAccessToken", EnvHttpVersionOverride="UE-CloudDataCacheHttpVersion", CommandLineHttpVersionOverride="CloudDataCacheHttpVersion", EnvOAuthProviderIdentifierOverride="UE-CloudDataCacheOAuthProviderIdentifier", CommandLineOAuthProviderIdentifierOverride="CloudDataCacheOAuthProviderIdentifier")
Pak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
CompressedPak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/Compressed.ddp", Compressed=true)
EnginePak=(Type=ReadPak, Filename=../../../Engine/DerivedDataCache/Compressed.ddp, Compressed=true)
EnterprisePak=(Type=ReadPak, Filename=../../../Enterprise/DerivedDataCache/Compressed.ddp, Compressed=true)
ZenShared=(Type=Zen, ServerID=Shared)

[ZenOnlyDDC]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Zen)
Zen=(Type=Zen, ServerID=Local)

[ZenDDC]
Root=(Type=Hierarchical, Inner=Pak, Inner=EnginePak, Inner=ZenLocal, Inner=Local, Inner=ZenShared, Inner=Shared, Inner=Cloud)
Local=(Type=FileSystem, DeleteOnly=true, UnusedFileAge=8, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache, CommandLineOverride=LocalDataCachePath)
ZenLocal=(Type=Zen, ServerID=Local)
ZenShared=(Type=Zen, ServerID=Shared)
Shared=(Type=FileSystem, UnusedFileAge=10, FoldersToClean=10, ConsiderSlowAt=70, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)
Cloud=(Type=Cloud, ServerID=Cloud, EnvHostOverride=UE-CloudDataCacheHost, CommandLineHostOverride=CloudDataCacheHost, CommandLineOAuthSecretOverride="CloudDataCacheOAuthSecret", OAuthAccessTokenEnvOverride="UE-CloudDataCacheAccessToken", EnvHttpVersionOverride="UE-CloudDataCacheHttpVersion", CommandLineHttpVersionOverride="CloudDataCacheHttpVersion", EnvOAuthProviderIdentifierOverride="UE-CloudDataCacheOAuthProviderIdentifier", CommandLineOAuthProviderIdentifierOverride="CloudDataCacheOAuthProviderIdentifier")
Pak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
EnginePak=(Type=ReadPak, Filename="%ENGINEDIR%DerivedDataCache/DDC.ddp")

[ZenColdDDC]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=ZenLocal)
ZenLocal=(Type=Zen, ServerID=Local, Sandbox=Cold, Flush=true)

[ZenWarmDDC]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=ZenLocal)
ZenLocal=(Type=Zen, ServerID=Local, Sandbox=Cold)

[DerivedDataBackendGraph_Fill_Seattle]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=Pak, Inner=EnginePak, Inner=ZenLocal, Inner=Local, Inner=Seattle)
Local=(Type=FileSystem, DeleteOnly=true, UnusedFileAge=8, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache, CommandLineOverride=LocalDataCachePath)
Seattle=(Type=FileSystem, UnusedFileAge=23, FoldersToClean=10, Path=?EpicSeaDDC, EnvPathOverride=UE-SharedDataCachePath_Seattle)
Pak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
EnginePak=(Type=ReadPak, Filename="%ENGINEDIR%DerivedDataCache/DDC.ddp")
ZenLocal=(Type=Zen, ServerID=Local)

[NoShared]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=Pak, Inner=ZenLocal, Inner=Local)
Local=(Type=FileSystem, DeleteOnly=true, UnusedFileAge=8, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache, CommandLineOverride=LocalDataCachePath)
Pak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
ZenLocal=(Type=Zen, ServerID=Local)

[Cold]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=ZenLocal)
ZenLocal=(Type=Zen, ServerID=Local, Sandbox=Cold, Flush=true)

[Warm]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=ZenLocal)
ZenLocal=(Type=Zen, ServerID=Local, Sandbox=Cold)

[CreatePak]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=PakWrite, Inner=PakRead, Inner=ZenLocal, Inner=Local, Inner=ZenShared, Inner=Shared, Inner=Cloud)
Local=(Type=FileSystem, DeleteOnly=true, UnusedFileAge=8, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache, CommandLineOverride=LocalDataCachePath)
Shared=(Type=FileSystem, DeleteUnused=false, ConsiderSlowAt=70, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)
Cloud=(Type=Cloud, ServerID=Cloud, EnvHostOverride=UE-CloudDataCacheHost, CommandLineHostOverride=CloudDataCacheHost, CommandLineOAuthSecretOverride="CloudDataCacheOAuthSecret", OAuthAccessTokenEnvOverride="UE-CloudDataCacheAccessToken", EnvHttpVersionOverride="UE-CloudDataCacheHttpVersion", CommandLineHttpVersionOverride="CloudDataCacheHttpVersion", EnvOAuthProviderIdentifierOverride="UE-CloudDataCacheOAuthProviderIdentifier", CommandLineOAuthProviderIdentifierOverride="CloudDataCacheOAuthProviderIdentifier")
PakRead=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
PakWrite=(Type=WritePak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
ZenLocal=(Type=Zen, ServerID=Local)
ZenShared=(Type=Zen, ServerID=Shared)

[CreateInstalledProjectPak]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=EnginePak, Inner=PakWrite, Inner=PakRead, Inner=ZenLocal, Inner=Local, Inner=ZenShared, Inner=Shared, Inner=Cloud)
Local=(Type=FileSystem, DeleteOnly=true, UnusedFileAge=8, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache, CommandLineOverride=LocalDataCachePath)
Shared=(Type=FileSystem, DeleteUnused=false, ConsiderSlowAt=70, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)
Cloud=(Type=Cloud, ServerID=Cloud, EnvHostOverride=UE-CloudDataCacheHost, CommandLineHostOverride=CloudDataCacheHost, CommandLineOAuthSecretOverride="CloudDataCacheOAuthSecret", OAuthAccessTokenEnvOverride="UE-CloudDataCacheAccessToken", EnvHttpVersionOverride="UE-CloudDataCacheHttpVersion", CommandLineHttpVersionOverride="CloudDataCacheHttpVersion", EnvOAuthProviderIdentifierOverride="UE-CloudDataCacheOAuthProviderIdentifier", CommandLineOAuthProviderIdentifierOverride="CloudDataCacheOAuthProviderIdentifier")
EnginePak=(Type=ReadPak, Filename=../../../Engine/DerivedDataCache/Compressed.ddp, Compressed=true)
PakRead=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/Compressed.ddp", Compressed=true)
PakWrite=(Type=WritePak, Filename="%GAMEDIR%DerivedDataCache/Compressed.ddp", Compressed=true)
ZenLocal=(Type=Zen, ServerID=Local)
ZenShared=(Type=Zen, ServerID=Shared)

[CreateInstalledEnginePak]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=PakWrite, Inner=ZenLocal, Inner=Local, Inner=ZenShared, Inner=Shared, Inner=Cloud)
Local=(Type=FileSystem, DeleteOnly=true, UnusedFileAge=8, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache, CommandLineOverride=LocalDataCachePath)
Shared=(Type=FileSystem, DeleteUnused=false, ConsiderSlowAt=70, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)
Cloud=(Type=Cloud, ServerID=Cloud, EnvHostOverride=UE-CloudDataCacheHost, CommandLineHostOverride=CloudDataCacheHost, CommandLineOAuthSecretOverride="CloudDataCacheOAuthSecret", OAuthAccessTokenEnvOverride="UE-CloudDataCacheAccessToken", EnvHttpVersionOverride="UE-CloudDataCacheHttpVersion", CommandLineHttpVersionOverride="CloudDataCacheHttpVersion", EnvOAuthProviderIdentifierOverride="UE-CloudDataCacheOAuthProviderIdentifier", CommandLineOAuthProviderIdentifierOverride="CloudDataCacheOAuthProviderIdentifier")
PakWrite=(Type=WritePak, Filename="%ENGINEDIR%DerivedDataCache/Compressed.ddp", Compressed=true)
ZenLocal=(Type=Zen, ServerID=Local)
ZenShared=(Type=Zen, ServerID=Shared)

[CreateInstalledEnterprisePak]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=PakWrite, Inner=ZenLocal, Inner=Local, Inner=ZenShared, Inner=Shared, Inner=Cloud)
Local=(Type=FileSystem, DeleteOnly=true, UnusedFileAge=8, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache, CommandLineOverride=LocalDataCachePath)
Shared=(Type=FileSystem, DeleteUnused=false, ConsiderSlowAt=70, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)
Cloud=(Type=Cloud, ServerID=Cloud, EnvHostOverride=UE-CloudDataCacheHost, CommandLineHostOverride=CloudDataCacheHost, CommandLineOAuthSecretOverride="CloudDataCacheOAuthSecret", OAuthAccessTokenEnvOverride="UE-CloudDataCacheAccessToken", EnvHttpVersionOverride="UE-CloudDataCacheHttpVersion", CommandLineHttpVersionOverride="CloudDataCacheHttpVersion", EnvOAuthProviderIdentifierOverride="UE-CloudDataCacheOAuthProviderIdentifier", CommandLineOAuthProviderIdentifierOverride="CloudDataCacheOAuthProviderIdentifier")
PakWrite=(Type=WritePak, Filename=../../../Enterprise/DerivedDataCache/Compressed.ddp, Compressed=true)
ZenLocal=(Type=Zen, ServerID=Local)
ZenShared=(Type=Zen, ServerID=Shared)

[CreateProjectCache]
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=ZenLocal, Inner=Local, Inner=Project, Inner=ZenShared, Inner=Shared, Inner=Cloud)
Local=(Type=FileSystem, DeleteOnly=true, UnusedFileAge=8, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache, CommandLineOverride=LocalDataCachePath)
Project=(Type=FileSystem, Flush=true, UnusedFileAge=34, Path=%GAMEDIR%ProjectDerivedData)
Shared=(Type=FileSystem, DeleteUnused=false, ConsiderSlowAt=70, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)
Cloud=(Type=Cloud, ServerID=Cloud, EnvHostOverride=UE-CloudDataCacheHost, CommandLineHostOverride=CloudDataCacheHost, CommandLineOAuthSecretOverride="CloudDataCacheOAuthSecret", OAuthAccessTokenEnvOverride="UE-CloudDataCacheAccessToken")
ZenLocal=(Type=Zen, ServerID=Local)
ZenShared=(Type=Zen, ServerID=Shared)

[VirtualTextureChunkDDCCache]
UnusedFileAge=34
MaxFileChecksPerSec=-1 ;no limit
Path=%GAMEDIR%DerivedDataCache/VT

[DDCCleanup]
TimeToWaitAfterInit=120
MaxFileChecksPerSec=1000

[/Script/Engine.LocalPlayer]
AspectRatioAxisConstraint=AspectRatio_MaintainYFOV

[ContentComparisonReferenceTypes]
+Class=AnimSet
+Class=SkeletalMesh
+Class=SoundCue
+Class=StaticMesh
+Class=ParticleSystem
+Class=Texture2D

[PythonScriptPlugin]
+DisablePythonForCommandlet=CookShaders

[AssetRegistry]
+CookedTagsBlacklist=(Class=/Script/Engine.Blueprint,Tag=FiB)
+CookedTagsBlacklist=(Class=/Script/Engine.Blueprint,Tag=FiBData)
+CookedTagsBlacklist=(Class=*,Tag=AssetImportData)

bUseAssetRegistryTagsWhitelistInsteadOfBlacklist=false
+CookedTagsWhitelist=(Class=/Script/Engine.Blueprint,Tag=ParentClass)
+CookedTagsWhitelist=(Class=/Script/Engine.Blueprint,Tag=GeneratedClass)
+CookedTagsWhitelist=(Class=/Script/Engine.Blueprint,Tag=GameplayCueName)
+CookedTagsWhitelist=(Class=*,Tag=AssetBundleData)
+CookedTagsWhitelist=(Class=*,Tag=PrimaryAssetType)
+CookedTagsWhitelist=(Class=*,Tag=PrimaryAssetName)
+CookedTagsWhitelist=(Class=/Script/Engine.World,Tag=Tests)
+CookedTagsWhitelist=(Class=/Script/Engine.World,Tag=TestNames)

+CookedTagsAsFName=PrimaryAssetType
+CookedTagsAsFName=PrimaryAssetName
+CookedTagsAsPathName=GeneratedClass
+CookedTagsAsPathName=ParentClass

bSerializeAssetRegistry=true
bSerializeDependencies=false
bSerializeSearchableNameDependencies=false
bSerializeManageDependencies=false
bSerializePackageData=false
bFilterAssetDataWithNoTags=false
bFilterDependenciesWithNoTags=false
bFilterSearchableNames=true

EditorGameScansAR=true

GatherOnGameThreadOnly=false

+CommandletsUsingAR=DataValidation
+CommandletsUsingAR=GatherText
+CommandletsUsingAR=GenerateNaniteDisplacedMesh
+CommandletsUsingAR=WorldPartitionBuilder
+CommandletsUsingAR=CookShaders

AssetRegistryDiscoveryCache=Never

[AutomationTesting]
ImportTestPath=Content/EditorAutomation/
ImportTestPackagePath=/Engine/Content/EditorAutomation
bForceSmokeTests=false

[AutomationTesting.FbxImport]
FbxImportTestPath=Content/FbxEditorAutomation/
FbxImportEditorTestPath=../../../Engine/Content/FbxEditorAutomation/
FbxImportTestPackagePath=/Engine/FbxEditorAutomationOut

[AutomationTesting.Blueprint]
TestAllBlueprints=false
+InstanceTestMaps=../../../Engine/Content/Maps/Automation/BlueprintInstanceTest.umap
+ReparentTest.ChildrenPackagePaths=/Game/ReparentingTestAssets/Children
+ReparentTest.ParentsPackagePaths=/Game/ReparentingTestAssets/Parents

[/Script/Engine.AutomationTestSettings]
+EditorTestModules=StaticMeshEditor
+EditorTestModules=LandscapeEditor
+EditorTestModules=GameProjectGeneration
+EditorTestModules=Cascade
+TestLevelFolders=TestMaps
MaterialEditorPromotionTest=(DefaultMaterialAsset=(FilePath="../../Content/EditorMeshes/ColorCalibrator/M_ColorGrid.uasset"),DefaultDiffuseTexture=(FilePath="../../Content/EngineMaterials/DefaultDiffuse.uasset"),DefaultNormalTexture=(FilePath="../../Content/EngineMaterials/DefaultNormal.uasset"))

[AutomationTesting.StaticMeshEditorTest]
+EditorViewButtons=Wireframe
+EditorViewButtons=Verts
+EditorViewButtons=Grid
+EditorViewButtons=Bounds
+EditorViewButtons=Collision
+EditorViewButtons=Show Pivot
+EditorViewButtons=Normals
+EditorViewButtons=Tangents
+EditorViewButtons=Binormals
+EditorViewButtons=UV
EditorViewButtonsObject=EditorCylinder
DefaultInteractiveFramerate=5
DefaultInteractiveFramerateWaitTime=600
DefaultInteractiveFramerateDuration=5


[/Script/NavigationSystem.NavigationSystemV1]
bAutoCreateNavigationData=true
bAddPlayersToGenerationSeeds=true

[/Script/NavigationSystem.NavigationData]
RuntimeGeneration=Static

[/Script/NavigationSystem.RecastNavMesh]
TileSetUpdateInterval=1.0
MaxTileGridWidth=256
MaxTileGridHeight=256
DefaultDrawDistance=5000.0
TileSizeUU=1000.f
CellSize=19.f
CellHeight=10.f
NavMeshResolutionParams[0]=(CellSize=38.000000,CellHeight=10.f,AgentMaxStepHeight=35.f)
NavMeshResolutionParams[1]=(CellSize=19.000000,CellHeight=10.f,AgentMaxStepHeight=35.f)
NavMeshResolutionParams[2]=(CellSize=19.000000,CellHeight=10.f,AgentMaxStepHeight=35.f)
AgentRadius=34.f
AgentHeight=144.f
AgentMaxHeight=160.f
AgentMaxStepHeight=35.f
AgentMaxSlope=44.f
MinRegionArea=0.f
MergeRegionSize=400.f
bUseBetterOffsetsFromCorners=true

[/Script/NavigationSystem.NavArea_Null]
DrawColor=(R=38,G=38,B=38,A=64)

[/Script/NavigationSystem.NavArea_Default]
DrawColor=(R=140,G=255,B=0,A=164)

[Trace.ChannelPresets]
Rendering=gpu,cpu,frame,log,bookmark
Loading=cpu,frame,log,bookmark,screenshot,region,loadtime,assetloadtime,file
TaskGraph=cpu,gpu,frame,log,bookmark,screenshot,region,task
ContextSwitching=cpu,gpu,frame,log,bookmark,screenshot,region,contextswitch

[RemoteConfiguration]
Enabled=false
ConfigPathPrefix=\\epicgames.net\root\Home
ConfigPathSuffix=UECloud
Timeout=1.0f
+IniToLoad=EditorPerProjectUserSettings
+IniToLoad=EditorKeyBindings

[Engine.ErrorHandling]
bPromptForRemoteDebugging=false
bPromptForRemoteDebugOnEnsure=false

[Niagara]
EnableNiagara=false

[/Script/Engine.Actor]
DefaultUpdateOverlapsMethodDuringLevelStreaming=OnlyUpdateMovable

[/Script/Engine.TriggerVolume]
DefaultUpdateOverlapsMethodDuringLevelStreaming=AlwaysUpdate

[/Script/Engine.CollisionProfile]
+Profiles=(Name="NoCollision",CollisionEnabled=NoCollision,ObjectTypeName="WorldStatic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore)),HelpMessage="No collision",bCanModify=False)
+Profiles=(Name="BlockAll",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldStatic",CustomResponses=,HelpMessage="WorldStatic object that blocks all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
+Profiles=(Name="OverlapAll",CollisionEnabled=QueryOnly,ObjectTypeName="WorldStatic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Overlap),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldStatic object that overlaps all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
+Profiles=(Name="BlockAllDynamic",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldDynamic",CustomResponses=,HelpMessage="WorldDynamic object that blocks all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
+Profiles=(Name="OverlapAllDynamic",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Overlap),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldDynamic object that overlaps all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
+Profiles=(Name="IgnoreOnlyPawn",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that ignores Pawn and Vehicle. All other channels will be set to default.",bCanModify=False)
+Profiles=(Name="OverlapOnlyPawn",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Pawn",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that overlaps Pawn, Camera, and Vehicle. All other channels will be set to default. ",bCanModify=False)
+Profiles=(Name="Pawn",CollisionEnabled=QueryAndPhysics,ObjectTypeName="Pawn",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Pawn object. Can be used for capsule of any playerable character or AI. ",bCanModify=False)
+Profiles=(Name="Spectator",CollisionEnabled=QueryOnly,ObjectTypeName="Pawn",CustomResponses=((Channel="WorldStatic",Response=ECR_Block),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore)),HelpMessage="Pawn object that ignores all other actors except WorldStatic.",bCanModify=False)
+Profiles=(Name="CharacterMesh",CollisionEnabled=QueryOnly,ObjectTypeName="Pawn",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Pawn object that is used for Character Mesh. All other channels will be set to default.",bCanModify=False)
+Profiles=(Name="PhysicsActor",CollisionEnabled=QueryAndPhysics,ObjectTypeName="PhysicsBody",CustomResponses=,HelpMessage="Simulating actors",bCanModify=False)
+Profiles=(Name="Destructible",CollisionEnabled=QueryAndPhysics,ObjectTypeName="Destructible",CustomResponses=,HelpMessage="Destructible actors",bCanModify=False)
+Profiles=(Name="InvisibleWall",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldStatic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="WorldStatic object that is invisible.",bCanModify=False)
+Profiles=(Name="InvisibleWallDynamic",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that is invisible.",bCanModify=False)
+Profiles=(Name="Trigger",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldDynamic object that is used for trigger. All other channels will be set to default.",bCanModify=False)
+Profiles=(Name="Ragdoll",CollisionEnabled=QueryAndPhysics,ObjectTypeName="PhysicsBody",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Simulating Skeletal Mesh Component. All other channels will be set to default.",bCanModify=False)
+Profiles=(Name="Vehicle",CollisionEnabled=QueryAndPhysics,ObjectTypeName="Vehicle",CustomResponses=,HelpMessage="Vehicle object that blocks Vehicle, WorldStatic, and WorldDynamic. All other channels will be set to default.",bCanModify=False)
+Profiles=(Name="UI",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Block),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldStatic object that overlaps all actors by default. All new custom channels will use its own default response. ",bCanModify=False)

+OldProfiles=(Name="BlockingVolume",CollisionEnabled=QueryAndPhysics,ObjectTypeName=WorldStatic,CustomResponses=((Channel=Visibility, Response=ECR_Ignore)))
+OldProfiles=(Name="InterpActor",CollisionEnabled=QueryOnly,ObjectTypeName=WorldStatic,CustomResponses=((Channel=Pawn, Response=ECR_Ignore)))
+OldProfiles=(Name="StaticMeshComponent",CollisionEnabled=QueryAndPhysics,ObjectTypeName=WorldStatic)
+OldProfiles=(Name="SkeletalMeshActor",CollisionEnabled=QueryAndPhysics,ObjectTypeName=PhysicsBody,CustomResponses=((Channel=Visibility, Response=ECR_Block)))
+OldProfiles=(Name="InvisibleActor", CollisionEnabled=QueryAndPhysics, ObjectTypeName=WorldDynamic, CustomResponses=((Channel=Visibility, Response=ECR_Ignore)))

+ProfileRedirects=(OldName="BlockingVolume",NewName="InvisibleWall")
+ProfileRedirects=(OldName="InterpActor",NewName="IgnoreOnlyPawn")
+ProfileRedirects=(OldName="StaticMeshComponent",NewName="BlockAllDynamic")
+ProfileRedirects=(OldName="SkeletalMeshActor",NewName="PhysicsActor")
+ProfileRedirects=(OldName="InvisibleActor",NewName="InvisibleWallDynamic")

+CollisionChannelRedirects=(OldName="Static",NewName="WorldStatic")
+CollisionChannelRedirects=(OldName="Dynamic",NewName="WorldDynamic")
+CollisionChannelRedirects=(OldName="VehicleMovement",NewName="Vehicle")
+CollisionChannelRedirects=(OldName="PawnMovement",NewName="Pawn")

[Engine.BufferVisualizationMaterials]
BaseColor=(Material="/Engine/BufferVisualization/BaseColor.BaseColor", Name=LOCTEXT("BaseColorMat", "Base Color"))
CustomDepth=(Material="/Engine/BufferVisualization/CustomDepth.CustomDepth", Name=LOCTEXT("BaseCustomDepthMat", "Custom Depth"))
CustomStencil=(Material="/Engine/BufferVisualization/CustomStencil.CustomStencil", Name=LOCTEXT("BaseCustomStencilMat", "Custom Stencil"))
FinalImage=(Material="/Engine/BufferVisualization/FinalImage.FinalImage", Name=LOCTEXT("BaseFinalImageMat", "Final Image"), ApplyAutoExposure=true)
ShadingModel=(Material="/Engine/BufferVisualization/LightingModel.LightingModel", Name=LOCTEXT("BaseShadingModelMat", "Shading Model"))
MaterialAO=(Material="/Engine/BufferVisualization/MaterialAO.MaterialAO", Name=LOCTEXT("BaseMaterialAOMat", "Material Ambient Occlusion"))
Metallic=(Material="/Engine/BufferVisualization/Metallic.Metallic", Name=LOCTEXT("BaseMetallicMat", "Metallic"))
Opacity=(Material="/Engine/BufferVisualization/Opacity.Opacity", Name=LOCTEXT("BaseOpacityMat", "Opacity"))
Roughness=(Material="/Engine/BufferVisualization/Roughness.Roughness", Name=LOCTEXT("BaseRoughnessMat", "Roughness"))
Anisotropy=(Material="/Engine/BufferVisualization/Anisotropy.Anisotropy", Name=LOCTEXT("BaseAnisotropyMat", "Anisotropy"))
SceneColor=(Material="/Engine/BufferVisualization/SceneColor.SceneColor", Name=LOCTEXT("BaseSceneColorMat", "Scene Color"), ApplyAutoExposure=true)
SceneDepth=(Material="/Engine/BufferVisualization/SceneDepth.SceneDepth", Name=LOCTEXT("BaseSceneDepthMat", "Scene Depth"))
SeparateTranslucencyRGB=(Material="/Engine/BufferVisualization/SeparateTranslucencyRGB.SeparateTranslucencyRGB", Name=LOCTEXT("BaseSeparateTranslucencyRGBMat", "Separate Translucency RGB"), ApplyAutoExposure=true)
SeparateTranslucencyA=(Material="/Engine/BufferVisualization/SeparateTranslucencyA.SeparateTranslucencyA", Name=LOCTEXT("BaseSeparateTranslucencyAMat", "Separate Translucency Alpha"))
Specular=(Material="/Engine/BufferVisualization/Specular.Specular", Name=LOCTEXT("BaseSpecularMat", "Specular"))
SubsurfaceColor=(Material="/Engine/BufferVisualization/SubsurfaceColor.SubsurfaceColor", Name=LOCTEXT("BaseSubsurfaceColorMat", "Subsurface Color"))
WorldNormal=(Material="/Engine/BufferVisualization/WorldNormal.WorldNormal", Name=LOCTEXT("BaseWorldNormalMat", "World Normal"))
WorldTangent=(Material="/Engine/BufferVisualization/WorldTangent.WorldTangent", Name=LOCTEXT("BaseWorldTangentMat", "World Tangent"))
AmbientOcclusion=(Material="/Engine/BufferVisualization/AmbientOcclusion.AmbientOcclusion", Name=LOCTEXT("BaseAmbientOcclusionMat", "Ambient Occlusion"))
CustomDepthWorldUnits=(Material="/Engine/BufferVisualization/CustomDepthWorldUnits.CustomDepthWorldUnits", Name=LOCTEXT("BaseCustomDepthWorldUnitsMat", "Custom Depth World Units"))
SceneDepthWorldUnits=(Material="/Engine/BufferVisualization/SceneDepthWorldUnits.SceneDepthWorldUnits", Name=LOCTEXT("BaseSceneDepthWorldUnitsMat", "Scene Depth World Units"))
Velocity=(Material="/Engine/BufferVisualization/Velocity.Velocity", Name=LOCTEXT("Velocity", "Velocity"))
PreTonemapHDRColor=(Material="/Engine/BufferVisualization/PreTonemapHDRColor.PreTonemapHDRColor", Name=LOCTEXT("PreTonemapHDRColor", "Pre Tonemap HDR Color"), ApplyAutoExposure=true)
PostTonemapHDRColor=(Material="/Engine/BufferVisualization/PostTonemapHDRColor.PostTonemapHDRColor", Name=LOCTEXT("PostTonemapHDRColor", "Post Tonemap HDR Color"), ApplyAutoExposure=true)

[DeviceProfileManager]
DeviceProfileSelectionModule="ExampleDeviceProfileSelector"

[SlateRenderer]
TextureAtlasSize=1024
GrayscaleFontAtlasSize=1024
ColorFontAtlasSize=512
SdfFontAtlasSize=1024
NumPreallocatedVertices=50000

[MobileSlateUI]
bTouchFallbackToMouse=true

[Pak]
bDoUseOodleDespiteNoPluginCompression = true
+ExtensionsToNotUsePluginCompression=uplugin
+ExtensionsToNotUsePluginCompression=upluginmanifest
+ExtensionsToNotUsePluginCompression=uproject
+ExtensionsToNotUsePluginCompression=ini
+ExtensionsToNotUsePluginCompression=icu
+ExtensionsToNotUsePluginCompression=res
+ExtensionsToNotUsePluginCompression=pem
+FileNamesToNotUsePluginCompression=AssetRegistry.bin
+DirectoryIndexKeepFiles="*/Config/Tags/*"
+DirectoryIndexKeepFiles="*/Content/Localization/*"
+DirectoryIndexKeepFiles="*/Content/Internationalization/*"
+DirectoryIndexKeepFiles="*/Content/Movies/*"
+DirectoryIndexKeepFiles="*/Plugins/*.uplugin*"
+DirectoryIndexKeepFiles="*/*.ini*"
+DirectoryIndexKeepFiles="*/*.upipelinecache*"
+DirectoryIndexKeepEmptyDirectories=*/Plugins/*
+DirectoryIndexKeepEmptyDirectories="*/PipelineCaches/*"
+IndexValidationIgnore="*/Saved/Logs/*"

[/Script/GameplayDebugger.GameplayDebuggingReplicator]
MaxEQSQueries=5
DebugComponentClassName="/Script/GameplayDebugger.GameplayDebuggingComponent"
DebugComponentHUDClassName="/Script/GameplayDebugger.GameplayDebuggingHUDComponent"
DebugComponentControllerClassName = "/Script/GameplayDebugger.GameplayDebuggingControllerComponent"

[/Script/GameplayDebugger.GameplayDebuggingHUDComponent]
MenuStartX=10.0
MenuStartY=10.0
DebugInfoStartX=20.0
DebugInfoStartY=60.0

[/Script/IOSRuntimeSettings.IOSRuntimeSettings]
bEnableStoreKitSupport=True
bEnableGameCenterSupport=False
bSupportsPortraitOrientation=False
bSupportsITunesFileSharing=False
bSupportsUpsideDownOrientation=False
bSupportsLandscapeLeftOrientation=True
bSupportsLandscapeRightOrientation=True
PreferredLandscapeOrientation=LandscapeLeft
bSupportsMetal=True
bSupportsMetalMRT=False
bSupportHighRefreshRates=False
bGameSupportsMultipleActiveControllers=False
bAllowRemoteRotation=True
bUseAbsoluteDpadValues=False
bAllowControllers=True
bBuildAsFramework=False
bGenerateFrameworkWrapperProject=True
bGeneratedSYMFile=False
bDisableHTTPS=false
bUseRSync=True
BundleDisplayName=[PROJECT_NAME]
BundleName=[PROJECT_NAME]
BundleIdentifier=com.YourCompany.[PROJECT_NAME]
DevCodeSigningIdentity=iPhone Developer:;Apple Development:
DistroCodeSigningIdentity=iPhone Distribution:;Apple Distribution:
VersionInfo=1.0
FrameRateLock=PUFRL_30
bEnableDynamicMaxFPS=False
bSupportsIPad=True
bSupportsIPhone=True
bEnableSplitView=False
bEnableSimulatorSupport=False
AdditionalPlistData=
RemoteServerName=
RemoteServerOverrideBuildPath=
SecondaryRemoteServerName=
SecondaryRemoteServerOverrideBuildPath=
RSyncUsername=
SecondaryRSyncUsername=
SSHPrivateKeyOverridePath=
SecondarySSHPrivateKeyOverridePath=
bEnableRemoteNotificationsSupport=False
bEnableCloudKitSupport=False
bUserSwitching=False
bSupportSecondaryMac=False
IOSCloudKitSyncStrategy=None
bGenerateCrashReportSymbols=false
bAutomaticSigning=false
UseFastIntrinsics=False
ForceFloats=True
EnableMathOptimisations=True
MetalLanguageVersion=0
bDisableMotionData=False
bUsesNonExemptEncryption=false
ITSEncryptionExportComplianceCode=""
bEnableAdvertisingIdentifier=True

[/Script/AndroidRuntimeSettings.AndroidRuntimeSettings]
SDKAPILevelOverride=
NDKAPILevelOverride=
BuildToolsOverride=
bEnableGooglePlaySupport=false
bSupportAdMob=true
bBuildForArm64=true
bBuildForX8664=false
bBuildForES31=true
bSupportsVulkan=true
bSupportsVulkanSM5=false
bDetectVulkanByDefault=true
bSplitIntoSeparateApks=false
bPackageDataInsideApk=false
bUseExternalFilesDir=false
bPublicLogFiles=true
bCreateAllPlatformsInstall=false
Orientation=SensorLandscape
InstallLocation=InternalOnly
DepthBufferPreference=Default
PackageName=com.YourCompany.[PROJECT]
StoreVersion=1
StoreVersionOffsetArmV7=0
StoreVersionOffsetArm64=0
StoreVersionOffsetX86=0
StoreVersionOffsetX8664=0
VersionDisplayName=1.0
MinSDKVersion=26
TargetSDKVersion=28
bEnableLint=false
bShowLaunchImage=true
bValidateTextureFormats=true
bMultiTargetFormat_ETC2=true
bMultiTargetFormat_DXT=true
bMultiTargetFormat_ASTC=true
TextureFormatPriority_ETC2=0.2
TextureFormatPriority_DXT=0.6
TextureFormatPriority_ASTC=0.9
bEnableNewKeyboard=True
bAndroidVoiceEnabled=false
bRecordPermissionAtStartupEnabled=false
bBuildWithHiddenSymbolVisibility=True
bSaveSymbols=false
bAllowControllers=True
bAllowIMU=False
bUseDisplayCutout=False
bEnableSnapshots=False
bRestoreNotificationsOnReboot=False
bEnableBundle=False
bEnableUniversalAPK=True
bBundleABISplit=True
bBundleLanguageSplit=True
bBundleDensitySplit=True
bFullScreen=True
bStripShaderReflection=True
bForceSmallOBBFiles=False
bAllowLargeOBBFiles=False
bAllowPatchOBBFile=False
bAllowOverflowOBBFiles=False
bExtractNativeLibs=true
bEnableDomStorage=False
bSkipLibCpp=True
bDisableLibCppSharedDependencyValidation=False
bDisableFunctionDataSections=False
bEnableAdvancedBinaryCompression=True
bDisableStackProtector=False
bEnableMulticastSupport=False
bEnableRayTracing=true
bAllowResizing=false
bSupportSizeChanges=false

[/Script/AndroidPlatformEditor.AndroidSDKSettings]
SDKAPILevel=latest
NDKAPILevel=android-26

[/Script/UnrealEd.CookerSettings]
DefaultASTCQualityBySpeed=2
DefaultASTCQualityBySize=3
DefaultASTCQualityBySizeHQ=4
+ClassesExcludedOnDedicatedServer=WidgetBlueprint
+ClassesExcludedOnDedicatedServer=GroupActor
+ClassesExcludedOnDedicatedServer=MetaData
+ClassesExcludedOnDedicatedServer=ObjectRedirector
+ClassesExcludedOnDedicatedServer=NavMeshRenderingComponent
+ClassesExcludedOnDedicatedServer=ReflectionCaptureComponent
+ClassesExcludedOnDedicatedServer=TextRenderComponent
+ClassesExcludedOnDedicatedServer=Font
+ClassesExcludedOnDedicatedServer=InterpCurveEdSetup
+ClassesExcludedOnDedicatedServer=MaterialExpression
+ClassesExcludedOnDedicatedServer=NiagaraEmitter
+ClassesExcludedOnDedicatedServer=NiagaraScript
+ClassesExcludedOnDedicatedServer=ParticleEmitter
+ClassesExcludedOnDedicatedServer=ParticleLODLevel
+ClassesExcludedOnDedicatedServer=ParticleModule
+ClassesExcludedOnDedicatedServer=SubUVAnimation
+ClassesExcludedOnDedicatedServer=SoundNode
+ClassesExcludedOnDedicatedServer=GameplayEffectUIData
+ClassesExcludedOnDedicatedServer=AmbientSound
+ClassesExcludedOnDedicatedClient=WidgetBlueprint
+ClassesExcludedOnDedicatedClient=GroupActor
+ClassesExcludedOnDedicatedClient=MetaData
+ClassesExcludedOnDedicatedClient=ObjectRedirector
+ClassesExcludedOnDedicatedClient=InterpCurveEdSetup

+VersionedIntRValues=r.AllowStaticLighting
+VersionedIntRValues=r.MaterialEditor.LWCTruncateMode
+VersionedIntRValues=r.GBuffer
+VersionedIntRValues=r.VelocityOutputPass
+VersionedIntRValues=r.SelectiveBasePassOutputs
+VersionedIntRValues=r.DBuffer
+VersionedIntRValues=r.Mobile.DBuffer
+VersionedIntRValues=r.Shaders.Symbols
+VersionedIntRValues=r.Shaders.GenerateSymbols
+VersionedIntRValues=r.Shaders.WriteSymbols
+VersionedIntRValues=r.Shaders.AllowUniqueSymbols
+VersionedIntRValues=r.Shaders.ExtraData
+VersionedIntRValues=r.Shaders.Optimize
+VersionedIntRValues=r.CompileShadersForDevelopment
+VersionedIntRValues=r.MobileHDR
+VersionedIntRValues=r.UsePreExposure

[/Script/UnrealEd.ChunkDependencyInfo]

[/Script/Engine.PhysicsSettings]
DefaultGravityZ=-980.0
bEnable2DPhysics=false

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
+D3D12TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
bEnableDistanceFields=true
bEnableRayTracing=true
bTarget32Bit=false

[/Script/LinuxTargetPlatform.LinuxTargetSettings]
+TargetedRHIs=SF_VULKAN_SM5
bEnableRayTracing=true

[/Script/MacTargetPlatform.MacTargetSettings]
MetalLanguageVersion=7
+TargetedRHIs=SF_METAL_SM5
UseFastIntrinsics=False
EnableMathOptimisations=True
TargetArchitecture=MacTargetArchitectureUniversal
EditorTargetArchitecture=MacTargetArchitectureUniversal
EditorDefaultArchitecture=MacTargetArchitectureHost
DefaultArchitecture=MacTargetArchitectureHost
bBuildAllSupportedOnBuildMachine=true
bEnableRayTracing=false

[/Script/MacTargetPlatform.XcodeProjectSettings]
bUseModernXcode=true
bUseAutomaticCodeSigning=true
bMacSignToRunLocally=false
bUsePremadeDSYMInXcArchive=false
CodeSigningTeam=
CodeSigningPrefix=com.YourCompany
BundleIdentifier=$(UE_SIGNING_PREFIX).$(UE_PRODUCT_NAME_STRIPPED)
AppCategory=public.app-category.games
TemplateMacPlist=(FilePath="/Game/Build/Mac/Resources/Info.Template.plist")
TemplateIOSPlist=(FilePath="/Game/Build/IOS/UBTGenerated/Info.Template.plist")
PremadeMacEntitlements=(FilePath="/Game/Build/Mac/Resources/Sandbox.Server.entitlements")
ShippingSpecificMacEntitlements=(FilePath="/Game/Build/Mac/Resources/Sandbox.NoNet.entitlements")
bUseEntitlementsForPrograms=false
MacSigningIdentity=Apple Development
IOSSigningIdentity=Apple Development
AdditionalPrivacyInfoMac=(FilePath="/Game/Build/Mac/Resources/PrivacyInfo.xcprivacy")
AdditionalPrivacyInfoIOS=(FilePath="/Game/Build/IOS/Resources/PrivacyInfo.xcprivacy")

[AutomationTestExcludelistSettings:Windows]
+SupportedRHIs=DirectX 11
+SupportedRHIs=DirectX 12
+SupportedRHIs=Vulkan
+SupportedRHIs=Null

[AutomationTestExcludelistSettings:Linux]
+SupportedRHIs=Vulkan
+SupportedRHIs=Null

[AutomationTestExcludelistSettings:Mac]
+SupportedRHIs=Metal
+SupportedRHIs=Null

[HMDPluginPriority]
OculusHMD=40
SteamVR=30
OpenXRHMD=20

[/Script/OculusHMD.OculusHMDRuntimeSettings]
XrApi=NativeOpenXR

[/Script/Engine.AISystemBase]
AISystemModuleName=AIModule
AISystemClassName=/Script/AIModule.AISystem

[/Script/AIModule.AISystem]
PerceptionSystemClassName=/Script/AIModule.AIPerceptionSystem

[AutomationController.History]
bTrackHistory=false
NumberOfHistoryItemsTracked=5

[VisualLogger]
FrameCacheLenght=1.0f ;in seconds, to batch log data between file serializations
UseCompression=false ;works only with binary files

[GameplayDebuggerSettings]
OverHead=True
Basic=True
BehaviorTree=False
EQS=False
EnableEQSOnHUD=true
Perception=False
GameView1=False
GameView2=False
GameView3=False
GameView4=False
GameView5=False
NameForGameView1="GameView1"
NameForGameView2="GameView2"
NameForGameView3="GameView3"
NameForGameView4="GameView4"
NameForGameView5="GameView5"

[Browser]
bForceMessageLoop=true

[PacketSimulationProfile.Off]
PktLoss=0
PktIncomingLoss=0
PktLagMin=0
PktLagMax=0
PktIncomingLagMin=0
PktIncomingLagMax=0

[PacketSimulationProfile.Average]
PktLoss=1
PktIncomingLoss=1
PktLagMin=30
PktLagMax=60
PktIncomingLagMin=30
PktIncomingLagMax=60

[PacketSimulationProfile.Bad]
PktLoss=5
PktIncomingLoss=5
PktLagMin=100
PktLagMax=200
PktIncomingLagMin=100
PktIncomingLagMax=200

[/Script/Engine.NetworkSettings]
+NetworkEmulationProfiles=(ProfileName="Average",ToolTip="Simulates average internet conditions")
+NetworkEmulationProfiles=(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")

[PacketHandlerComponents]

[/Script/GameplayDebugger.GameplayDebuggingControllerComponent]
CategoryZeroBind=(Key=NumPadZero,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryOneBind=(Key=NumPadOne,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryTwoBind=(Key=NumPadTwo,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryThreeBind=(Key=NumPadThree,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryFourBind=(Key=NumPadFour,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryFiveBind=(Key=NumPadFive,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategorySixBind=(Key=NumPadSix,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategorySevenBind=(Key=NumPadSeven,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryEightBind=(Key=NumPadEight,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryNineBind=(Key=NumPadNine,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CycleDetailsViewBind=(Key=Add,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
DebugCameraBind=(Key=Tab,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
OnScreenDebugMessagesBind=(Key=Tab,bShift=False,bCtrl=True,bAlt=False,bCmd=False)
GameHUDBind=(Key=Tilde,bShift=False,bCtrl=True,bAlt=False,bCmd=False)

[/Script/GameFeatures.GameFeatureAction_AddChunkOverride]
LowestAllowedChunkIndexForAutoGeneration=100

[/Script/Engine.SkeletalMeshLODSettings]
+LODGroups=(ScreenSize=(Default=1.0,PerPlatform=()),ReductionSettings=(NumOfTrianglesPercentage=.5))
+LODGroups=(ScreenSize=(Default=.3,PerPlatform=()),ReductionSettings=(NumOfTrianglesPercentage=.25))
+LODGroups=(ScreenSize=(Default=.15,PerPlatform=()),ReductionSettings=(NumOfTrianglesPercentage=.125))
+LODGroups=(ScreenSize=(Default=.1,PerPlatform=()),ReductionSettings=(NumOfTrianglesPercentage=.06))

[/Script/Engine.PlayerCameraManager]
ServerUpdateCameraTimeout=2.0

[/Script/CinematicCamera.CineCameraSettings]
+FilmbackPresets=(Name="16:9 Film",FilmbackSettings=(SensorWidth=24.00,SensorHeight=13.5))
+FilmbackPresets=(Name="16:9 Digital Film",FilmbackSettings=(SensorWidth=23.76,SensorHeight=13.365))
+FilmbackPresets=(Name="16:9 DSLR",FilmbackSettings=(SensorWidth=36,SensorHeight=20.25))
+FilmbackPresets=(Name="Super 8mm",FilmbackSettings=(SensorWidth=5.79,SensorHeight=4.01))
+FilmbackPresets=(Name="Super 16mm",FilmbackSettings=(SensorWidth=12.52,SensorHeight=7.58))
+FilmbackPresets=(Name="Super 35mm",FilmbackSettings=(SensorWidth=24.89,SensorHeight=18.66))
+FilmbackPresets=(Name="35mm Academy",FilmbackSettings=(SensorWidth=21.946,SensorHeight=16.002))
+FilmbackPresets=(Name="35mm Full Aperture",FilmbackSettings=(SensorWidth=24.892,SensorHeight=18.9121))
+FilmbackPresets=(Name="35mm VistaVision",FilmbackSettings=(SensorWidth=37.719,SensorHeight=25.146))
+FilmbackPresets=(Name="IMAX 70mm",FilmbackSettings=(SensorWidth=70.41,SensorHeight=56.63))
+FilmbackPresets=(Name="APS-C (Canon)",FilmbackSettings=(SensorWidth=22.2,SensorHeight=14.8))
+FilmbackPresets=(Name="Full Frame DSLR",FilmbackSettings=(SensorWidth=36,SensorHeight=24))
+FilmbackPresets=(Name="Micro Four Thirds",FilmbackSettings=(SensorWidth=17.3,SensorHeight=13))
DefaultFilmbackPreset="16:9 Digital Film"
+LensPresets=(Name="12mm Prime f/2.8",LensSettings=(MinFocalLength=12,MaxFocalLength=12,MinFStop=2.8,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="30mm Prime f/1.4",LensSettings=(MinFocalLength=30,MaxFocalLength=30,MinFStop=1.4,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="50mm Prime f/1.8",LensSettings=(MinFocalLength=50,MaxFocalLength=50,MinFStop=1.8,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="85mm Prime f/1.8",LensSettings=(MinFocalLength=85,MaxFocalLength=85,MinFStop=1.8,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="105mm Prime f/2",LensSettings=(MinFocalLength=105,MaxFocalLength=105,MinFStop=2,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="200mm Prime f/2",LensSettings=(MinFocalLength=200,MaxFocalLength=200,MinFStop=2,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="24-70mm Zoom f/2.8",LensSettings=(MinFocalLength=24,MaxFocalLength=70,MinFStop=2.8,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="70-200mm Zoom f/2.8",LensSettings=(MinFocalLength=70,MaxFocalLength=200,MinFStop=2.8,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="Universal Zoom",LensSettings=(MinFocalLength=4,MaxFocalLength=1000,MinFStop=1.2,MaxFStop=22,DiaphragmBladeCount=7))
DefaultLensPresetName="Universal Zoom"
+CropPresets=(Name="No Crop",CropSettings=(AspectRatio=0))
+CropPresets=(Name="1.33 (4:3)",CropSettings=(AspectRatio=1.333333))
+CropPresets=(Name="1.77 (16:9)",CropSettings=(AspectRatio=1.777778))
+CropPresets=(Name="2.39",CropSettings=(AspectRatio=2.39))
DefaultCropPresetName="No Crop"
DefaultLensFocalLength=35
DefaultLensFStop=2.8

[/Script/TcpMessaging.TcpMessagingSettings]
EnableTransport=True
ListenEndpoint=
!ConnectToEndpoints=CLEAR_ARRAY
ConnectionRetryDelay=2

[CrashReportClient]
bHideLogFilesOption=false
bIsAllowedToCloseWithoutSending=true
CrashConfigPurgeDays=2
Stall.RecordDump=false
Ensure.RecordDump=true

[SteamVR.Settings]
HMDWornMovementThreshold = 50.0

[/Script/Engine.AnimationSettings]
bStripAnimationDataOnDedicatedServer=False
BoneTimecodeCustomAttributeNameSettings=(HourAttributeName="TCHour",MinuteAttributeName="TCMinute",SecondAttributeName="TCSecond",FrameAttributeName="TCFrame",SubframeAttributeName="TCSubframe",RateAttributeName="TCRate",TakenameAttributeName="TCSlate")

[Animation.DefaultObjectSettings]
BoneCompressionSettingsFallback="/Engine/Animation/DefaultAnimBoneCompressionSettings"
BoneCompressionSettings="/ACLPlugin/ACLAnimBoneCompressionSettings"
AnimationRecorderBoneCompressionSettings="/Engine/Animation/DefaultRecorderBoneCompression"
CurveCompressionSettingsFallback="/Engine/Animation/DefaultAnimCurveCompressionSettings"
CurveCompressionSettings="/ACLPlugin/ACLAnimCurveCompressionSettings"
VariableFrameStrippingSettings="/Engine/Animation/DefaultVariableFrameStrippingSettings"

[/Script/Engine.MeshSimplificationSettings]
r.MeshReductionModule="QuadricMeshReduction"

[/Script/ClassViewer.ClassViewerProjectSettings]
+InternalOnlyPaths=(Path="/Engine/VREditor")
+InternalOnlyPaths=(Path="/Engine/Sequencer")
+InternalOnlyPaths=(Path="/Engine/NotForLicensees")
+InternalOnlyClasses=/Script/VREditor.VREditorBaseUserWidget
+InternalOnlyClasses=/Script/LevelSequence.LevelSequenceBurnIn

[/Script/ClassViewer.StructViewerProjectSettings]
+InternalOnlyPaths=(Path="/Engine/VREditor")
+InternalOnlyPaths=(Path="/Engine/Sequencer")
+InternalOnlyPaths=(Path="/Engine/NotForLicensees")

[/Script/LevelSequence.LevelSequence]
DefaultCompletionMode=RestoreState

[/Script/TemplateSequence.TemplateSequence]
DefaultCompletionMode=RestoreState

[PlatformCrypto]
PlatformRequiresDataCrypto=False
PakSigningRequired=True
PakEncryptionRequired=True

[/Script/AppleARKit.AppleARKitSettings]
bEnableLiveLinkForFaceTracking=true
LiveLinkPublishingPort=11111
bRequireDeviceSupportsARKit=true

[/Script/Engine.RendererSettings]
r.GPUCrashDebugging=false
MaxSkinBones=(Default=65536,PerPlatform=(("Mobile", 256)))

[Messaging]
bAllowDelayedMessaging=false

[/Script/ChaosSolverEngine.ChaosSolverSettings]
DefaultChaosSolverActorClass=/Script/ChaosSolverEngine.ChaosSolverActor

[PlatformPaths]
UnrealBuildTool=Engine/Build/BatchFiles/Build.bat

[AlternateTextureCompression]
TextureCompressionFormat="TextureFormatOodle"

[TextureFormatOodleSettings]
bDebugColor=False
DebugDumpFilter=
LogVerbosity=0
GlobalLambdaMultiplier=1.000000

[/Script/Engine.TextureEncodingProjectSettings]
bFinalUsesRDO=false
FinalRDOLambda=30
FinalEffortLevel=Normal
FinalUniversalTiling=Disabled
bFastUsesRDO=false
FastRDOLambda=30
FastEffortLevel=Normal
FastUniversalTiling=Disabled
CookUsesSpeed=Final
EditorUsesSpeed=FinalIfAvailable

[OodleDataCompressionFormat]
PreallocatedBufferCount=2

[Plugins]
+ConfigFilesPluginsCannotOverride=EditorPerProjectUserSettings
[ClothSettings]
EnvironmentCollisionPadding = 2.f
EnvironmentCollisionThickness = 2.f
EnvironmentCollisionMaxShapes = 32

[BuildPatchServices]
bEnableDiskOverflowStore=true

[/Script/Engine.BlueprintGeneratedClass]
+RequiresCompleteValueForPostConstruction=/Script/Engine.BodyInstance:CollisionResponses

[Ias]
EnableInEditor=False
CreatePerContainerTocs=False
UsePerContainerTocs=False

[/Script/InterchangeEngine.InterchangeProjectSettings]
ContentImportSettings=(DefaultPipelineStackOverride=((Materials, "Materials"),(Textures, "Textures")),ShowImportDialogOverride=((Textures, (bShowImportDialog=False))),PipelineStacks=(("Assets", (Pipelines=("/Interchange/Pipelines/DefaultAssetsPipeline.DefaultAssetsPipeline"),PerTranslatorPipelines=((Translator="/Script/InterchangeImport.InterchangeMaterialXTranslator",Pipelines=("/Interchange/Pipelines/DefaultAssetsPipeline.DefaultAssetsPipeline","/Interchange/Pipelines/DefaultMaterialXPipeline.DefaultMaterialXPipeline")),(Translator="/Script/InterchangeImport.InterchangeGLTFTranslator",Pipelines=("/Interchange/Pipelines/DefaultGLTFAssetsPipeline.DefaultGLTFAssetsPipeline","/Interchange/Pipelines/DefaultGLTFPipeline.DefaultGLTFPipeline")),(Translator="/Script/InterchangeImport.InterchangeUSDTranslator",Pipelines=("/Interchange/Pipelines/DefaultAssetsPipeline.DefaultAssetsPipeline","/Interchange/Pipelines/DefaultMaterialXPipeline.DefaultMaterialXPipeline"))))),("Materials", (Pipelines=("/Interchange/Pipelines/DefaultMaterialPipeline.DefaultMaterialPipeline"),PerTranslatorPipelines=((Translator="/Script/InterchangeImport.InterchangeMaterialXTranslator",Pipelines=("/Interchange/Pipelines/DefaultMaterialPipeline.DefaultMaterialPipeline","/Interchange/Pipelines/DefaultMaterialXPipeline.DefaultMaterialXPipeline"))))),("Textures", (Pipelines=("/Interchange/Pipelines/DefaultTexturePipeline.DefaultTexturePipeline")))),DefaultPipelineStack="Assets",ImportDialogClass="/Script/InterchangeEditorPipelines.InterchangePipelineConfigurationGeneric",bShowImportDialog=True)
SceneImportSettings=(PipelineStacks=(("Scene", (Pipelines=("/Interchange/Pipelines/DefaultSceneAssetsPipeline.DefaultSceneAssetsPipeline","/Interchange/Pipelines/DefaultSceneLevelPipeline.DefaultSceneLevelPipeline"),PerTranslatorPipelines=((Translator="/Script/InterchangeImport.InterchangeMaterialXTranslator",Pipelines=("/Interchange/Pipelines/DefaultSceneAssetsPipeline.DefaultSceneAssetsPipeline","/Interchange/Pipelines/DefaultSceneLevelPipeline.DefaultSceneLevelPipeline","/Interchange/Pipelines/DefaultMaterialXPipeline.DefaultMaterialXPipeline")),(Translator="/Script/InterchangeImport.InterchangeGLTFTranslator",Pipelines=("/Interchange/Pipelines/DefaultGLTFSceneAssetsPipeline.DefaultGLTFSceneAssetsPipeline","/Interchange/Pipelines/DefaultSceneLevelPipeline.DefaultSceneLevelPipeline","/Interchange/Pipelines/DefaultGLTFPipeline.DefaultGLTFPipeline")),(Translator="/Script/InterchangeImport.InterchangeUSDTranslator",Pipelines=("/Interchange/Pipelines/DefaultSceneAssetsPipeline.DefaultSceneAssetsPipeline","/Interchange/Pipelines/DefaultSceneLevelPipeline.DefaultSceneLevelPipeline","/Interchange/Pipelines/DefaultMaterialXPipeline.DefaultMaterialXPipeline")))))),DefaultPipelineStack="Scene",ImportDialogClass="/Script/InterchangeEditorPipelines.InterchangePipelineConfigurationGeneric",bShowImportDialog=True)
FilePickerClass=/Script/InterchangeEditorUtilities.InterchangeFilePickerGeneric
bStaticMeshUseSmoothEdgesIfSmoothingInformationIsMissing=True
GenericPipelineClass=/Script/InterchangePipelines.InterchangeGenericAssetsPipeline
ConverterDefaultPipeline=/Interchange/Pipelines/DefaultAssetsPipeline.DefaultAssetsPipeline

[/Script/Engine.NetworkMetricsCSV_Replication]
IntervalSeconds=0

[/Script/Engine.NetworkMetricsPerfCounters]
IntervalSeconds=1

[/Script/Engine.NetworkMetricsConfig]
+Listeners=(MetricName=NumConnections, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=Connections, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=GatherPrioritizeTimeMS, Class=/Script/Engine.NetworkMetricsCSV_Replication, EnableMode=ENetworkMetricEnableMode::EnableForNonIrisOnly)
+Listeners=(MetricName=InPacketsClientAvg, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=InPacketsClientMax, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=NumberOfActiveActors, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=NumberOfFullyDormantActors, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=NumClientUpdateLevelVisibility, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=NumOpenChannels, Class=/Script/Engine.NetworkMetricsCSV_Replication, EnableMode=ENetworkMetricEnableMode::EnableForNonIrisOnly)
+Listeners=(MetricName=NumReplicateActorCallsPerConAvg, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=NumSkippedObjectEmptyUpdates, Class=/Script/Engine.NetworkMetricsCSV_Replication, EnableMode=ENetworkMetricEnableMode::EnableForNonIrisOnly)
+Listeners=(MetricName=NumTickingChannels, Class=/Script/Engine.NetworkMetricsCSV_Replication, EnableMode=ENetworkMetricEnableMode::EnableForNonIrisOnly)
+Listeners=(MetricName=OutKBytes, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=OutNetGUIDKBytesSec, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=OutPacketsClientAvg, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=OutPacketsClientMax, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=ReplicateActorTimeMS, Class=/Script/Engine.NetworkMetricsCSV_Replication, EnableMode=ENetworkMetricEnableMode::EnableForNonIrisOnly)
+Listeners=(MetricName=SatConnections, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=ServerReplicateActorTimeMS, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=SharedSerializationPropertyHit, Class=/Script/Engine.NetworkMetricsCSV_Replication, EnableMode=ENetworkMetricEnableMode::EnableForNonIrisOnly)
+Listeners=(MetricName=SharedSerializationPropertyMiss, Class=/Script/Engine.NetworkMetricsCSV_Replication, EnableMode=ENetworkMetricEnableMode::EnableForNonIrisOnly)
+Listeners=(MetricName=SharedSerializationRPCHit, Class=/Script/Engine.NetworkMetricsCSV_Replication, EnableMode=ENetworkMetricEnableMode::EnableForNonIrisOnly)
+Listeners=(MetricName=SharedSerializationRPCMiss, Class=/Script/Engine.NetworkMetricsCSV_Replication, EnableMode=ENetworkMetricEnableMode::EnableForNonIrisOnly)
+Listeners=(MetricName=OutgoingReliableMessageQueueMaxSize, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=IncomingReliableMessageQueueMaxSize, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=InPacketsPerFrame, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=OutPacketsPerFrame, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=InLostPacketsFoundPerFrame, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=OutLostPacketsFoundPerFrame, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=Ping, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=InRate, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=OutRate, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=AverageJitterInMS, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=InPacketsLost, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=OutPacketsLost, Class=/Script/Engine.NetworkMetricsCSV_Replication)
+Listeners=(MetricName=NumConnections, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=AvgPing, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=InBunches, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=InPackets, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=InPacketsClientPerSecondAvg, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=InPacketsClientPerSecondMax, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=InPacketsClientPerSecondMin, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=InPacketsLost, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=InRate, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=InRateClientAvg, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=InRateClientMax, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=InRateClientMin, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=MaxPacketOverhead, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=MaxPing, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=MinPing, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=NumClients, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=Connections, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=OutBunches, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=OutPackets, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=OutPacketsClientPerSecondAvg, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=OutPacketsClientPerSecondMax, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=OutPacketsClientPerSecondMin, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=OutPacketsLost, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=OutRate, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=OutRateClientAvg, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=OutRateClientMax, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=OutRateClientMin, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=PingBucketInt0, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=PingBucketInt1, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=PingBucketInt2, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=PingBucketInt3, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=PingBucketInt4, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=PingBucketInt5, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=PingBucketInt6, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=PingBucketInt7, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=ClosedConnectionsDueToReliableBufferOverflow, Class=/Script/Engine.NetworkMetricsPerfCounters)
+Listeners=(MetricName=AddedConnections, Class=/Script/Engine.NetworkMetricsPerfCounters)

[StudioTelemetry.Config]
SendTelemetry=true
SendUserData=false
SendHardwareData=true
SendOSData=true



[/Script/Engine.WorldPartitionSettings]
EditorHashDefaultClass=/Script/CoreUObject.Class'/Script/Engine.WorldPartitionEditorSpatialHash'
RuntimeHashDefaultClass=/Script/CoreUObject.Class'/Script/Engine.WorldPartitionRuntimeHashSet'

[BuildPatchTool]
VerificationThreadCount=4
