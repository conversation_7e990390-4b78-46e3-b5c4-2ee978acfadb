[Audio]
PlatformHeadroomDB=0
PlatformFormat=OGG
PlatformStreamingFormat=OGG

[SystemSettings]
r.setres=1280x720
fx.NiagaraAllowRuntimeScalabilityChanges=1
QualityLevelMapping="high"
r.Shadow.DetectVertexShaderLayerAtRuntime=1

[SystemSettingsEditor]

[TextureStreaming]
PoolSizeVRAMPercentage=70

[DeviceProfileManager]
DeviceProfileSelectionModule="LinuxDeviceProfileSelector"

[ConsoleVariables]
g.TimeoutForBlockOnRenderFence=60000

[PlatformCrypto]
PlatformRequiresDataCrypto=True

[/Script/Engine.RendererSettings]
r.Shaders.RemoveUnusedInterpolators=1
