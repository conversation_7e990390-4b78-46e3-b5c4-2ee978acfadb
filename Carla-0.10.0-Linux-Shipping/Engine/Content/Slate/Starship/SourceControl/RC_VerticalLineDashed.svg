<?xml version="1.0" encoding="utf-8"?>
<svg width="26px" height="26px" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg">
  <g style="" transform="matrix(1, 0, 0, 0.8125, -3, 0)">
    <title>Layer 1</title>
    <line stroke-linecap="undefined" stroke-linejoin="undefined" id="svg_2" y2="32" x2="16" y1="0" x1="16" style="fill: rgb(128, 128, 128); paint-order: fill; stroke-width: 2px; stroke-opacity: 0.4; stroke: rgb(100, 100, 100); stroke-dasharray: 4, 3;"/>
  </g>
</svg>