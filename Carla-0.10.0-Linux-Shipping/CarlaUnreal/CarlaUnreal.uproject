{"FileVersion": 3, "EngineAssociation": "{EFC07C98-4070-2F0E-BE31-319DCE259DFF}", "Category": "", "Description": "", "Modules": [{"Name": "CarlaUnreal", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "AdditionalDependencies": ["Engine", "<PERSON>", "CoreUObject"]}], "Plugins": [{"Name": "<PERSON>", "Enabled": true}, {"Name": "RenderDocPlugin", "Enabled": true}, {"Name": "Car<PERSON>im", "Enabled": false, "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/content/2d712649ca864c80812da7b5252f5608"}, {"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "CarlaT<PERSON><PERSON>", "Enabled": true}, {"Name": "PythonScriptPlugin", "Enabled": true}, {"Name": "AlembicHairImporter", "Enabled": true}, {"Name": "HairStrands", "Enabled": true}, {"Name": "SunPosition", "Enabled": true}, {"Name": "ModelingToolsEditorMode", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enabled": true}, {"Name": "Volumetrics", "Enabled": true}, {"Name": "PerformanceMonitor", "Enabled": true}], "TargetPlatforms": ["Linux", "LinuxArm64", "Windows"]}