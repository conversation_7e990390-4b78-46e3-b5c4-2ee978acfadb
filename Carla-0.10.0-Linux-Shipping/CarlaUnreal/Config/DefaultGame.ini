[/Script/EngineSettings.GeneralProjectSettings]
ProjectID=675BF8694238308FA9368292CC440350
ProjectName=CarlaUnreal
CompanyName=CVC
CopyrightNotice="Copyright (c) 2024 Computer Vision Center (CVC) at the Universitat Autonoma de Barcelona (UAB). This work is licensed under the terms of the MIT license. For a copy, see <https://opensource.org/licenses/MIT>."
ProjectVersion=0.10.0

[/Script/Carla.CarlaSettings]
# +LowRoadMaterials=(MaterialInterface=MaterialInstanceConstant'"/Game/Carla/Static/GenericMaterials/Ground/SimpleRoad/CheapRoad.CheapRoad"',MaterialSlotName="Tileroad_Road",ImportedMaterialSlotName="",UVChannelData=(bInitialized=False,bOverrideDensities=False,LocalUVDensities[0]=0.000000,LocalUVDensities[1]=0.000000,LocalUVDensities[2]=0.000000,LocalUVDensities[3]=0.000000))
# +LowRoadMaterials=(MaterialInterface=MaterialInstanceConstant'"/Game/Carla/Static/GenericMaterials/Ground/SimpleRoad/CheapSideWalkCurb.CheapSideWalkCurb"',MaterialSlotName="TileRoad_Curb",ImportedMaterialSlotName="",UVChannelData=(bInitialized=False,bOverrideDensities=False,LocalUVDensities[0]=0.000000,LocalUVDensities[1]=0.000000,LocalUVDensities[2]=0.000000,LocalUVDensities[3]=0.000000))
# +LowRoadMaterials=(MaterialInterface=MaterialInstanceConstant'"/Game/Carla/Static/GenericMaterials/Ground/SimpleRoad/CheapSideWalk_00.CheapSideWalk_00"',MaterialSlotName="Tileroad_Sidewalk",ImportedMaterialSlotName="",UVChannelData=(bInitialized=False,bOverrideDensities=False,LocalUVDensities[0]=0.000000,LocalUVDensities[1]=0.000000,LocalUVDensities[2]=0.000000,LocalUVDensities[3]=0.000000))
# +LowRoadMaterials=(MaterialInterface=MaterialInstanceConstant'"/Game/Carla/Static/GenericMaterials/Ground/SimpleRoad/CheapLaneMarking.CheapLaneMarking"',MaterialSlotName="TileRoad_LaneMarkingSolid",ImportedMaterialSlotName="",UVChannelData=(bInitialized=False,bOverrideDensities=False,LocalUVDensities[0]=0.000000,LocalUVDensities[1]=0.000000,LocalUVDensities[2]=0.000000,LocalUVDensities[3]=0.000000))
# +EpicRoadMaterials=(MaterialInterface=MaterialInstanceConstant'"/Game/Carla/Static/GenericMaterials/WetPavement/WetPavement_Complex_Road_N2.WetPavement_Complex_Road_N2"',MaterialSlotName="Tileroad_Road",ImportedMaterialSlotName="",UVChannelData=(bInitialized=False,bOverrideDensities=False,LocalUVDensities[0]=0.000000,LocalUVDensities[1]=0.000000,LocalUVDensities[2]=0.000000,LocalUVDensities[3]=0.000000))
# +EpicRoadMaterials=(MaterialInterface=MaterialInstanceConstant'"/Game/Carla/Static/GenericMaterials/WetPavement/WetPavement_Complex_Concrete.WetPavement_Complex_Concrete"',MaterialSlotName="TileRoad_Curb",ImportedMaterialSlotName="",UVChannelData=(bInitialized=False,bOverrideDensities=False,LocalUVDensities[0]=0.000000,LocalUVDensities[1]=0.000000,LocalUVDensities[2]=0.000000,LocalUVDensities[3]=0.000000))
# +EpicRoadMaterials=(MaterialInterface=MaterialInstanceConstant'"/Game/Carla/Static/GenericMaterials/Ground/SideWalks/SidewalkN4/WetPavement_SidewalkN4.WetPavement_SidewalkN4"',MaterialSlotName="Tileroad_Sidewalk",ImportedMaterialSlotName="",UVChannelData=(bInitialized=False,bOverrideDensities=False,LocalUVDensities[0]=0.000000,LocalUVDensities[1]=0.000000,LocalUVDensities[2]=0.000000,LocalUVDensities[3]=0.000000))
# +EpicRoadMaterials=(MaterialInterface=MaterialInstanceConstant'"/Game/Carla/Static/GenericMaterials/LaneMarking/Lanemarking.Lanemarking"',MaterialSlotName="TileRoad_LaneMarkingSolid",ImportedMaterialSlotName="",UVChannelData=(bInitialized=False,bOverrideDensities=False,LocalUVDensities[0]=0.000000,LocalUVDensities[1]=0.000000,LocalUVDensities[2]=0.000000,LocalUVDensities[3]=0.000000))
LowLightFadeDistance=1000.000000
LowStaticMeshMaxDrawDistance=10000.000000
LowRoadPieceMeshMaxDrawDistance=15000.000000

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_Development
StagingDirectory=(Path="")
FullRebuild=False
ForDistribution=False
IncludeDebugFiles=True
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
UsePakFile=True
bGenerateChunks=True
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bBuildHttpChunkInstallData=False
HttpChunkInstallDataDirectory=(Path="")
HttpChunkInstallDataVersion=
IncludePrerequisites=True
IncludeAppLocalPrerequisites=False
bShareMaterialShaderCode=False
bSharedMaterialNativeLibraries=False
bDeterministicShaderCodeOrder=True
ApplocalPrerequisitesDirectory=(Path="")
IncludeCrashReporter=False
InternationalizationPreset=English
-CulturesToStage=en
+CulturesToStage=en
bCookAll=False
bCookMapsOnly=False
bTreatWarningsAsErrorsOnCook=False
bSkipEditorContent=False
+MapsToCook=(FilePath="/Game/Carla/Maps/Town10HD_Opt")
+MapsToCook=(FilePath="/Game/Carla/Maps/OpenDriveMap")
+MapsToCook=(FilePath="/Game/Carla/Maps/TestMaps/EmptyMap")
+MapsToCook=(FilePath="/Game/Carla/Maps/Mine_01")
+DirectoriesToAlwaysCook=(Path="/Game/Carla/Static/GenericMaterials/Licenseplates/Textures")
+DirectoriesToAlwaysCook=(Path="/Game/Carla/Static/Car/4Wheeled/ParkedVehicles")
+DirectoriesToAlwaysStageAsUFS=(Path="Carla/Maps/OpenDrive")
+DirectoriesToAlwaysStageAsUFS=(Path="Carla/Maps/Nav")
+DirectoriesToAlwaysStageAsUFS=(Path="Carla/Maps/TM")
+DirectoriesToAlwaysStageAsUFS=(Path="Carla/Config")
+DirectoriesToAlwaysStageAsUFS=(Path="Carla/Config/Mine_01")
bNativizeBlueprintAssets=False
bNativizeOnlySelectedBlueprints=False
