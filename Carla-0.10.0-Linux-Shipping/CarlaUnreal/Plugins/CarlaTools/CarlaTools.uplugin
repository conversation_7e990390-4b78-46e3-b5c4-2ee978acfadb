{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "CarlaT<PERSON><PERSON>", "Description": "This plugin provides engine tools for Carla", "Category": "Other", "CreatedBy": "Carla <PERSON>", "CreatedByURL": "http://carla.org", "DocsURL": "http://carla.readthedocs.io", "MarketplaceURL": "", "SupportURL": "https://github.com/carla-simulator/carla/issues", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": true, "Modules": [{"Name": "CarlaT<PERSON><PERSON>", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "<PERSON>", "Enabled": true}, {"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "ProceduralMeshComponent", "Enabled": true}, {"Name": "ChaosVehiclesPlugin", "Enabled": true}]}