#!/usr/bin/env python3
"""
调试CARLA连接和蓝图问题
"""

import carla
import random

def debug_carla_connection():
    """调试CARLA连接和可用资源"""
    print("调试CARLA连接...")
    
    try:
        # 连接CARLA
        client = carla.Client('localhost', 2000)
        client.set_timeout(10.0)
        world = client.get_world()
        print("✓ CARLA连接成功")
        
        # 获取蓝图库
        blueprint_library = world.get_blueprint_library()
        print(f"✓ 蓝图库加载成功，共有 {len(blueprint_library)} 个蓝图")
        
        # 检查车辆蓝图
        vehicle_blueprints = blueprint_library.filter('vehicle.*')
        print(f"✓ 找到 {len(vehicle_blueprints)} 个车辆蓝图")
        
        # 列出前10个车辆蓝图
        print("\n前10个车辆蓝图:")
        for i, bp in enumerate(list(vehicle_blueprints)[:10]):
            wheels = "未知"
            if bp.has_attribute('number_of_wheels'):
                wheels = bp.get_attribute('number_of_wheels')
            print(f"  {i+1}. {bp.id} (轮子数: {wheels})")
        
        # 检查Tesla Model 3
        tesla_blueprints = blueprint_library.filter('vehicle.tesla.model3')
        print(f"\n✓ Tesla Model 3 蓝图: {len(tesla_blueprints)} 个")
        for bp in tesla_blueprints:
            print(f"  - {bp.id}")
        
        # 检查四轮车辆
        four_wheel_vehicles = []
        for bp in vehicle_blueprints:
            if bp.has_attribute('number_of_wheels'):
                if int(bp.get_attribute('number_of_wheels')) == 4:
                    four_wheel_vehicles.append(bp)
        print(f"✓ 四轮车辆: {len(four_wheel_vehicles)} 个")
        
        # 检查生成点
        spawn_points = world.get_map().get_spawn_points()
        print(f"✓ 生成点: {len(spawn_points)} 个")
        
        if len(spawn_points) >= 2:
            # 测试选择两个生成点
            start_point, end_point = random.sample(spawn_points, 2)
            distance = ((start_point.location.x - end_point.location.x)**2 + 
                       (start_point.location.y - end_point.location.y)**2)**0.5
            print(f"✓ 示例生成点距离: {distance:.1f}m")
        
        # 测试车辆生成
        if len(four_wheel_vehicles) > 0:
            test_bp = random.choice(four_wheel_vehicles)
            print(f"\n测试生成车辆: {test_bp.id}")
            
            if len(spawn_points) > 0:
                test_spawn = random.choice(spawn_points)
                vehicle = world.try_spawn_actor(test_bp, test_spawn)
                if vehicle:
                    print("✓ 车辆生成成功")
                    vehicle.destroy()
                    print("✓ 车辆清理成功")
                else:
                    print("❌ 车辆生成失败")
        
        print("\n🎉 CARLA调试完成，所有检查通过！")
        return True
        
    except Exception as e:
        print(f"❌ CARLA调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("CARLA调试工具")
    print("=" * 50)
    debug_carla_connection()
