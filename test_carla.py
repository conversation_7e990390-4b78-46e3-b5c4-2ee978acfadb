import carla
import time

try:
    # 连接到 CARLA 服务器
    client = carla.Client('localhost', 2000)
    client.set_timeout(10.0)
    
    # 获取世界
    world = client.get_world()
    print(f"成功连接到 CARLA！")
    print(f"当前地图：{world.get_map().name}")
    
    # 获取可用的生成点
    spawn_points = world.get_map().get_spawn_points()
    print(f"可用生成点数量: {len(spawn_points)}")
    
    # 获取一些基本信息
    settings = world.get_settings()
    print(f"当前仿真步长: {settings.fixed_delta_seconds if settings.fixed_delta_seconds else '可变'} 秒")
    print(f"同步模式: {'开启' if settings.synchronous_mode else '关闭'}")
    
except Exception as e:
    print(f"连接错误: {e}") 