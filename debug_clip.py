#!/usr/bin/env python3
"""
调试np.clip的数据类型行为
"""

import numpy as np

def test_clip_behavior():
    """测试np.clip的数据类型行为"""
    print("测试np.clip的数据类型行为...")
    
    # 测试不同输入类型
    inputs = [
        np.float32(0.5),
        np.float64(0.5),
        0.5,  # Python float
        np.array(0.5, dtype=np.float32),
        np.array(0.5, dtype=np.float64),
    ]
    
    low, high = -1, 1
    
    for i, value in enumerate(inputs):
        print(f"\n测试 {i+1}:")
        print(f"  输入: {value}, 类型: {type(value)}, dtype: {getattr(value, 'dtype', 'N/A')}")
        
        clipped = np.clip(value, low, high)
        print(f"  输出: {clipped}, 类型: {type(clipped)}, dtype: {clipped.dtype}")
        
        # 测试强制转换为float32
        clipped_f32 = np.clip(value, low, high).astype(np.float32)
        print(f"  强制f32: {clipped_f32}, 类型: {type(clipped_f32)}, dtype: {clipped_f32.dtype}")

if __name__ == "__main__":
    test_clip_behavior()
