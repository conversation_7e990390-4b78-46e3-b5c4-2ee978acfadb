<mujoco model="quadruped">

  <visual>
    <quality shadowsize="2048"/>
    <rgba rangefinder="1 1 0.1 0.1"/>
  </visual>

  <asset>
    <texture name="grid" type="2d" builtin="checker" rgb1=".1 .2 .3" rgb2=".2 .3 .4" width="300" height="300" mark="edge" markrgb=".2 .3 .4"/>
    <material name="grid" texture="grid" texrepeat="1 1" texuniform="true" reflectance=".2"/>
    <material name="self" rgba=".7 .5 .3 1"/>
    <material name="self_default" rgba=".7 .5 .3 1"/>
    <material name="self_highlight" rgba="0 .5 .3 1"/>
    <material name="effector" rgba=".7 .4 .2 1"/>
    <material name="effector_default" rgba=".7 .4 .2 1"/>
    <material name="effector_highlight" rgba="0 .5 .3 1"/>
    <material name="decoration" rgba=".3 .5 .7 1"/>
    <material name="eye" rgba="0 .2 1 1"/>
    <material name="target" rgba=".6 .3 .3 1"/>
    <material name="target_default" rgba=".6 .3 .3 1"/>
    <material name="target_highlight" rgba=".6 .3 .3 .4"/>
    <material name="site" rgba=".5 .5 .5 .3"/>

    <hfield name="terrain" ncol="201" nrow="201" size="30 30 5 .1"/>
  </asset>

  <option timestep=".005"/>

  <default>
    <geom solimp=".9 .99 .003" solref=".01 1"/>
    <default class="body">
      <geom  type="capsule" size=".08" condim="1" material="self" density="500"/>
      <joint type="hinge" damping="30" armature=".01"
             limited="true" solimplimit="0 .99 .01"/>
      <default class="hip">
        <default class="yaw">
          <joint axis="0 0 1" range="-50 50"/>
        </default>
        <default class="pitch">
          <joint axis="0 1 0" range="-20 60"/>
        </default>
        <geom fromto="0 0 0 .3 0 .11"/>
      </default>
      <default class="knee">
        <joint axis="0 1 0" range="-60 50"/>
        <geom size=".065" fromto="0 0 0 .25 0 -.25"/>
      </default>
      <default class="ankle">
        <joint axis="0 1 0" range="-45 55"/>
        <geom size=".055" fromto="0 0 0 0 0 -.25"/>
      </default>
      <default class="toe">
        <geom type="sphere" size=".08" material="effector" friction="1.5"/>
        <site type="sphere" size=".084" material="site"  group="4"/>
      </default>
    </default>
    <default class="rangefinder">
      <site type="capsule" size=".005 .1" material="site" group="4"/>
    </default>

    <default class="coupling">
      <equality solimp="0.95 0.99 0.01" solref=".005 .5"/>
    </default>

    <general ctrllimited="true" gainprm="1000" biasprm="0 -1000" biastype="affine" dyntype="filter" dynprm=".1"/>
    <default class="yaw_act">
      <general ctrlrange="-1 1"/>
    </default>
    <default class="lift_act">
      <general ctrlrange="-1 1.1"/>
    </default>
    <default class="extend_act">
      <general ctrlrange="-.8 .8"/>
    </default>
  </default>

  <worldbody>
    <camera name="sideon" pos="0 -10 5" fovy="45" mode="targetbody" target="torso" />
    <camera name="float_far"  pos="-4 0 2" xyaxes="0 -1 0 .5 0 1" mode="trackcom" fovy="90"/>
    <body name="torso" childclass="body" pos="0 0 .57">

      <camera name="x"  pos="-1.7 0 1" xyaxes="0 -1 0 .75 0 1" mode="trackcom"/>
      <camera name="y"  pos="0 4 2" xyaxes="-1 0 0 0 -.5 1" mode="trackcom"/>
      <camera name="egocentric"  pos=".3 0 .11" xyaxes="0 -1 0 .4 0 1" fovy="60"/>
      <light name="light" pos="0 0 4" mode="trackcom"/>

      <geom name="eye_r" type="cylinder" size=".05"  fromto=".1 -.07 .12 .31 -.07 .08" mass="0"/>
      <site name="pupil_r" type="sphere" size=".033"  pos=".3 -.07 .08" zaxis="1 0 0" material="eye"/>
      <geom name="eye_l" type="cylinder" size=".05"  fromto=".1 .07 .12 .31 .07 .08" mass="0"/>
      <site name="pupil_l" type="sphere" size=".033"  pos=".3 .07 .08" zaxis="1 0 0" material="eye"/>
      <site name="workspace" type="sphere" size=".3 .3 .3"  material="site" pos=".8 0 -.2" group="3"/>

      <site name="rf_00" class="rangefinder" fromto=".41 -.02  .11 .34 0 .115"/>
      <site name="rf_01" class="rangefinder" fromto=".41 -.01  .11 .34 0 .115"/>
      <site name="rf_02" class="rangefinder" fromto=".41   0   .11 .34 0 .115"/>
      <site name="rf_03" class="rangefinder" fromto=".41  .01  .11 .34 0 .115"/>
      <site name="rf_04" class="rangefinder" fromto=".41  .02  .11 .34 0 .115"/>
      <site name="rf_10" class="rangefinder" fromto=".41 -.02  .1  .36 0 .11"/>
      <site name="rf_11" class="rangefinder" fromto=".41 -.02  .1  .36 0 .11"/>
      <site name="rf_12" class="rangefinder" fromto=".41   0   .1  .36 0 .11"/>
      <site name="rf_13" class="rangefinder" fromto=".41  .01  .1  .36 0 .11"/>
      <site name="rf_14" class="rangefinder" fromto=".41  .02  .1  .36 0 .11"/>
      <site name="rf_20" class="rangefinder" fromto=".41 -.02  .09 .38 0 .105"/>
      <site name="rf_21" class="rangefinder" fromto=".41 -.01  .09 .38 0 .105"/>
      <site name="rf_22" class="rangefinder" fromto=".41   0   .09 .38 0 .105"/>
      <site name="rf_23" class="rangefinder" fromto=".41  .01  .09 .38 0 .105"/>
      <site name="rf_24" class="rangefinder" fromto=".41  .02  .09 .38 0 .105"/>
      <site name="rf_30" class="rangefinder" fromto=".41 -.02  .08 .4  0 .1"/>
      <site name="rf_31" class="rangefinder" fromto=".41 -.01  .08 .4  0 .1"/>
      <site name="rf_32" class="rangefinder" fromto=".41   0   .08 .4  0 .1"/>
      <site name="rf_33" class="rangefinder" fromto=".41  .01  .08 .4  0 .1"/>
      <site name="rf_34" class="rangefinder" fromto=".41  .02  .08 .4  0 .1"/>

      <geom name="torso" type="ellipsoid" size=".3 .27 .2" density="1000"/>
      <site name="torso_touch" type="box" size=".26 .26 .26" rgba="0 0 1 0"/>
      <site name="torso" size=".05" rgba="1 0 0 1" />

      <body name="hip_front_left" pos=".2 .2 0" euler="0 0 45" childclass="hip">
        <joint name="yaw_front_left" class="yaw"/>
        <joint name="pitch_front_left" class="pitch"/>
        <geom name="thigh_front_left"/>
        <body name="knee_front_left" pos=".3 0 .11" childclass="knee">
          <joint name="knee_front_left"/>
          <geom name="shin_front_left"/>
          <body name="ankle_front_left" pos=".25 0 -.25" childclass="ankle">
            <joint name="ankle_front_left"/>
            <geom name="foot_front_left"/>
            <body name="toe_front_left" pos="0 0 -.3" childclass="toe">
              <geom name="toe_front_left"/>
              <site name="toe_front_left"/>
            </body>
          </body>
        </body>
      </body>

      <body name="hip_front_right" pos=".2 -.2 0" euler="0 0 -45" childclass="hip">
        <joint name="yaw_front_right" class="yaw"/>
        <joint name="pitch_front_right" class="pitch"/>
        <geom name="thigh_front_right"/>
        <body name="knee_front_right" pos=".3 0 .11" childclass="knee">
          <joint name="knee_front_right"/>
          <geom name="shin_front_right"/>
          <body name="ankle_front_right" pos=".25 0 -.25" childclass="ankle">
            <joint name="ankle_front_right"/>
            <geom name="foot_front_right"/>
            <body name="toe_front_right" pos="0 0 -.3" childclass="toe">
              <geom name="toe_front_right"/>
              <site name="toe_front_right"/>
            </body>
          </body>
        </body>
      </body>

      <body name="hip_back_right" pos="-.2 -.2 0" euler="0 0 -135" childclass="hip">
        <joint name="yaw_back_right" class="yaw"/>
        <joint name="pitch_back_right" class="pitch"/>
        <geom name="thigh_back_right"/>
        <body name="knee_back_right" pos=".3 0 .11" childclass="knee">
          <joint name="knee_back_right"/>
          <geom name="shin_back_right"/>
          <body name="ankle_back_right" pos=".25 0 -.25" childclass="ankle">
            <joint name="ankle_back_right"/>
            <geom name="foot_back_right"/>
            <body name="toe_back_right" pos="0 0 -.3" childclass="toe">
              <geom name="toe_back_right"/>
              <site name="toe_back_right"/>
            </body>
          </body>
        </body>
      </body>

      <body name="hip_back_left" pos="-.2 .2 0" euler="0 0 135" childclass="hip">
        <joint name="yaw_back_left" class="yaw"/>
        <joint name="pitch_back_left" class="pitch"/>
        <geom name="thigh_back_left"/>
        <body name="knee_back_left" pos=".3 0 .11" childclass="knee">
          <joint name="knee_back_left"/>
          <geom name="shin_back_left"/>
          <body name="ankle_back_left" pos=".25 0 -.25" childclass="ankle">
            <joint name="ankle_back_left"/>
            <geom name="foot_back_left"/>
            <body name="toe_back_left" pos="0 0 -.3" childclass="toe">
              <geom name="toe_back_left"/>
              <site name="toe_back_left"/>
            </body>
          </body>
        </body>
      </body>
    </body>
  </worldbody>

  <tendon>
    <fixed name="coupling_front_left">
      <joint joint="pitch_front_left"      coef=".333"/>
      <joint joint="knee_front_left"       coef=".333"/>
      <joint joint="ankle_front_left"      coef=".333"/>
    </fixed>
    <fixed name="coupling_front_right">
      <joint joint="pitch_front_right"      coef=".333"/>
      <joint joint="knee_front_right"       coef=".333"/>
      <joint joint="ankle_front_right"      coef=".333"/>
    </fixed>
    <fixed name="coupling_back_right">
      <joint joint="pitch_back_right"      coef=".333"/>
      <joint joint="knee_back_right"       coef=".333"/>
      <joint joint="ankle_back_right"      coef=".333"/>
    </fixed>
    <fixed name="coupling_back_left">
      <joint joint="pitch_back_left"      coef=".333"/>
      <joint joint="knee_back_left"       coef=".333"/>
      <joint joint="ankle_back_left"      coef=".333"/>
    </fixed>

    <fixed name="extend_front_left">
      <joint joint="pitch_front_left"      coef=".25"/>
      <joint joint="knee_front_left"       coef="-.5"/>
      <joint joint="ankle_front_left"      coef=".25"/>
    </fixed>
    <fixed name="lift_front_left">
      <joint joint="pitch_front_left"      coef=".5"/>
      <joint joint="ankle_front_left"      coef="-.5"/>
    </fixed>

    <fixed name="extend_front_right">
      <joint joint="pitch_front_right"     coef=".25"/>
      <joint joint="knee_front_right"      coef="-.5"/>
      <joint joint="ankle_front_right"     coef=".25"/>
    </fixed>
    <fixed name="lift_front_right">
      <joint joint="pitch_front_right"     coef=".5"/>
      <joint joint="ankle_front_right"     coef="-.5"/>
    </fixed>

    <fixed name="extend_back_right">
      <joint joint="pitch_back_right"     coef=".25"/>
      <joint joint="knee_back_right"      coef="-.5"/>
      <joint joint="ankle_back_right"     coef=".25"/>
    </fixed>
    <fixed name="lift_back_right">
      <joint joint="pitch_back_right"     coef=".5"/>
      <joint joint="ankle_back_right"     coef="-.5"/>
    </fixed>

    <fixed name="extend_back_left">
      <joint joint="pitch_back_left"      coef=".25"/>
      <joint joint="knee_back_left"       coef="-.5"/>
      <joint joint="ankle_back_left"      coef=".25"/>
    </fixed>
    <fixed name="lift_back_left">
      <joint joint="pitch_back_left"     coef=".5"/>
      <joint joint="ankle_back_left"     coef="-.5"/>
    </fixed>
  </tendon>

  <equality>
    <tendon name="coupling_front_left" tendon1="coupling_front_left" class="coupling"/>
    <tendon name="coupling_front_right" tendon1="coupling_front_right" class="coupling"/>
    <tendon name="coupling_back_right" tendon1="coupling_back_right" class="coupling"/>
    <tendon name="coupling_back_left" tendon1="coupling_back_left" class="coupling"/>
  </equality>

  <actuator>
    <general name="yaw_front_left" class="yaw_act" joint="yaw_front_left"/>
    <general name="lift_front_left" class="lift_act" tendon="lift_front_left"/>
    <general name="extend_front_left" class="extend_act" tendon="extend_front_left"/>
    <general name="yaw_front_right" class="yaw_act" joint="yaw_front_right"/>
    <general name="lift_front_right" class="lift_act" tendon="lift_front_right"/>
    <general name="extend_front_right" class="extend_act" tendon="extend_front_right"/>
    <general name="yaw_back_right" class="yaw_act" joint="yaw_back_right"/>
    <general name="lift_back_right" class="lift_act" tendon="lift_back_right"/>
    <general name="extend_back_right" class="extend_act" tendon="extend_back_right"/>
    <general name="yaw_back_left" class="yaw_act" joint="yaw_back_left"/>
    <general name="lift_back_left" class="lift_act" tendon="lift_back_left"/>
    <general name="extend_back_left" class="extend_act" tendon="extend_back_left"/>
  </actuator>

  <sensor>
    <accelerometer name="imu_accel" site="torso"/>
    <gyro name="imu_gyro" site="torso"/>
    <velocimeter name="velocimeter" site="torso"/>
    <force name="force_toe_front_left" site="toe_front_left"/>
    <force name="force_toe_front_right" site="toe_front_right"/>
    <force name="force_toe_back_right" site="toe_back_right"/>
    <force name="force_toe_back_left" site="toe_back_left"/>
    <torque name="torque_toe_front_left" site="toe_front_left"/>
    <torque name="torque_toe_front_right" site="toe_front_right"/>
    <torque name="torque_toe_back_right" site="toe_back_right"/>
    <torque name="torque_toe_back_left" site="toe_back_left"/>
    <subtreecom name="center_of_mass" body="torso"/>
    <rangefinder name="rf_00" site="rf_00"/>
    <rangefinder name="rf_01" site="rf_01"/>
    <rangefinder name="rf_02" site="rf_02"/>
    <rangefinder name="rf_03" site="rf_03"/>
    <rangefinder name="rf_04" site="rf_04"/>
    <rangefinder name="rf_10" site="rf_10"/>
    <rangefinder name="rf_11" site="rf_11"/>
    <rangefinder name="rf_12" site="rf_12"/>
    <rangefinder name="rf_13" site="rf_13"/>
    <rangefinder name="rf_14" site="rf_14"/>
    <rangefinder name="rf_20" site="rf_20"/>
    <rangefinder name="rf_21" site="rf_21"/>
    <rangefinder name="rf_22" site="rf_22"/>
    <rangefinder name="rf_23" site="rf_23"/>
    <rangefinder name="rf_24" site="rf_24"/>
    <rangefinder name="rf_30" site="rf_30"/>
    <rangefinder name="rf_31" site="rf_31"/>
    <rangefinder name="rf_32" site="rf_32"/>
    <rangefinder name="rf_33" site="rf_33"/>
    <rangefinder name="rf_34" site="rf_34"/>
  </sensor>
</mujoco>
