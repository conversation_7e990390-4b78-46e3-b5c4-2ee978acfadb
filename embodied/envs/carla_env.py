'''
观测空间为：
前视摄像头图像(RGB,shape=(84,84,3))
当前速度(float32, shape=(1,))
动作空间为：
方向盘 steer(float32, 范围[-1, 1])
油门 throttle(float32, 范围[0, 1])
刹车 brake(float32, 范围[0, 1])
重置 reset(bool)
奖励设计为：
到目标点距离的负变化量（越接近目标奖励越高）
到达目标点有额外奖励
碰撞有惩罚
'''

import numpy as np
import embodied
from elements import Space
import carla

class CarlaEnv(embodied.Env):
    def __init__(self, task, size=(84, 84), host='localhost', port=2000, max_steps=1000, **kwargs):
        self.client = carla.Client(host, port)
        self.client.set_timeout(10.0)
        self.world = self.client.get_world()
        
        # 开启同步模式
        self.settings = self.world.get_settings()
        self.settings.synchronous_mode = True
        self.settings.fixed_delta_seconds = 0.05  # 20 FPS
        self.world.apply_settings(self.settings)

        self.blueprint_library = self.world.get_blueprint_library()
        self.size = size
        self.max_steps = max_steps
        
        # 内部状态
        self.actors = []
        self.vehicle = None
        self.camera = None
        self.image = None
        self.collision = None
        self.step_count = 0
        self.last_dist = 0
        self.target_location = None
        self._is_first = True

    @property
    def obs_space(self):
        return {
            'image': Space(np.uint8, self.size + (3,)),
            'speed': Space(np.float32, (1,)),
            'reward': Space(np.float32),
            'is_first': Space(bool),
            'is_last': Space(bool),
            'is_terminal': Space(bool),
        }

    @property
    def act_space(self):
        return {
            'steer': Space(np.float32, (), -1, 1),
            'throttle': Space(np.float32, (), 0, 1),
            'brake': Space(np.float32, (), 0, 1),
            'reset': Space(bool),
        }

    def _to_float32(self, x):
        if isinstance(x, dict):
            return {k: self._to_float32(v) for k, v in x.items()}
        elif isinstance(x, np.ndarray) and x.dtype == np.float64:
            return x.astype(np.float32)
        elif isinstance(x, float) or (isinstance(x, np.floating) and getattr(x, 'dtype', None) == np.float64):
            return np.float32(x)
        else:
            return x

    def step(self, action):
        action = self._to_float32(action)
        # 强制所有动作转为 float32
        for k in ['steer', 'throttle', 'brake']:
            if k in action:
                action[k] = np.float32(action[k])
        if self._is_first or action['reset']:
            return self._reset()

        # 执行动作
        control = self.vehicle.get_control()
        control.steer = float(np.clip(action['steer'], -1, 1))
        control.throttle = float(np.clip(action['throttle'], 0, 1))
        control.brake = float(np.clip(action['brake'], 0, 1))
        self.vehicle.apply_control(control)

        # 驱动仿真
        self.world.tick()
        self.step_count += 1

        obs = self._get_obs()
        reward, is_terminal = self._get_reward_and_done()
        is_last = self.step_count >= self.max_steps or is_terminal
        
        obs.update({
            'reward': np.float32(reward),
            'is_first': False,
            'is_last': is_last,
            'is_terminal': is_terminal,
        })
        # 再次强制 float32，防止 wrapper 重新生成 float64
        for k in ['steer', 'throttle', 'brake']:
            if k in action:
                action[k] = np.float32(action[k])
        return obs

    def _reset(self):
        self._cleanup()
        self._setup_vehicle_and_sensors()
        self.step_count = 0
        self._is_first = False
        
        self.world.tick() # Tick to get the first observation
        obs = self._get_obs()
        self.last_dist = self._get_distance_to_target()

        obs.update({
            'reward': 0.0,
            'is_first': True,
            'is_last': False,
            'is_terminal': False,
        })
        return obs

    def _get_obs(self):
        while self.image is None:
            self.world.tick() # Wait for camera image
        
        velocity = self.vehicle.get_velocity()
        speed = np.sqrt(velocity.x**2 + velocity.y**2 + velocity.z**2)
        
        obs = {
            'image': self.image.copy(),
            'speed': np.array([speed], dtype=np.float32),
        }
        self.image = None # Consume image
        return obs

    def _get_reward_and_done(self):
        dist = self._get_distance_to_target()
        
        # 奖励: 接近目标的距离变化
        reward = self.last_dist - dist
        self.last_dist = dist
        
        done = False
        # 到达目标点
        if dist < 5.0:
            reward += 100.0
            done = True
        
        # 碰撞惩罚
        if self.collision:
            reward -= 20.0
            done = True # Episode ends on collision
            self.collision = None
        
        return reward, done

    def _get_distance_to_target(self):
        location = self.vehicle.get_location()
        return np.linalg.norm(
            np.array([location.x, location.y]) -
            np.array([self.target_location.x, self.target_location.y])
        )

    def _setup_vehicle_and_sensors(self):
        spawn_points = self.world.get_map().get_spawn_points()
        
        # 确保起点和终点不相同
        start_point = None
        self.target_location = None
        while start_point is None or start_point.location == self.target_location.location:
            start_point, self.target_location = np.random.choice(spawn_points, 2, replace=False)

        self.target_location = self.target_location.location

        # 创建车辆
        vehicle_bp = self.blueprint_library.find('vehicle.tesla.model3')
        self.vehicle = self.world.try_spawn_actor(vehicle_bp, start_point)
        if not self.vehicle:
            # 如果生成失败，尝试其他车辆
            vehicle_bp = np.random.choice(self.blueprint_library.filter('vehicle.*'))
            self.vehicle = self.world.spawn_actor(vehicle_bp, start_point)
        self.actors.append(self.vehicle)

        # 创建摄像头
        camera_bp = self.blueprint_library.find('sensor.camera.rgb')
        camera_bp.set_attribute('image_size_x', str(self.size[0]))
        camera_bp.set_attribute('image_size_y', str(self.size[1]))
        camera_transform = carla.Transform(carla.Location(x=1.5, z=2.4))
        self.camera = self.world.spawn_actor(camera_bp, camera_transform, attach_to=self.vehicle)
        self.camera.listen(lambda image: self._on_image(image))
        self.actors.append(self.camera)

        # 创建碰撞传感器
        collision_bp = self.blueprint_library.find('sensor.other.collision')
        self.collision_sensor = self.world.spawn_actor(collision_bp, carla.Transform(), attach_to=self.vehicle)
        self.collision_sensor.listen(lambda event: self._on_collision(event))
        self.actors.append(self.collision_sensor)
        
    def _on_image(self, image):
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))[:, :, :3]
        self.image = array

    def _on_collision(self, event):
        self.collision = event

    def _cleanup(self):
        # 停止所有传感器监听
        if self.camera and self.camera.is_listening:
            self.camera.stop()
        if hasattr(self, 'collision_sensor') and self.collision_sensor.is_listening:
            self.collision_sensor.stop()

        # 销毁所有actor
        for actor in self.actors:
            if actor is not None and actor.is_alive:
                actor.destroy()
        self.actors = []
        self.vehicle = None
        self.camera = None
        self.collision_sensor = None
        self.image = None
        self.collision = None
        
    def close(self):
        self._cleanup()
        # 恢复异步模式
        self.settings.synchronous_mode = False
        self.settings.fixed_delta_seconds = None
        self.world.apply_settings(self.settings)