import numpy as np
import embodied
from elements import Space
import carla
import math
import random

class CarlaEnv(embodied.Env):
    def __init__(self, task, size=(84, 84), host='localhost', port=2000, max_steps=1000,
                 min_distance=50.0, max_distance=200.0, **kwargs):
        self.client = carla.Client(host, port)
        self.client.set_timeout(10.0)
        self.world = self.client.get_world()

        # 保存原始设置以便恢复
        self.original_settings = self.world.get_settings()
        self.settings = self.world.get_settings()
        self.settings.synchronous_mode = True
        self.settings.fixed_delta_seconds = 0.05
        self.world.apply_settings(self.settings)

        self.blueprint_library = self.world.get_blueprint_library()
        self.size = size
        self.max_steps = max_steps
        self.min_distance = min_distance  # 最小起点终点距离
        self.max_distance = max_distance  # 最大起点终点距离

        # 车辆和传感器
        self.actors = []
        self.vehicle = None
        self.camera = None
        self.collision_sensor = None

        # 状态变量
        self.image = None
        self.collision = None
        self.step_count = 0
        self.last_dist = 0
        self.target_location = None
        self.start_location = None
        self.initial_distance = 0
        self._is_first = True

        # 性能统计
        self.success_count = 0
        self.total_episodes = 0

    @property
    def obs_space(self):
        return {
            'image': Space(np.uint8, self.size + (3,)),
            'speed': Space(np.float32, (1,)),
            'distance_to_target': Space(np.float32, (1,)),  # 到目标的距离
            'target_direction': Space(np.float32, (2,)),    # 目标方向向量(x,y)
            'vehicle_heading': Space(np.float32, (1,)),     # 车辆朝向角度
            'progress': Space(np.float32, (1,)),            # 任务完成进度
            'reward': Space(np.float32),
            'is_first': Space(bool),
            'is_last': Space(bool),
            'is_terminal': Space(bool),
        }

    @property
    def act_space(self):
        return {
            'steer': Space(np.float32, (), -1, 1),
            'throttle': Space(np.float32, (), 0, 1),
            'brake': Space(np.float32, (), 0, 1),
            'reset': Space(bool),
        }

    def _to_float32(self, x):
        if isinstance(x, dict):
            return {k: self._to_float32(v) for k, v in x.items()}
        elif isinstance(x, np.ndarray) and x.dtype == np.float64:
            return x.astype(np.float32)
        elif isinstance(x, float) or (isinstance(x, np.floating) and getattr(x, 'dtype', None) == np.float64):
            return np.float32(x)
        else:
            return x

    def step(self, action):
        action = self._to_float32(action)
        for k in ['steer', 'throttle', 'brake']:
            action[k] = np.float32(np.clip(action[k], self.act_space[k].low, self.act_space[k].high))

        if self._is_first or action['reset']:
            return self._reset()

        control = carla.VehicleControl(
            steer=float(action['steer']),
            throttle=float(action['throttle']),
            brake=float(action['brake'])
        )
        self.vehicle.apply_control(control)

        self.world.tick()
        self.step_count += 1

        obs = self._get_obs()
        reward, is_terminal = self._get_reward_and_done()
        is_last = self.step_count >= self.max_steps or is_terminal

        obs.update({
            'reward': np.float32(reward),
            'is_first': False,
            'is_last': is_last,
            'is_terminal': is_terminal,
        })
        return obs

    def _reset(self):
        self._cleanup()
        self._setup_vehicle_and_sensors()
        self.step_count = 0
        self._is_first = False

        self.world.tick()
        obs = self._get_obs()
        self.last_dist = self._get_distance_to_target()

        obs.update({
            'reward': np.float32(0.0),
            'is_first': True,
            'is_last': False,
            'is_terminal': False,
        })
        return obs

    def _get_obs(self):
        while self.image is None:
            self.world.tick()

        # 获取车辆状态
        velocity = self.vehicle.get_velocity()
        speed = math.sqrt(velocity.x**2 + velocity.y**2 + velocity.z**2)

        # 获取位置和朝向
        transform = self.vehicle.get_transform()
        location = transform.location
        rotation = transform.rotation

        # 计算到目标的距离和方向
        distance_to_target = self._get_distance_to_target()
        target_direction = self._get_target_direction()

        # 计算任务进度
        progress = max(0.0, 1.0 - distance_to_target / self.initial_distance)

        obs = {
            'image': self.image.copy(),
            'speed': np.array([speed], dtype=np.float32),
            'distance_to_target': np.array([distance_to_target], dtype=np.float32),
            'target_direction': target_direction.astype(np.float32),
            'vehicle_heading': np.array([math.radians(rotation.yaw)], dtype=np.float32),
            'progress': np.array([progress], dtype=np.float32),
        }
        self.image = None
        return obs

    def _get_reward_and_done(self):
        dist = self._get_distance_to_target()

        # 基础距离奖励 - 鼓励接近目标
        distance_reward = (self.last_dist - dist) * 0.1

        # 速度奖励 - 鼓励合理速度
        velocity = self.vehicle.get_velocity()
        speed = math.sqrt(velocity.x**2 + velocity.y**2 + velocity.z**2)
        speed_reward = min(speed / 10.0, 1.0) * 0.01  # 鼓励适中速度

        # 方向奖励 - 鼓励朝向目标
        direction_reward = self._get_direction_reward() * 0.02

        # 总奖励
        reward = distance_reward + speed_reward + direction_reward - 0.01  # 时间惩罚

        done = False

        # 成功到达目标
        if dist < 5.0:
            reward += 100.0
            done = True
            self.success_count += 1
            print(f"成功到达目标！距离: {dist:.2f}m, 成功率: {self.success_count}/{self.total_episodes}")

        # 碰撞惩罚
        if self.collision:
            reward -= 50.0
            done = True
            self.collision = None
            print(f"发生碰撞！")

        # 超时或偏离太远
        if dist > self.initial_distance * 1.5:
            reward -= 10.0
            done = True
            print(f"偏离目标太远！当前距离: {dist:.2f}m")

        self.last_dist = dist
        return reward, done

    def _get_distance_to_target(self):
        location = self.vehicle.get_location()
        return math.sqrt(
            (location.x - self.target_location.x)**2 +
            (location.y - self.target_location.y)**2
        )

    def _get_target_direction(self):
        """获取从当前位置到目标的单位方向向量"""
        location = self.vehicle.get_location()
        dx = self.target_location.x - location.x
        dy = self.target_location.y - location.y
        distance = math.sqrt(dx**2 + dy**2)
        if distance > 0:
            return np.array([dx / distance, dy / distance])
        else:
            return np.array([0.0, 0.0])

    def _get_direction_reward(self):
        """计算方向奖励 - 车辆朝向与目标方向的一致性"""
        # 获取车辆朝向
        transform = self.vehicle.get_transform()
        yaw = math.radians(transform.rotation.yaw)
        vehicle_direction = np.array([math.cos(yaw), math.sin(yaw)])

        # 获取目标方向
        target_direction = self._get_target_direction()

        # 计算方向一致性 (点积)
        if np.linalg.norm(target_direction) > 0:
            dot_product = np.dot(vehicle_direction, target_direction)
            return max(0.0, dot_product)  # 只奖励正确方向
        return 0.0

    def _setup_vehicle_and_sensors(self):
        spawn_points = self.world.get_map().get_spawn_points()
        while True:
            start_point, end_point = np.random.choice(spawn_points, 2, replace=False)
            if start_point.location != end_point.location:
                break
        self.target_location = end_point.location

        vehicle_bp = self.blueprint_library.find('vehicle.tesla.model3')
        self.vehicle = self.world.try_spawn_actor(vehicle_bp, start_point)
        if not self.vehicle:
            vehicle_bp = np.random.choice(self.blueprint_library.filter('vehicle.*'))
            self.vehicle = self.world.spawn_actor(vehicle_bp, start_point)
        self.actors.append(self.vehicle)

        camera_bp = self.blueprint_library.find('sensor.camera.rgb')
        camera_bp.set_attribute('image_size_x', str(self.size[0]))
        camera_bp.set_attribute('image_size_y', str(self.size[1]))
        camera_transform = carla.Transform(carla.Location(x=1.5, z=2.4))
        self.camera = self.world.spawn_actor(camera_bp, camera_transform, attach_to=self.vehicle)
        self.camera.listen(lambda image: self._on_image(image))
        self.actors.append(self.camera)

        collision_bp = self.blueprint_library.find('sensor.other.collision')
        self.collision_sensor = self.world.spawn_actor(collision_bp, carla.Transform(), attach_to=self.vehicle)
        self.collision_sensor.listen(lambda event: self._on_collision(event))
        self.actors.append(self.collision_sensor)

    def _on_image(self, image):
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))[:, :, :3]
        self.image = array

    def _on_collision(self, event):
        self.collision = event

    def _cleanup(self):
        if self.camera and self.camera.is_listening:
            self.camera.stop()
        if hasattr(self, 'collision_sensor') and self.collision_sensor.is_listening:
            self.collision_sensor.stop()
        for actor in self.actors:
            if actor and actor.is_alive:
                actor.destroy()
        self.actors = []
        self.vehicle = None
        self.camera = None
        self.collision_sensor = None
        self.image = None
        self.collision = None

    def close(self):
        self._cleanup()
        self.settings.synchronous_mode = False
        self.settings.fixed_delta_seconds = None
        self.world.apply_settings(self.settings)
