import numpy as np
import embodied
from elements import Space
import carla

class CarlaEnv(embodied.Env):
    def __init__(self, task, size=(84, 84), host='localhost', port=2000, max_steps=1000, **kwargs):
        self.client = carla.Client(host, port)
        self.client.set_timeout(10.0)
        self.world = self.client.get_world()

        self.settings = self.world.get_settings()
        self.settings.synchronous_mode = True
        self.settings.fixed_delta_seconds = 0.05
        self.world.apply_settings(self.settings)

        self.blueprint_library = self.world.get_blueprint_library()
        self.size = size
        self.max_steps = max_steps

        self.actors = []
        self.vehicle = None
        self.camera = None
        self.image = None
        self.collision = None
        self.step_count = 0
        self.last_dist = 0
        self.target_location = None
        self._is_first = True

    @property
    def obs_space(self):
        return {
            'image': Space(np.uint8, self.size + (3,)),
            'speed': Space(np.float32, (1,)),
            'reward': Space(np.float32),
            'is_first': Space(bool),
            'is_last': Space(bool),
            'is_terminal': Space(bool),
        }

    @property
    def act_space(self):
        return {
            'steer': Space(np.float32, (), -1, 1),
            'throttle': Space(np.float32, (), 0, 1),
            'brake': Space(np.float32, (), 0, 1),
            'reset': Space(bool),
        }

    def _to_float32(self, x):
        if isinstance(x, dict):
            return {k: self._to_float32(v) for k, v in x.items()}
        elif isinstance(x, np.ndarray) and x.dtype == np.float64:
            return x.astype(np.float32)
        elif isinstance(x, float) or (isinstance(x, np.floating) and getattr(x, 'dtype', None) == np.float64):
            return np.float32(x)
        else:
            return x

    def step(self, action):
        action = self._to_float32(action)
        for k in ['steer', 'throttle', 'brake']:
            action[k] = np.float32(np.clip(action[k], self.act_space[k].low, self.act_space[k].high))

        if self._is_first or action['reset']:
            return self._reset()

        control = carla.VehicleControl(
            steer=float(action['steer']),
            throttle=float(action['throttle']),
            brake=float(action['brake'])
        )
        self.vehicle.apply_control(control)

        self.world.tick()
        self.step_count += 1

        obs = self._get_obs()
        reward, is_terminal = self._get_reward_and_done()
        is_last = self.step_count >= self.max_steps or is_terminal

        obs.update({
            'reward': np.float32(reward),
            'is_first': False,
            'is_last': is_last,
            'is_terminal': is_terminal,
        })
        return obs

    def _reset(self):
        self._cleanup()
        self._setup_vehicle_and_sensors()
        self.step_count = 0
        self._is_first = False

        self.world.tick()
        obs = self._get_obs()
        self.last_dist = self._get_distance_to_target()

        obs.update({
            'reward': np.float32(0.0),
            'is_first': True,
            'is_last': False,
            'is_terminal': False,
        })
        return obs

    def _get_obs(self):
        while self.image is None:
            self.world.tick()
        velocity = self.vehicle.get_velocity()
        speed = np.sqrt(velocity.x**2 + velocity.y**2 + velocity.z**2)
        obs = {
            'image': self.image.copy(),
            'speed': np.array([speed], dtype=np.float32),
        }
        self.image = None
        return obs

    def _get_reward_and_done(self):
        dist = self._get_distance_to_target()
        reward = self.last_dist - dist
        self.last_dist = dist

        done = False
        if dist < 5.0:
            reward += 100.0
            done = True

        if self.collision:
            reward -= 20.0
            done = True
            self.collision = None

        return reward, done

    def _get_distance_to_target(self):
        location = self.vehicle.get_location()
        return np.linalg.norm([
            location.x - self.target_location.x,
            location.y - self.target_location.y
        ])

    def _setup_vehicle_and_sensors(self):
        spawn_points = self.world.get_map().get_spawn_points()
        while True:
            start_point, end_point = np.random.choice(spawn_points, 2, replace=False)
            if start_point.location != end_point.location:
                break
        self.target_location = end_point.location

        vehicle_bp = self.blueprint_library.find('vehicle.tesla.model3')
        self.vehicle = self.world.try_spawn_actor(vehicle_bp, start_point)
        if not self.vehicle:
            vehicle_bp = np.random.choice(self.blueprint_library.filter('vehicle.*'))
            self.vehicle = self.world.spawn_actor(vehicle_bp, start_point)
        self.actors.append(self.vehicle)

        camera_bp = self.blueprint_library.find('sensor.camera.rgb')
        camera_bp.set_attribute('image_size_x', str(self.size[0]))
        camera_bp.set_attribute('image_size_y', str(self.size[1]))
        camera_transform = carla.Transform(carla.Location(x=1.5, z=2.4))
        self.camera = self.world.spawn_actor(camera_bp, camera_transform, attach_to=self.vehicle)
        self.camera.listen(lambda image: self._on_image(image))
        self.actors.append(self.camera)

        collision_bp = self.blueprint_library.find('sensor.other.collision')
        self.collision_sensor = self.world.spawn_actor(collision_bp, carla.Transform(), attach_to=self.vehicle)
        self.collision_sensor.listen(lambda event: self._on_collision(event))
        self.actors.append(self.collision_sensor)

    def _on_image(self, image):
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))[:, :, :3]
        self.image = array

    def _on_collision(self, event):
        self.collision = event

    def _cleanup(self):
        if self.camera and self.camera.is_listening:
            self.camera.stop()
        if hasattr(self, 'collision_sensor') and self.collision_sensor.is_listening:
            self.collision_sensor.stop()
        for actor in self.actors:
            if actor and actor.is_alive:
                actor.destroy()
        self.actors = []
        self.vehicle = None
        self.camera = None
        self.collision_sensor = None
        self.image = None
        self.collision = None

    def close(self):
        self._cleanup()
        self.settings.synchronous_mode = False
        self.settings.fixed_delta_seconds = None
        self.world.apply_settings(self.settings)
