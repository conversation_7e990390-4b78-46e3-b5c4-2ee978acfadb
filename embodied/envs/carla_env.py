import numpy as np
import embodied
from elements import Space
import carla
import math
import random
import time

class CarlaEnv(embodied.Env):
    """
    基于gym-carla优秀设计的点到点导航环境
    支持多模态观察、路径规划和丰富的奖励函数
    """
    def __init__(self, task=None, size=(84, 84), host='localhost', port=2000, max_steps=1000,
                 desired_speed=8.0, max_ego_spawn_times=200, **kwargs):
        # 连接CARLA服务器
        print('正在连接到CARLA服务器...')
        self.client = carla.Client(host, port)
        self.client.set_timeout(10.0)
        self.world = self.client.get_world()
        print('CARLA服务器连接成功!')

        # 环境参数
        self.size = size
        self.max_steps = max_steps
        self.desired_speed = desired_speed  # 期望速度 (m/s)
        self.max_ego_spawn_times = max_ego_spawn_times
        self.dt = 0.05  # 仿真时间步长

        # 设置同步模式
        self.original_settings = self.world.get_settings()
        self.settings = self.world.get_settings()
        self.settings.synchronous_mode = True
        self.settings.fixed_delta_seconds = self.dt
        self.world.apply_settings(self.settings)

        # 设置天气
        self.world.set_weather(carla.WeatherParameters.ClearNoon)

        # 获取蓝图库和生成点
        self.blueprint_library = self.world.get_blueprint_library()
        self.vehicle_spawn_points = list(self.world.get_map().get_spawn_points())

        # 创建自车蓝图 (优先使用Tesla，如果没有则使用其他车辆)
        self.ego_bp = self._create_vehicle_blueprint('vehicle.tesla.model3', color='49,8,8')

        # 车辆和传感器
        self.actors = []
        self.ego = None
        self.camera_sensor = None
        self.collision_sensor = None

        # 传感器数据
        self.camera_img = np.zeros((self.size[0], self.size[1], 3), dtype=np.uint8)
        self.collision_hist = []
        self.collision_hist_l = 1  # 碰撞历史长度

        # 状态变量
        self.step_count = 0
        self.reset_step = 0
        self.total_step = 0
        self.target_location = None
        self.start_location = None
        self.initial_distance = 0
        self._is_first = True

        # 路径规划相关
        self.waypoints = []
        self.route_planner = None

        # 性能统计
        self.success_count = 0
        self.total_episodes = 0

    @property
    def obs_space(self):
        return {
            'image': Space(np.uint8, self.size + (3,)),     # RGB相机图像
            'speed': Space(np.float32, (1,)),               # 当前速度
            'distance_to_target': Space(np.float32, (1,)),  # 到目标的距离
            'target_direction': Space(np.float32, (2,)),    # 目标方向向量(x,y)
            'vehicle_heading': Space(np.float32, (1,)),     # 车辆朝向角度
            'lateral_distance': Space(np.float32, (1,)),    # 横向偏离距离
            'delta_yaw': Space(np.float32, (1,)),           # 朝向偏差角度
            'progress': Space(np.float32, (1,)),            # 任务完成进度
            'reward': Space(np.float32),
            'is_first': Space(bool),
            'is_last': Space(bool),
            'is_terminal': Space(bool),
        }

    @property
    def act_space(self):
        return {
            'acceleration': Space(np.float32, (), -1, 1),   # 标准化的加速度 [-1, 1]
            'steer': Space(np.float32, (), -1, 1),          # 转向角度 [-1, 1]
            'reset': Space(bool),
        }

    def _create_vehicle_blueprint(self, actor_filter, color=None):
        """创建车辆蓝图"""
        blueprints = self.blueprint_library.filter(actor_filter)

        # 如果指定的车辆不存在，使用所有车辆
        if len(blueprints) == 0:
            print(f"警告: 未找到 {actor_filter}，使用默认车辆")
            blueprints = self.blueprint_library.filter('vehicle.*')

        # 筛选四轮车辆
        blueprint_library = []
        for x in blueprints:
            if x.has_attribute('number_of_wheels'):
                if int(x.get_attribute('number_of_wheels')) == 4:
                    blueprint_library.append(x)

        # 如果没有四轮车辆，使用所有车辆
        if len(blueprint_library) == 0:
            print("警告: 未找到四轮车辆，使用所有可用车辆")
            blueprint_library = list(blueprints)

        # 如果仍然没有车辆，抛出错误
        if len(blueprint_library) == 0:
            raise RuntimeError("CARLA服务器中没有可用的车辆蓝图")

        bp = random.choice(blueprint_library)
        print(f"选择车辆: {bp.id}")

        if bp.has_attribute('color'):
            if not color:
                color = random.choice(bp.get_attribute('color').recommended_values)
            bp.set_attribute('color', color)
        return bp

    def _to_float32(self, x):
        """确保数据类型为float32"""
        if isinstance(x, dict):
            return {k: self._to_float32(v) for k, v in x.items()}
        elif isinstance(x, np.ndarray) and x.dtype == np.float64:
            return x.astype(np.float32)
        elif isinstance(x, float) or (isinstance(x, np.floating) and getattr(x, 'dtype', None) == np.float64):
            return np.float32(x)
        else:
            return x

    def _get_pos(self, vehicle):
        """获取车辆位置"""
        trans = vehicle.get_transform()
        return trans.location.x, trans.location.y

    def _get_speed(self, vehicle):
        """计算车辆速度 (m/s)"""
        vel = vehicle.get_velocity()
        return math.sqrt(vel.x ** 2 + vel.y ** 2 + vel.z ** 2)

    def _set_synchronous_mode(self, synchronous=True):
        """设置同步模式"""
        self.settings.synchronous_mode = synchronous
        self.world.apply_settings(self.settings)

    def step(self, action):
        action = self._to_float32(action)

        # 处理重置
        if self._is_first or action.get('reset', False):
            return self._reset()

        # 解析动作（输入范围是[-1, 1]）
        acceleration_norm = np.clip(action['acceleration'], -1.0, 1.0)
        steer_norm = np.clip(action['steer'], -1.0, 1.0)

        # 将标准化的加速度转换为实际加速度 [-1, 1] -> [-3, 3] m/s²
        acceleration = acceleration_norm * 3.0
        steer = steer_norm

        # 将加速度转换为油门和刹车
        if acceleration > 0:
            throttle = np.clip(acceleration / 3.0, 0, 1)
            brake = 0
        else:
            throttle = 0
            brake = np.clip(-acceleration / 8.0, 0, 1)

        # 应用控制
        control = carla.VehicleControl(
            throttle=float(throttle),
            steer=float(-steer),  # CARLA使用左手坐标系
            brake=float(brake)
        )
        self.ego.apply_control(control)

        # 推进仿真
        self.world.tick()
        self.step_count += 1
        self.total_step += 1

        # 更新路径点（如果有路径规划器）
        if self.route_planner:
            self.waypoints, _, _ = self.route_planner.run_step()

        # 获取观察、奖励和终止条件
        obs = self._get_obs()
        reward, is_terminal = self._get_reward_and_done()
        is_last = self.step_count >= self.max_steps or is_terminal

        obs.update({
            'reward': np.float32(reward),
            'is_first': False,
            'is_last': is_last,
            'is_terminal': is_terminal,
        })
        return obs

    def _reset(self):
        """重置环境"""
        # 清理现有的actors
        self._clear_all_actors()

        # 禁用同步模式进行重置
        self._set_synchronous_mode(False)

        # 生成自车
        ego_spawn_times = 0
        while True:
            if ego_spawn_times > self.max_ego_spawn_times:
                raise RuntimeError("无法生成自车，请检查CARLA服务器状态")

            # 随机选择起点和终点
            start_point, end_point = self._select_spawn_points()

            if self._try_spawn_ego_vehicle_at(start_point):
                self.target_location = end_point.location
                self.start_location = start_point.location
                self.initial_distance = self._get_distance_to_target()
                break
            else:
                ego_spawn_times += 1
                time.sleep(0.1)

        # 设置传感器
        self._setup_sensors()

        # 初始化路径规划器
        self._setup_route_planner()

        # 重置状态
        self.step_count = 0
        self.reset_step += 1
        self.total_episodes += 1
        self._is_first = False
        self.collision_hist = []

        # 启用同步模式
        self._set_synchronous_mode(True)

        # 推进一步以获取初始观察
        self.world.tick()

        obs = self._get_obs()
        obs.update({
            'reward': np.float32(0.0),
            'is_first': True,
            'is_last': False,
            'is_terminal': False,
        })

        print(f"Episode {self.total_episodes}: 起点({self.start_location.x:.1f}, {self.start_location.y:.1f}) -> "
              f"终点({self.target_location.x:.1f}, {self.target_location.y:.1f}), "
              f"距离: {self.initial_distance:.1f}m")

        return obs

    def _select_spawn_points(self):
        """选择起点和终点"""
        if len(self.vehicle_spawn_points) < 2:
            raise RuntimeError(f"生成点不足，只有 {len(self.vehicle_spawn_points)} 个生成点")

        max_attempts = 100
        for _ in range(max_attempts):
            start_point, end_point = random.sample(self.vehicle_spawn_points, 2)
            distance = math.sqrt(
                (start_point.location.x - end_point.location.x)**2 +
                (start_point.location.y - end_point.location.y)**2
            )
            # 确保起点和终点距离合适
            if 50.0 <= distance <= 200.0:
                return start_point, end_point

        # 如果找不到合适距离的点，放宽条件
        print("警告: 未找到理想距离的生成点，使用任意两个不同的点")
        start_point, end_point = random.sample(self.vehicle_spawn_points, 2)
        return start_point, end_point

    def _try_spawn_ego_vehicle_at(self, transform):
        """尝试在指定位置生成自车"""
        vehicle = self.world.try_spawn_actor(self.ego_bp, transform)
        if vehicle is not None:
            self.ego = vehicle
            self.actors.append(vehicle)
            print(f"成功生成自车在位置: ({transform.location.x:.1f}, {transform.location.y:.1f})")
            return True
        else:
            print(f"无法在位置 ({transform.location.x:.1f}, {transform.location.y:.1f}) 生成车辆")
            return False

    def _setup_sensors(self):
        """设置传感器"""
        # RGB相机
        camera_bp = self.blueprint_library.find('sensor.camera.rgb')
        camera_bp.set_attribute('image_size_x', str(self.size[0]))
        camera_bp.set_attribute('image_size_y', str(self.size[1]))
        camera_bp.set_attribute('fov', '110')
        camera_bp.set_attribute('sensor_tick', '0.02')

        camera_transform = carla.Transform(carla.Location(x=0.8, z=1.7))
        self.camera_sensor = self.world.spawn_actor(camera_bp, camera_transform, attach_to=self.ego)
        self.camera_sensor.listen(lambda image: self._on_camera_image(image))
        self.actors.append(self.camera_sensor)

        # 碰撞传感器
        collision_bp = self.blueprint_library.find('sensor.other.collision')
        self.collision_sensor = self.world.spawn_actor(collision_bp, carla.Transform(), attach_to=self.ego)
        self.collision_sensor.listen(lambda event: self._on_collision(event))
        self.actors.append(self.collision_sensor)

    def _setup_route_planner(self):
        """设置路径规划器（简化版本）"""
        # 这里可以添加更复杂的路径规划逻辑
        # 目前使用简单的直线路径
        self.waypoints = []
        current_loc = self.ego.get_location()
        target_loc = self.target_location

        # 生成简单的路径点
        num_waypoints = 10
        for i in range(num_waypoints + 1):
            t = i / num_waypoints
            x = current_loc.x + t * (target_loc.x - current_loc.x)
            y = current_loc.y + t * (target_loc.y - current_loc.y)
            # 简化的路径点格式 [x, y, yaw]
            yaw = math.atan2(target_loc.y - current_loc.y, target_loc.x - current_loc.x)
            self.waypoints.append([x, y, math.degrees(yaw)])

    def _clear_all_actors(self):
        """清理所有actors"""
        for actor in self.actors:
            if actor and actor.is_alive:
                if hasattr(actor, 'is_listening') and actor.is_listening:
                    actor.stop()
                actor.destroy()
        self.actors = []
        self.ego = None
        self.camera_sensor = None
        self.collision_sensor = None

    def _on_camera_image(self, image):
        """相机图像回调"""
        array = np.frombuffer(image.raw_data, dtype=np.uint8)
        array = array.reshape((image.height, image.width, 4))[:, :, :3]
        array = array[:, :, ::-1]  # BGR to RGB
        self.camera_img = array

    def _on_collision(self, event):
        """碰撞回调"""
        impulse = event.normal_impulse
        intensity = math.sqrt(impulse.x**2 + impulse.y**2 + impulse.z**2)
        self.collision_hist.append(intensity)
        if len(self.collision_hist) > self.collision_hist_l:
            self.collision_hist.pop(0)

    def _get_obs(self):
        """获取观察"""
        # 等待相机图像
        while self.camera_img is None:
            self.world.tick()

        # 获取车辆状态
        velocity = self.ego.get_velocity()
        speed = math.sqrt(velocity.x**2 + velocity.y**2 + velocity.z**2)

        # 获取位置和朝向
        transform = self.ego.get_transform()
        rotation = transform.rotation

        # 计算到目标的距离和方向
        distance_to_target = self._get_distance_to_target()
        target_direction = self._get_target_direction()

        # 计算横向偏离和朝向偏差（基于路径点）
        lateral_distance, delta_yaw = self._get_lane_info()

        # 计算任务进度
        progress = max(0.0, 1.0 - distance_to_target / self.initial_distance) if self.initial_distance > 0 else 0.0

        obs = {
            'image': self.camera_img.copy(),
            'speed': np.array([speed], dtype=np.float32),
            'distance_to_target': np.array([distance_to_target], dtype=np.float32),
            'target_direction': target_direction.astype(np.float32),
            'vehicle_heading': np.array([math.radians(rotation.yaw)], dtype=np.float32),
            'lateral_distance': np.array([lateral_distance], dtype=np.float32),
            'delta_yaw': np.array([delta_yaw], dtype=np.float32),
            'progress': np.array([progress], dtype=np.float32),
        }
        return obs

    def _get_distance_to_target(self):
        """计算到目标的距离"""
        location = self.ego.get_location()
        return math.sqrt(
            (location.x - self.target_location.x)**2 +
            (location.y - self.target_location.y)**2
        )

    def _get_target_direction(self):
        """获取从当前位置到目标的单位方向向量"""
        location = self.ego.get_location()
        dx = self.target_location.x - location.x
        dy = self.target_location.y - location.y
        distance = math.sqrt(dx**2 + dy**2)
        if distance > 0:
            return np.array([dx / distance, dy / distance])
        else:
            return np.array([0.0, 0.0])

    def _get_lane_info(self):
        """获取车道信息（横向偏离和朝向偏差）"""
        if not self.waypoints:
            return 0.0, 0.0

        ego_x, ego_y = self._get_pos(self.ego)
        ego_yaw = math.radians(self.ego.get_transform().rotation.yaw)

        # 找到最近的路径点
        min_dist = float('inf')
        closest_waypoint = self.waypoints[0]
        for waypoint in self.waypoints:
            dist = math.sqrt((ego_x - waypoint[0])**2 + (ego_y - waypoint[1])**2)
            if dist < min_dist:
                min_dist = dist
                closest_waypoint = waypoint

        # 计算横向偏离
        waypoint_x, waypoint_y, waypoint_yaw = closest_waypoint
        waypoint_yaw_rad = math.radians(waypoint_yaw)

        # 车辆到路径点的向量
        vec = np.array([ego_x - waypoint_x, ego_y - waypoint_y])
        # 路径点的方向向量
        w = np.array([math.cos(waypoint_yaw_rad), math.sin(waypoint_yaw_rad)])

        # 横向偏离（叉积）
        lateral_distance = np.cross(w, vec)

        # 朝向偏差
        delta_yaw = ego_yaw - waypoint_yaw_rad
        # 归一化到[-pi, pi]
        while delta_yaw > math.pi:
            delta_yaw -= 2 * math.pi
        while delta_yaw < -math.pi:
            delta_yaw += 2 * math.pi

        return lateral_distance, delta_yaw

    def _get_reward_and_done(self):
        """计算奖励和终止条件"""
        # 获取当前状态
        speed = self._get_speed(self.ego)
        distance_to_target = self._get_distance_to_target()
        lateral_distance, delta_yaw = self._get_lane_info()

        # 初始化奖励
        reward = 0.0
        done = False

        # 1. 速度跟踪奖励
        speed_reward = -abs(speed - self.desired_speed) * 0.1

        # 2. 距离进步奖励
        if hasattr(self, 'last_distance'):
            distance_reward = (self.last_distance - distance_to_target) * 1.0
        else:
            distance_reward = 0.0
        self.last_distance = distance_to_target

        # 3. 方向奖励（朝向目标）
        target_direction = self._get_target_direction()
        ego_yaw = math.radians(self.ego.get_transform().rotation.yaw)
        ego_direction = np.array([math.cos(ego_yaw), math.sin(ego_yaw)])
        direction_alignment = np.dot(ego_direction, target_direction)
        direction_reward = max(0.0, direction_alignment) * 0.5

        # 4. 横向偏离惩罚
        lateral_penalty = -abs(lateral_distance) * 0.1

        # 5. 朝向偏差惩罚
        yaw_penalty = -abs(delta_yaw) * 0.1

        # 6. 时间惩罚（鼓励快速完成）
        time_penalty = -0.01

        # 7. 碰撞检测
        collision_penalty = 0.0
        if len(self.collision_hist) > 0:
            collision_penalty = -100.0
            done = True
            print(f"Episode {self.total_episodes}: 发生碰撞！")

        # 8. 成功到达目标
        success_reward = 0.0
        if distance_to_target < 5.0:
            success_reward = 200.0
            done = True
            self.success_count += 1
            print(f"Episode {self.total_episodes}: 成功到达目标！距离: {distance_to_target:.2f}m, "
                  f"成功率: {self.success_count}/{self.total_episodes} ({100*self.success_count/self.total_episodes:.1f}%)")

        # 9. 偏离太远
        if distance_to_target > self.initial_distance * 2.0:
            reward += -50.0
            done = True
            print(f"Episode {self.total_episodes}: 偏离目标太远！当前距离: {distance_to_target:.2f}m")

        # 10. 超时
        if self.step_count >= self.max_steps:
            done = True
            print(f"Episode {self.total_episodes}: 超时！最终距离: {distance_to_target:.2f}m")

        # 总奖励
        reward = (speed_reward + distance_reward + direction_reward +
                 lateral_penalty + yaw_penalty + time_penalty +
                 collision_penalty + success_reward)

        return reward, done

    def close(self):
        """关闭环境"""
        print("正在关闭CARLA环境...")

        # 清理所有actors
        self._clear_all_actors()

        # 恢复原始设置
        if hasattr(self, 'original_settings'):
            self.world.apply_settings(self.original_settings)

        print("CARLA环境已关闭")

    def __del__(self):
        """析构函数"""
        try:
            self.close()
        except:
            pass


