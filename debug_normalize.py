#!/usr/bin/env python3
"""
调试NormalizeAction包装器的行为
"""

import numpy as np
import embodied
from embodied.envs.carla_env import CarlaEnv

def test_normalize_action():
    """测试NormalizeAction包装器"""
    print("测试NormalizeAction包装器...")
    
    try:
        # 创建环境
        env = CarlaEnv(size=(64, 64), max_steps=10)
        print(f"原始动作空间: {env.act_space}")
        
        # 应用NormalizeAction包装器
        normalize_wrapper = embodied.wrappers.NormalizeAction(env, 'acceleration')
        print(f"NormalizeAction._low: {normalize_wrapper._low}")
        print(f"NormalizeAction._high: {normalize_wrapper._high}")
        print(f"NormalizeAction._mask: {normalize_wrapper._mask}")
        print(f"标准化后动作空间: {normalize_wrapper.act_space}")
        
        # 测试一些动作值
        test_values = [-1.0, -0.5, 0.0, 0.5, 1.0, 1.1, -1.1]
        
        for value in test_values:
            # 模拟NormalizeAction的转换
            orig = (value + 1) / 2 * (normalize_wrapper._high - normalize_wrapper._low) + normalize_wrapper._low
            print(f"输入: {value} -> 输出: {orig}")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("NormalizeAction调试")
    print("=" * 50)
    test_normalize_action()
