# CARLA环境改进总结

## 概述

基于gym-carla优秀设计，我们对DreamerV3项目中的CARLA环境进行了全面改进，使其更适合点到点导航任务的强化学习训练。

## 主要改进

### 1. 动作空间优化
- **原始设计**: 分离的油门、刹车、转向控制
- **改进后**: 统一的加速度控制 + 转向控制
  - `acceleration`: [-3, 3] m/s² (自动转换为油门/刹车)
  - `steer`: [-1, 1] 转向角度

### 2. 观察空间增强
新增了导航相关的关键观察信息：
- `distance_to_target`: 到目标的距离
- `target_direction`: 目标方向向量 (x, y)
- `vehicle_heading`: 车辆朝向角度
- `lateral_distance`: 横向偏离距离
- `delta_yaw`: 朝向偏差角度
- `progress`: 任务完成进度

### 3. 奖励函数重设计
采用多维度奖励机制：
- **速度跟踪奖励**: 鼓励维持期望速度 (8 m/s)
- **距离进步奖励**: 奖励接近目标的行为
- **方向奖励**: 奖励朝向目标的正确方向
- **横向偏离惩罚**: 惩罚偏离路径
- **朝向偏差惩罚**: 惩罚错误的朝向
- **碰撞惩罚**: 严重惩罚碰撞 (-100)
- **成功奖励**: 到达目标的大奖励 (+200)

### 4. 环境管理改进
- **智能生成点选择**: 确保起点和终点距离合适 (50-200m)
- **同步模式管理**: 更好的同步模式控制
- **传感器优化**: 改进的相机和碰撞传感器设置
- **资源清理**: 完善的actor清理机制

### 5. 路径规划集成
- 简化的路径规划器实现
- 基于路径点的横向偏离计算
- 为未来集成复杂路径规划器预留接口

## 技术特点

### 借鉴gym-carla的优秀设计
1. **多模态传感器**: RGB相机 + 碰撞检测
2. **车辆状态监控**: 速度、位置、朝向
3. **环境同步**: 确定性仿真行为
4. **性能统计**: 成功率跟踪

### 针对DreamerV3的优化
1. **观察空间兼容**: 符合DreamerV3的输入要求
2. **奖励信号设计**: 提供丰富的学习信号
3. **终止条件**: 明确的成功/失败判断

## 使用方法

### 基本使用
```python
from embodied.envs.carla_env import CarlaEnv

# 创建环境
env = CarlaEnv(
    size=(84, 84),          # 图像尺寸
    max_steps=1000,         # 最大步数
    desired_speed=8.0,      # 期望速度 (m/s)
    host='localhost',       # CARLA服务器地址
    port=2000              # CARLA服务器端口
)

# 重置环境
obs = env._reset()

# 执行动作
action = {
    'acceleration': 1.0,    # 加速度
    'steer': 0.1,          # 转向
    'reset': False
}
obs = env.step(action)
```

### 测试环境
运行测试脚本验证环境：
```bash
python test_carla_env.py
```

## 配置要求

### CARLA服务器
- CARLA 0.9.x
- 运行命令: `./CarlaUE4.sh`
- 默认端口: 2000

### Python依赖
- carla (CARLA Python API)
- numpy
- 其他DreamerV3依赖

## 性能指标

### 观察空间
- `image`: (84, 84, 3) RGB图像
- `speed`: (1,) 当前速度
- `distance_to_target`: (1,) 目标距离
- `target_direction`: (2,) 目标方向
- `vehicle_heading`: (1,) 车辆朝向
- `lateral_distance`: (1,) 横向偏离
- `delta_yaw`: (1,) 朝向偏差
- `progress`: (1,) 完成进度

### 动作空间
- `acceleration`: 连续值 [-3, 3]
- `steer`: 连续值 [-1, 1]

## 下一步计划

1. **集成复杂路径规划**: 使用CARLA的全局路径规划器
2. **多车交互**: 添加其他车辆和行人
3. **天气变化**: 支持不同天气条件
4. **传感器扩展**: 添加激光雷达、鸟瞰图等
5. **性能优化**: 提高仿真效率

## 总结

改进后的CARLA环境为DreamerV3提供了：
- 丰富的导航相关观察信息
- 合理的奖励信号设计
- 稳定的仿真环境
- 良好的扩展性

这些改进使得环境更适合点到点导航任务的强化学习训练，为实现自动驾驶智能体提供了坚实的基础。
