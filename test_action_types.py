#!/usr/bin/env python3
"""
测试动作类型问题
"""

import numpy as np
from embodied.envs.carla_env import CarlaEnv
import embodied

def test_action_types():
    """测试动作类型转换"""
    print("测试动作类型转换...")
    
    try:
        # 创建环境
        env = CarlaEnv(size=(64, 64), max_steps=10)
        
        # 应用DreamerV3的包装器
        print(f"原始动作空间: {env.act_space}")

        for name, space in env.act_space.items():
            if not space.discrete:
                print(f"应用NormalizeAction到 {name}")
                env = embodied.wrappers.NormalizeAction(env, name)
        print(f"标准化后动作空间: {env.act_space}")

        print("应用UnifyDtypes...")
        unify_wrapper = embodied.wrappers.UnifyDtypes(env)
        print(f"UnifyDtypes._act_inner: {unify_wrapper._act_inner}")
        env = unify_wrapper
        print(f"统一类型后动作空间: {env.act_space}")

        print("应用CheckSpaces...")
        env = embodied.wrappers.CheckSpaces(env)

        for name, space in env.act_space.items():
            if not space.discrete:
                print(f"应用ClipAction到 {name}")
                env = embodied.wrappers.ClipAction(env, name)
        
        print("✓ 包装器应用成功")
        print(f"最终动作空间: {env.act_space}")
        
        # 重置环境 - 需要提供所有动作键
        reset_action = {'acceleration': np.float32(0.0), 'steer': np.float32(0.0), 'reset': True}
        obs = env.step(reset_action)
        print("✓ 环境重置成功")
        
        # 测试不同类型的动作
        test_actions = [
            # float32类型
            {'acceleration': np.float32(0.5), 'steer': np.float32(0.1), 'reset': False},
            # float64类型
            {'acceleration': np.float64(0.5), 'steer': np.float64(0.1), 'reset': False},
            # Python float类型
            {'acceleration': 0.5, 'steer': 0.1, 'reset': False},
            # numpy数组
            {'acceleration': np.array(0.5), 'steer': np.array(0.1), 'reset': False},
        ]
        
        for i, action in enumerate(test_actions):
            try:
                print(f"\n测试动作 {i+1}:")
                print(f"  acceleration类型: {type(action['acceleration'])}")
                print(f"  steer类型: {type(action['steer'])}")
                
                obs = env.step(action)
                print(f"  ✓ 动作执行成功")
                
            except Exception as e:
                print(f"  ❌ 动作执行失败: {e}")
        
        env.close()
        print("\n🎉 动作类型测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("动作类型测试")
    print("=" * 50)
    test_action_types()
