#!/usr/bin/env python3
"""
测试改进后的CARLA环境
确保环境能够正常工作并提供合理的观察和奖励
"""

import numpy as np
import sys
import os

# 添加项目路径
sys.path.append('/home/<USER>/dreamerv3-main')

from embodied.envs.carla_env import CarlaEnv

def test_carla_env():
    """测试CARLA环境的基本功能"""
    print("开始测试CARLA环境...")
    
    try:
        # 创建环境
        env = CarlaEnv(
            size=(64, 64),
            host='localhost',
            port=2000,
            max_steps=100,
            desired_speed=8.0
        )
        print("✓ 环境创建成功")
        
        # 测试观察空间
        obs_space = env.obs_space
        print(f"✓ 观察空间: {list(obs_space.keys())}")
        
        # 测试动作空间
        act_space = env.act_space
        print(f"✓ 动作空间: {list(act_space.keys())}")
        
        # 重置环境
        print("重置环境...")
        obs = env._reset()
        print(f"✓ 重置成功，观察形状:")
        for key, value in obs.items():
            if hasattr(value, 'shape'):
                print(f"  {key}: {value.shape}")
            else:
                print(f"  {key}: {value}")
        
        # 运行几步
        print("运行测试步骤...")
        for step in range(5):
            # 随机动作
            action = {
                'acceleration': np.random.uniform(-1, 1),
                'steer': np.random.uniform(-0.5, 0.5),
                'reset': False
            }
            
            obs = env.step(action)
            reward = obs['reward']
            is_terminal = obs['is_terminal']
            
            print(f"  步骤 {step+1}: 奖励={reward:.3f}, 终止={is_terminal}")
            
            if is_terminal:
                print("  环境提前终止")
                break
        
        print("✓ 步骤执行成功")
        
        # 关闭环境
        env.close()
        print("✓ 环境关闭成功")
        
        print("\n🎉 所有测试通过！CARLA环境工作正常。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_observation_consistency():
    """测试观察的一致性"""
    print("\n测试观察一致性...")
    
    try:
        env = CarlaEnv(size=(64, 64), max_steps=50)
        
        # 重置并获取多个观察
        obs1 = env._reset()
        
        # 执行一个动作
        action = {'acceleration': 0.5, 'steer': 0.0, 'reset': False}
        obs2 = env.step(action)
        
        # 检查观察的形状和类型
        for key in obs1.keys():
            if key in obs2:
                val1, val2 = obs1[key], obs2[key]
                if hasattr(val1, 'shape') and hasattr(val2, 'shape'):
                    assert val1.shape == val2.shape, f"形状不一致: {key}"
                    assert val1.dtype == val2.dtype, f"类型不一致: {key}"
        
        env.close()
        print("✓ 观察一致性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 观察一致性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("CARLA环境测试")
    print("=" * 50)
    
    # 检查CARLA服务器是否运行
    print("请确保CARLA服务器正在运行 (./CarlaUE4.sh)")
    input("按Enter键继续...")
    
    # 运行测试
    success1 = test_carla_env()
    success2 = test_observation_consistency()
    
    if success1 and success2:
        print("\n🎉 所有测试都通过了！")
        print("CARLA环境已经准备好用于DreamerV3训练。")
    else:
        print("\n❌ 部分测试失败，请检查CARLA服务器和环境配置。")
