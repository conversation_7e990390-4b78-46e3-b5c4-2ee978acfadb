#!/usr/bin/env python3
"""
调试UnifyDtypes包装器的行为
"""

import numpy as np
import embodied
from embodied.envs.carla_env import CarlaEnv

def test_unify_dtypes():
    """测试UnifyDtypes包装器"""
    print("测试UnifyDtypes包装器...")
    
    try:
        # 创建环境
        env = CarlaEnv(size=(64, 64), max_steps=10)
        print(f"原始动作空间: {env.act_space}")
        
        # 应用UnifyDtypes包装器
        unify_wrapper = embodied.wrappers.UnifyDtypes(env)
        print(f"UnifyDtypes._act_inner: {unify_wrapper._act_inner}")
        print(f"统一类型后动作空间: {unify_wrapper.act_space}")
        
        # 测试不同类型的动作
        test_actions = [
            # float64类型 - 这是问题所在
            {'acceleration': np.float64(1.267778754234314), 'steer': np.float64(1.267778754234314), 'reset': False},
            # Python float类型
            {'acceleration': 1.267778754234314, 'steer': 1.267778754234314, 'reset': False},
        ]
        
        for i, action in enumerate(test_actions):
            print(f"\n测试动作 {i+1}:")
            print(f"  原始动作类型:")
            for key, value in action.items():
                if key != 'reset':
                    print(f"    {key}: {type(value)}, dtype: {getattr(value, 'dtype', 'N/A')}")
            
            # 模拟UnifyDtypes的转换
            converted_action = action.copy()
            for key, dtype in unify_wrapper._act_inner.items():
                if key in converted_action:
                    converted_action[key] = np.asarray(converted_action[key], dtype)
            
            print(f"  转换后动作类型:")
            for key, value in converted_action.items():
                if key != 'reset':
                    print(f"    {key}: {type(value)}, dtype: {getattr(value, 'dtype', 'N/A')}, value: {value}")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("UnifyDtypes调试")
    print("=" * 50)
    test_unify_dtypes()
